const { hooks } = require('@adonisjs/ignitor')

hooks.after.providersBooted(() => {
  const Exception = use('Exception')

  Exception.handle('InvalidSessionException', async (error, { response, session }) => {
    return response.redirect('/login')
  })

  Exception.handle('HttpException', async (error, { response, session }) => {
    return response.redirect('/')
  })

  //benim e<PERSON>lerim
  const Env = use('Env')
  const View = use('View')

  View.global('APP_URL', function () {
    return Env.get('APP_URL')
  })

})