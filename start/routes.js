/* eslint-disable no-undef */
'use strict'

/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| Http routes are entry points to your web application. You can create
| routes for different URL's and bind Controller actions to them.
|
| A complete guide on routing is available here.
| http://adonisjs.com/docs/4.1/routing
|
*/

/** @type {typeof import('@adonisjs/framework/src/Route/Manager')} */
const Route = use('Route')

//Route.on('/').render("terminal")

/*
Route.get('/', ({ session, response }) => {
    if (session.get('login') == 0) {
        response.redirect('/login', { login: 0 })
    }else{
        response.redirect('/terminals', { login: 1 })
    }
})
*/

//CORE
Route.group(() => {
    Route.get('/invitation', 'CoreController.invitationIndex').as('invitation')
    Route.post('/invitation', 'CoreController.invitationCheck').as('checkInvitation')
}).middleware(['csrf'])

//CORE from Out
Route.group(() => {
    //Route.post('/winnerPhone', 'CoreController.addWinnerPhoneSendSms').as('addWinnerPhoneSendSms')
    Route.post('/winnerPhone', 'CoreController.addWinnerPhoneSendSmsJson').as('addWinnerPhoneSendSms')
}).middleware(['noCsrf'])

//API Get
Route.group(() => {
    Route.get('/customer/:id', 'ApiController.checkCustomer').as('apiCheckCustomer')
    Route.get('/customer/login/:id', 'ApiController.checkLoginCustomer').as('apiCheckLoginCustomer')
}).middleware(['csrf']).prefix('api/v1')

//API JSON from Out
Route.group(() => {
    Route.post('/customer/:id', 'ApiController.updateCustomer').as('apiUpdateCustomer')
}).middleware(['noCsrf']).prefix('api/v1').formats(['json', 'html'])

//MOBILE-APP-WEBSITE
Route.group(() => {
    Route.get('/bill/:id', 'MobileController.kycUtilityBill').as('kycUtilityBill')
    Route.post('/bill', 'MobileController.kycUtilityBillUpload').as('kycUtilityBillUpload')

    Route.get('/:id', 'MobileController.kycWelcome').as('kycWelcome')
    Route.post('/instruction', 'MobileController.kycInstruction').as('kycInstruction')
    Route.post('/photo', 'MobileController.kycTakePhoto').as('kycPhoto')
    Route.post('/uploadPhoto', 'MobileController.kycUploadPhoto').as('kycUploadPhoto')
    Route.post('/photoFront', 'MobileController.kycPhotoFront').as('kycPhotoFront')
    Route.post('/photoBack', 'MobileController.kycPhotoBack').as('kycPhotoBack')
    Route.post('/photoSelfie', 'MobileController.kycPhotoSelfie').as('kycPhotoSelfie')
    Route.post('/check', 'MobileController.kycCheck').as('kycCheck')
    Route.post('/selfieID', 'MobileController.kycSelfieID').as('kycSelfieID')
    Route.post('/signature', 'MobileController.kycSignature').as('kycSignature')
    Route.post('/signatureOk', 'MobileController.kycSignatureOk').as('kycSignatureOk')

    Route.post('/uploadOk', 'MobileController.kycUploadOk').as('kycUploadOk')
    Route.post('/dirtyMoney', 'MobileController.kycDirtyMoney').as('kycDirtyMoney')
    Route.post('/question', 'MobileController.kycQuestion').as('kycQuestion')
    Route.get('/question/:login', 'MobileController.kycQuestion').as('kycQuestion')
    Route.post('/finish', 'MobileController.kycFinish').as('kycFinish')
    Route.post('/process', 'MobileController.kycProcess').as('kycProcess')
    Route.post('/camera', 'MobileController.kycCamera').as('kycCamera')
}).middleware(['csrf']).prefix('kyc')

//PANEL with GUEST
Route.group(() => {
    Route.get('/login', 'LoginController.index').as('login')
    Route.post('/login', 'LoginController.check').as('checkLogin')
}).middleware(['guest', 'csrf'])

Route.get('/', 'HomeController.index')
Route.get('/home', 'HomeController.index').as('home')
Route.get('/qrcodes/:id', 'HomeController.qrcodes').as('qrcodes')
Route.get('/bfiMap', 'HomeController.bfiMap').as('bfiMap')

//COMMON with AUTH
Route.group(() => {
    Route.get('/logout', 'LoginController.destroy').as('logout')
    Route.get('/profile', 'ProfileController.index').as('getProfile')
    Route.post('/profile', 'ProfileController.update').as('updateProfile').validator('UserUpdate')
}).middleware(['auth', 'csrf'])

//PANEL with AUTH
Route.group(() => {
    Route.get('/register', 'RegisterController.index').as('register')
    Route.post('/register', 'RegisterController.create').as('createUser').validator('Register')

    Route.get('/users', 'UserController.index').as('getUsers')
    Route.patch('/users/:id', 'UserController.update').as('updateUser')
    Route.delete('/users/:id', 'UserController.delete').as('deleteUser')
    Route.patch('/users', 'UserController.query').as('queryUsers')

    Route.get('/sag', 'SagController.index').as('getSag')
    Route.patch('/sag/:id', 'SagController.update').as('updateSag')

    Route.get('/bfi', 'BfiController.query').as('getBfi')
    Route.put('/bfi', 'BfiController.create').as('createBfi').validator('BfiCreate')
    Route.patch('/bfi/:id', 'BfiController.update').as('updateBfi').validator('BfiUpdate')
    Route.delete('/bfi/:id', 'BfiController.delete').as('deleteBfi')
    Route.patch('/bfi', 'BfiController.query').as('queryBfi')
    Route.post('/bfi/fileUpload', 'BfiController.fileUpload').as('bfiFileUpload')

    Route.get('/terminals', 'TerminalController.index').as('getTerminals')
    Route.get('/terminals', 'TerminalController.query').as('queryTerminals')
    Route.put('/terminals', 'TerminalController.create').as('createTerminal').validator('TerminalCreate')
    Route.patch('/terminals/:id', 'TerminalController.update').as('updateTerminal').validator('TerminalUpdate')
    Route.delete('/terminals/:id', 'TerminalController.delete').as('deleteTerminal')
    Route.patch('/terminals', 'TerminalController.updateAll').as('updateAllTerminals')
    Route.patch('/terminals/migrate/:id', 'TerminalController.migrate').as('migrateTerminal').validator('TerminalMigrate')

    Route.put('/terminalKeys/:terminalId', 'TerminalKeyController.create').as('createTerminalKey')
    Route.patch('/terminalKeys/:id', 'TerminalKeyController.update').as('updateTerminalKey')
    Route.get('/terminalKeys/by/:terminalId', 'TerminalKeyController.queryByTerminalId').as('queryKeyByTerminalId')
    Route.delete('/terminalKeys/:id', 'TerminalKeyController.delete').as('deleteTerminalKey')

    Route.put('/terminalNotes/:terminalId', 'TerminalNoteController.create').as('createTerminalNote')
    Route.patch('/terminalNotes/:id', 'TerminalNoteController.update').as('updateTerminalNote')
    Route.get('/terminalNotes/by/:terminalId', 'TerminalNoteController.queryByTerminalId').as('queryNoteByTerminalId')
    Route.delete('/terminalNotes/:id', 'TerminalNoteController.delete').as('deleteTerminalNote')

    Route.get('/customer/:id', 'CustomerController.getById').as('getCustomerById')
    Route.get('/customers', 'CustomerController.query').as('getCustomers')
    Route.patch('/customers/:id', 'CustomerController.update').as('updateCustomer').validator('CustomerUpdate')
    Route.delete('/customers/:id', 'CustomerController.delete').as('deleteCustomer')
    Route.patch('/customers', 'CustomerController.query').as('queryCustomers')
    //Route.get('/customers/sync', 'CustomerController.sync').as('syncCustomers')
    Route.post('/customer/sendSms/:id', 'CustomerController.sendSms').as('sendSmsCustomer')
    Route.post('/customer/sendCustomSms/:id', 'CustomerController.sendCustomSms').as('sendCustomSmsCustomer')
    Route.get('/customer/updateInfo/:id', 'CustomerController.updateInfo').as('updateInfoCustomer')
    Route.post('/customer/utilityBill', 'CustomerController.utilityBillUpload').as('utilityBillUpload')
    Route.get('/customer/riskCheck/:id', 'CustomerController.sendKycRiskCheck').as('sendKycRiskCheck')

    //Route.get('/customers/sendMultiSms', 'CustomerController.sendMultiSms')
    Route.post('/customer/sendMultiSms', 'CustomerController.sendMultiSms').as('sendMultiSmsCustomer')
    //Route.get('/customerGetCrmData/:id', 'CustomerController.getCrmData').as('getCrmDataCustomer')

    Route.get('/transfers', 'TransferController.index').as('getTransfers')
    Route.put('/transfers', 'TransferController.create').as('createTransfer').validator('TransferCreate')
    Route.patch('/transfers/:id', 'TransferController.update').as('updateTransfer').validator('TransferUpdate')
    Route.delete('/transfers/:id', 'TransferController.delete').as('deleteTransfer')
    Route.patch('/transfers', 'TransferController.query').as('queryTransfers')
    Route.get('/transfers/by/:login', 'TransferController.queryByLogin').as('queryTransfersByLogin')
    Route.get('/transfers/device/:terminalCode', 'TransferController.queryByTerminal').as('queryTransfersByTerminal')
    Route.get('/transfers/bfi/:bId', 'TransferController.queryByBfi').as('queryTerminalByBfi')
    Route.get('/transfers/sag/:sagId', 'TransferController.queryBySag').as('queryTransfersBySag')
    Route.get('/transfers/log/:logId', 'TransferController.index').as('queryTransfersByLog')

    Route.get('/logs', 'SystemLogController.index').as('getLogs')
    Route.put('/logs', 'SystemLogController.create').as('createLog')
    Route.patch('/logs', 'SystemLogController.query').as('queryLogs')

    Route.get('/stocks', 'StocksController.index').as('getStocks')
    Route.put('/stocks', 'StocksController.create').as('createStocks').validator('TransferCreate')
    Route.patch('/stocks/:id', 'StocksController.update').as('updateStocks').validator('TransferUpdate')
    Route.delete('/stocks/:id', 'StocksController.delete').as('deleteStocks')
    Route.patch('/stocks', 'StocksController.query').as('queryStocks')
    Route.get('/stocks/by/:login', 'StocksController.queryByLogin').as('queryStocksByLogin')

    Route.get('/cashbox', 'CashboxController.index').as('getCashbox')
    Route.put('/cashbox', 'CashboxController.create').as('createCashbox').validator('TransferCreate')
    Route.patch('/cashbox/:id', 'CashboxController.update').as('updateCashbox').validator('TransferUpdate')
    Route.delete('/cashbox/:id', 'CashboxController.delete').as('deleteCashbox')
    Route.patch('/cashbox', 'CashboxController.query').as('queryCashbox')
    Route.get('/cashbox/by/:login', 'CashboxController.queryByLogin').as('queryCashboxByLogin')

    Route.get('/stocks/accounts', 'StockAccountController.index').as('getStockAccounts')
    Route.patch('/stocks/accounts/:id', 'StockAccountController.update').as('updateStockAccount').validator('StockAccountUpdate')
    Route.delete('/stocks/accounts/:id', 'StockAccountController.delete').as('deleteStockAccount')

    Route.get('/institutes', 'InstituteController.index').as('getInstitutes')
    Route.put('/institutes', 'InstituteController.create').as('createInstitute').validator('InstituteCreate')
    Route.patch('/institutes/:id', 'InstituteController.update').as('updateInstitute').validator('InstituteUpdate')
    Route.delete('/institutes/:id', 'InstituteController.delete').as('deleteInstitute')

    Route.get('/buttons', 'ButtonController.index').as('getButtons')
    Route.put('/buttons', 'ButtonController.create').as('createButton').validator('ButtonCreate')
    Route.patch('/buttons/:id', 'ButtonController.update').as('updateButton').validator('ButtonUpdate')
    Route.delete('/buttons/:id', 'ButtonController.delete').as('deleteButton')

    Route.get('/languages', 'LanguageController.index').as('getLanguages')
    Route.put('/languages', 'LanguageController.create').as('createLanguage').validator('LanguageCreate')
    Route.patch('/languages/:id', 'LanguageController.update').as('updateLanguage').validator('LanguageUpdate')
    Route.delete('/languages/:id', 'LanguageController.delete').as('deleteLanguage')

    Route.get('/postcodes/:page', 'PostcodeController.page').as('getPostcodesPage')
    Route.get('/postcodes', 'PostcodeController.index').as('getPostcodes')
    Route.put('/postcodes', 'PostcodeController.create').as('createPostcode')
    Route.patch('/postcodes/:id', 'PostcodeController.update').as('updatePostcode')
    Route.delete('/postcodes/:id', 'PostcodeController.delete').as('deletePostcode')

    Route.get('/playlist', 'PlaylistController.index').as('getPlaylist')
    Route.put('/playlist', 'PlaylistController.create').as('createPlaylist').validator('PlaylistCreate')
    Route.patch('/playlist/:id', 'PlaylistController.update').as('updatePlaylist')
    Route.delete('/playlist/:id', 'PlaylistController.delete').as('deletePlaylist')

    Route.get('/settings', 'SettingController.index').as('getSettings')
    Route.patch('/settings/:id', 'SettingController.update').as('updateSetting').validator('SettingUpdate')

    Route.get('/forexMtcs', 'ReportController.getForexMtcs').as('getForexMtcs')
    Route.get('/forexMtc/:instituteId/:type/:gateway/:periodId/:begin/:end', 'ReportController.getForexMtc').as('getForexMtc')
    Route.get('/forexMtc/:instituteId/:type/:gateway/:periodId/:begin/:end/order/:field', 'ReportController.getForexMtc').as('getForexMtcOrder')

    Route.put('/forexMtcPdf', 'ReportController.previewForexMtcPdf').as('previewForexMtcPdf')
    Route.put('/saveForexMtc', 'ReportController.saveForexMtc').as('saveForexMtc')
    Route.put('/freezeForexMtc', 'ReportController.freezeForexMtc').as('freezeForexMtc')

    Route.put('/forexMtc', 'ReportController.createForexMtc').as('createForexMtc').validator('ForexMtcCreate')
    Route.patch('/forexMtc/:id', 'ReportController.updateForexMtc').as('updateForexMtc').validator('ForexMtcUpdate')
    Route.delete('/forexMtc/:id', 'ReportController.deleteForexMtc').as('deleteForexMtc')

    Route.patch('/invoice/:id', 'ReportController.updateInvoice').as('updateInvoice').validator('InvoiceUpdate')

}).middleware(['auth', 'csrf', 'onlyPanel'])

//REPORT with AUTH
Route.group(() => {
    Route.get('/all', 'ReportController.getAll').as('getAllReports')

    Route.get('/ibs', 'ReportController.getIbs').as('getIbs')
    Route.get('/ibs/order/:field', 'ReportController.getIbs').as('getIbsOrder')
    Route.get('/ibs/period/:periodId/:begin/:end', 'ReportController.getIbs').as('getIbsPeriod')
    Route.get('/ibs/period/:periodId/:begin/:end/order/:field', 'ReportController.getIbs').as('getIbsPeriodOrder')
    Route.get('/ib/:login/:periodId/:begin/:end', 'ReportController.getIb').as('getIb')
    Route.get('/ib/:login/:periodId/:begin/:end/order/:field', 'ReportController.getIb').as('getIbOrder')

    Route.get('/bfis', 'ReportController.getBfis').as('getBfis')
    Route.get('/bfis/order/:field', 'ReportController.getBfis').as('getBfisOrder')
    Route.get('/bfis/period/:periodId/:begin/:end', 'ReportController.getBfis').as('getBfisPeriod')
    Route.get('/bfis/period/:periodId/:begin/:end/order/:field', 'ReportController.getBfis').as('getBfisPeriodOrder')
    Route.get('/bfi/:id/:periodId/:begin/:end', 'ReportController.getBfi').as('getBfi')
    Route.get('/bfi/:id/:periodId/:begin/:end/order/:field', 'ReportController.getBfi').as('getBfiOrder')

    Route.get('/ccs', 'ReportController.getCcs').as('getCcs')
    Route.get('/ccs/order/:field', 'ReportController.getCcs').as('getCcsOrder')
    Route.get('/cc/:periodId/:begin/:end', 'ReportController.getCc').as('getCc')
    Route.get('/cc/:periodId/:begin/:end/order/:field', 'ReportController.getCc').as('getCcOrder')
    Route.post('/ccs/changeSource', 'ReportController.changeCcSource').as('changeCcSource')

    Route.get('/sags', 'ReportController.getSags').as('getSags')
    Route.get('/sags/order/:field', 'ReportController.getSags').as('getSagsOrder')
    Route.get('/sag/:id/:periodId/:begin/:end', 'ReportController.getSag').as('getSag')
    Route.get('/sag/:id/:periodId/:begin/:end/order/:field', 'ReportController.getSag').as('getSagOrder')

    Route.put('/pdf', 'ReportController.previewPdf').as('previewPdf')

    Route.put('/saveForexData', 'ReportController.saveForexData').as('saveForexData')

    Route.get('/forexIbs', 'ReportController.getForexIbs').as('getForexIbs')
    Route.get('/forexIb/:login/:periodId/:begin/:end', 'ReportController.getForexIb').as('getForexIb')
    Route.get('/forexIb/:login/:periodId/:begin/:end/order/:field', 'ReportController.getForexIb').as('getForexIbOrder')

    Route.get('/forexContests', 'ReportController.getForexContests').as('getForexContests')
    Route.get('/forexContest/:login/:periodId/:begin/:end', 'ReportController.getForexContest').as('getForexContest')
    Route.get('/forexContest/:login/:periodId/:begin/:end/order/:field', 'ReportController.getForexContest').as('getForexContestOrder')

    Route.get('/forexBulls', 'ReportController.getForexBulls').as('getForexBulls')
    Route.get('/forexBull/:login/:periodId/:begin/:end', 'ReportController.getForexBull').as('getForexBull')
    Route.get('/forexBull/:login/:periodId/:begin/:end/order/:field', 'ReportController.getForexBull').as('getForexBullOrder')

    Route.get('/forexQuizes', 'ReportController.getForexQuizes').as('getForexQuizes')
    Route.get('/forexQuiz/:login/:periodId/:begin/:end', 'ReportController.getForexQuiz').as('getForexQuiz')
    Route.get('/forexQuiz/:login/:periodId/:begin/:end/order/:field', 'ReportController.getForexQuiz').as('getForexQuizOrder')

    Route.put('/forexPdf', 'ReportController.previewForexPdf').as('previewForexPdf')
    Route.patch('/invoice/:id', 'ReportController.updateInvoice').as('updateInvoice').validator('InvoiceUpdate')

}).middleware(['auth', 'csrf', 'onlyReport']).prefix('report')

/*
Route.get('/', "PostController.index")

Route.get("/test", () => "teste")

Route.get("/hello/:name", function ({ params }) {
    //return "Test: " + params.name
    return `${params.name} hoşgeldin`
})
*/
