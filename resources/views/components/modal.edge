@if(type=='new')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="new dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">
		<form action="{{ route( action, actionParams) + '?_method=PUT' }}" class="form" method="POST" enctype="multipart/form-data">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="newModalLabel">{{caption.newData}}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						<div class="row">
							@!yield($slot.main)
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{caption.close}}</button>
					<button type="submit" class="btn btn-success"><i class="fas fa-plus"></i> {{caption.add}}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='edit')
<div class="modal fade " id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="edit dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">
		<form id="editForm" action="#" class="form" method="POST" enctype="multipart/form-data">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="editModalLabel">{{ caption.editData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-info" {{ elIf('disabled','', saveDisabled ) }}><i class="fas fa-save"></i> {{ caption.save }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='editCustom')
<div class="modal fade " id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="edit dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">
		<form id="{{formId}}" action="#" class="form" method="POST" enctype="multipart/form-data">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="editModalLabel">{{ caption.editData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-info"><i class="fas fa-save"></i> {{ caption.save }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='info')
<div class="modal fade " id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="info dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">
		<form id="infoForm" action="#" class="form" method="POST" enctype="multipart/form-data">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="infoModalLabel">{{ title }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<div class="ml-0">
						<h5><span id="infoStatusBadge" class="badge"></span></h5>
					</div>
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='imagePreview')
<div class="modal fade " id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="image preview dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">		
		<div class="modal-content">
			<div class="modal-body">
				<div class="container-fluid">
					@!yield($slot.main)
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
			</div>
		</div>
	</div>
</div>

@elseif(type=='delete')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="delete dialog"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="deleteForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="deleteModalLabel">{{ caption.deleteData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-danger"><i class="fas fa-trash"></i> {{ caption.delete }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='sendSms')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="send SMS dialog"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="sendSmsForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="sendSmsModalLabel">{{ caption.sendSmsData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> {{ caption.send }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='sendCustomSms')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="send SMS dialog"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="sendCustomSmsForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="sendCustomSmsModalLabel">{{ caption.sendSmsData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> {{ caption.send }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='sendMultiSms')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="send SMS dialog"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="sendMultiSmsForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="sendMultiSmsModalLabel">{{ caption.sendMultiSmsData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> {{ caption.send }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='detail')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="detail dialog"
	aria-hidden="true">
	<div class="modal-dialog" role="document">		
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="detailModalLabel">{{ caption.detailData }}</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="container-fluid">
					@!yield($slot.main)
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
			</div>
		</div>
	</div>
</div>

@elseif(type=='migrate')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="migrate dialog" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="migrateForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="migrateModalLabel">{{ caption.migrateData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-danger"><i class="fas fa-arrow-right"></i> {{ caption.migrate }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='command')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<form id="commandForm" action="{{ route( action ) + '?_method=PATCH' }}" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="commandModalLabel">{{ caption.commandData }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						@!yield($slot.main)
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
					<button type="submit" class="btn btn-primary"><i class="fas fa-check-circle"></i> {{ caption.send }}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='query')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="query dialog" aria-hidden="true">
	<div class="modal-dialog {{lg}}" role="document">
		<form id="queryForm" action="#" class="form" method="POST">
			{{ csrfField() }}
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="queryModalLabel">{{caption.queryData}}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="container-fluid">
						<div class="row">
							@!yield($slot.main)
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{caption.close}}</button>
					<button type="submit" class="btn btn-primary"><i class="fas fa-search"></i> {{caption.find}}</button>
				</div>
			</div>
		</form>
	</div>
</div>

@elseif(type=='url')
<div class="modal fade" id="{{id}}" tabindex="-1" role="dialog" aria-labelledby="url dialog" aria-hidden="true">
	<div class="modal-dialog modal-xl" role="document">		
		<div class="modal-content p-0 m-0">
			<div class="modal-body p-0 m-0" >
				<div class="container-fluid">
					@!yield($slot.main)
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ caption.close }}</button>
			</div>
		</div>
	</div>
</div>
@endif
