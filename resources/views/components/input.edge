@if(type == 'active')
	<div class="input-group mb-0 {{ sm }}">
		<div id="btnActivePassive" class="btn-group btn-group-toggle" data-toggle="buttons">
			<label class="btn btn-secondary btn-sm active" id="active">
				<input type="radio" name="{{ name }}" value="1" autocomplete="on"> {{ active }}
			</label>
			<label class="btn btn-secondary btn-sm" id="passive">
				<input type="radio" name="{{ name }}" value="0" autocomplete="on"> {{ passive }}
			</label>
		</div>
	</div>
@elseif(type == 'file')
	<div class="input-group mb-2 {{ sm }}">
		<div class="input-group-prepend">
			<div class="input-group-text"><i class="{{ icon }} fa-fw"></i></div>
		</div>
		<div class="custom-file">
			<input type="{{ type }}" class="form-control custom-file-input {{ elIf('is-invalid', '', hError) }}"
				id="{{ id }}Input" name="{{ name }}File" data-toggle="tooltip" data-placement="top" 
				title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} accept="image/*">
			<label id="{{ id }}Label" class="custom-file-label" for="{{ id }}Input" data-browse="{{ caption.selectFile }}">{{ oldValue }}</label>
			<input type="hidden" id="{{ id }}" name="{{ name }}" value="{{ oldValue }}">
		</div>
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@elseif(type == 'switch')
	<div class="input-group mb-2 {{ sm }}">
		<div class="custom-control custom-switch">
			<input type="checkbox" class="form-control  custom-control-input {{ elIf('is-invalid','', hError) }}" 
				id="{{ id }}" name="{{ name }}" {{ elIf('disabled','', !enabled )}} {{ elIf('checked','', options )}}>
			<label class="custom-control-label" for="{{ id }}">{{ placeholder }}</label>
		</div>
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@elseif(type == 'image')
	<div class="input-group mb-2 {{ sm }}">
		<input type="text" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
		<div class="input-group-append">
			<button class="btn btn-outline-secondary btnImage" type="button" data-src="{{ placeholder }}" 
				id="{{ id }}Button" name="{{ name }}Image" data-toggle="modal" data-target="#imagePreviewModal"><i class="{{ icon }} fa-fw"></i></button>
		</div>
	</div>
@elseif(type == 'imageFile')
	<div class="input-group mb-2 {{ sm }}">
		<input type="text" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
		<div class="input-group-append">
			<button class="btn btn-outline-secondary btnImage" type="button" data-src="{{ placeholder }}"
				data-toggle="tooltip" data-placement="top" title="Preview"
				id="{{ id }}Button" name="{{ name }}Image" data-toggle="modal" data-target="#imagePreviewModal"><i class="{{ icon }} fa-fw"></i></button>
			<button class="btn btn-outline-secondary btnImageFile" type="button" data-field="{{ name }}"
				data-toggle="tooltip" data-placement="top" title="Select File"
				id="{{ id }}ButtonFile" name="{{ name }}ImageFile"><i class="fas fa-folder-open fa-fw"></i></button>
		</div>
	</div>
@elseif(type == 'selectInfo')
	<div class="mm-fs-8">{{ placeholder }}</div>
	<div class="input-group mb-2 {{ sm }}">
		<div class="input-group-prepend">
			<div class="input-group-text"><i class="{{ icon }} fa-fw"></i></div>
		</div>
		<select class="form-control custom-select {{ elIf('is-invalid','', hError) }}" 
			id="{{ id }}" name="{{ name }}" {{ elIf('disabled','', !enabled )}} >
			@each(item in options)
			<option value="{{ item.id }}" {{ elIf('selected','', oldValue==item.id )}} >{{ item.title }}</option>
			@endeach
		</select>
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@elseif(type == 'textInfo')
	<div class="mm-fs-8">{{ placeholder }}</div>
	<div class="input-group mb-2 {{ sm }}">
		<div class="input-group-prepend">
			<div class="input-group-text"><i class="{{ icon }} fa-fw"></i></div>
		</div>
		<input type="text" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" {{ elIf('disabled','', !enabled )}} >
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@elseif(type == 'labelSelect')
	<div class="input-group mb-2 {{ sm }}">
		<select class="form-control custom-select {{ elIf('is-invalid','', hError) }}" name="{{ name }}" id="{{ id }}" data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
			<option value="" selected hidden>{{ placeholder }}</option>
			@each(item in options)
				<option value="{{ item.id }}" {{ elIf('selected','', oldValue==item.id )}} >{{ item.title }}</option>
			@endeach
		</select>
		<div class="input-group-prepend">
			<div class="input-group-text" id="{{ id }}End">{{ label }}</div>
		</div>
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@else
	<div class="input-group mb-2 {{ sm }}">
		<div class="input-group-prepend">
			<div class="input-group-text" id="{{ id }}Label"><i class="{{ icon }} fa-fw"></i></div>
		</div>
		@if(label)
			<div class="input-group-prepend">
				<div class="input-group-text" id="{{ id }}End">{{ label }}</div>
			</div>
		@endif
		@if(type == 'select')
			<select class="form-control custom-select {{ elIf('is-invalid','', hError) }}" name="{{ name }}" id="{{ id }}" data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
				<option value="" selected hidden>{{ placeholder }}</option>
				@each(item in options)
					<option value="{{ item.id }}" {{ elIf('selected','', oldValue==item.id )}} >{{ item.title }}</option>
				@endeach
			</select>
		@elseif(type=='textarea')
			<textarea class="form-control {{ elIf('is-invalid', '', hError) }}" id="{{ id }}" rows="{{ row }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >{{ oldValue }}</textarea>
		@elseif(type=='date')
			<input type="date" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ value }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@elseif(type=='time')
			<input type="time" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ value }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@elseif(type=='dateTime')
			<input type="datetime-local" step="1"
					min="2020-01-01 00:00:00" max="2030-12-30 23:59:59" 
					class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ value }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@elseif(type == 'money')
			<input type="number" step="0.01" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@elseif(type == 'double')
			<input type="number" step="0.0000001" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@elseif(type == 'number')
			<input type="{{ type }}" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@else
			<input type="{{ type }}" class="form-control {{ elIf('is-invalid', '', hError) }}" value="{{ oldValue }}"
					id="{{ id }}" name="{{ name }}" placeholder="{{ placeholder }}"
					data-toggle="tooltip" data-placement="top" title="{{ placeholder }}" {{ elIf('disabled','', !enabled )}} >
		@endif
		{{ elIf('<div class="invalid-feedback">$self</div>', gError, hError) }}
	</div>
@endif
