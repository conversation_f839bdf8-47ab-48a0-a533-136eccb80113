@layout('layout.report_main')

@section('title')
	@if(setting.company == 'tradecenter24')
		Trade Center Reports
	@elseif(setting.company == 'forex724')
		Forex724 Reports
	@endif
@endsection


@section('content')

	<div class="jumbotron mt-5">
		<h1 class="display-4">{{ title }} <i class="fab fa-pied-piper-square"></i></h1>
		<p class="lead">{{ subtitle }}</p>
		<hr class="my-4">
		@loggedIn
			<p id="data"></p>
		@else
			<p id="data">{{ data }}</p>
		@endloggedIn
		<p class="lead">
			@loggedIn
				<a class="btn btn-primary btn-lg" href="{{ route('getAllReports') }}" role="button">{{ buttonCaption }}</a>
			@else
				<a class="btn btn-primary btn-lg" href="{{ route('login') }}" role="button">Login</a>
			@endloggedIn
		</p>
	</div>

	@if(old('error'))
		<div class="alert alert-danger fade show" role="alert">
			<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
		</div>
	@endif

	@if(old('info'))
		@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
			@slot('strong')
				<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
			@endslot
		@endcomponent
	@endif
	<div class="alert alert-info">
		Version {{caption.version}} Last Update: {{caption.lastUpdate}}
	</div>
	<div>
		@if(setting.company == 'tradecenter24')
			<p class="mm-t-ortala mm-gri">All rights reserved© Trade Center ® Switzerland</p>
			<p class="mm-t-ortala mm-gri mm-fs-6">powered by Alpio.ch</p>
		@endif
		@if(setting.company == 'forex724')
			<p class="mm-t-ortala mm-gri">All rights reserved© Forex724 ® Switzerland</p>
		@endif
	</div>
@endsection
