@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Data List  --}}
<div class="jumbotron mt-3 mb-3 p-3">
	<h1 class="display-4">{{summary.businessName}} <span class="badge bg-secondary text-white">{{ summary.name }}</span></h1>
	
	<div class="row mt-0 mb-0 p-0 my-0">
		<div class="col-7 text-left">
			<p class="lead"><b>Period: </b>{{ summary.period }} <b>Commission:</b> {{ summary.provision}} <b>Count:</b> {{ data.length }}</p>
		</div>
		<div class="col-5 text-right">
			<p class="lead"><b>Trade ID:</b> {{ summary.login }}</p>
		</div>
	</div>
	
	<hr class="my-2">
	<p class="lead">
		<a id="btnExport" href="#" role="button" class="btn btn-info btn-lg" aria-pressed="true">
			<i class="fas fa-download"></i>
		</a>
	</p>
	
</div>

<table class="table table-striped table-sm" id="tblData">
	<thead>
		<tr>
			<th scope="col"><a href="{{ url }}/order/date{{order.asc}}">Date</a></th>
			<th scope="col">Comment</th>
			<th scope="col"><a href="{{ url }}/order/login{{order.asc}}">Trader</a></th>
			<th scope="col">Invited Trader</th>
			<th scope="col" class="text-right"><a href="{{ url }}/order/amount{{order.asc}}">Amount</a></th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		@if(item.amount > 0)
		<tr class="dataLine">
			<td>{{ item.date }}</td>
			<td class="mm-fs-8">{{ item.comment }}</td>
			<td class="mm-fs-8">{{ item.login }} {{ item.name.substring(0, 20) }}</td>
			<td class="mm-fs-8">{{ item.inviteLogin }} {{ item.inviteName.substring(0, 20) }}</td>
			<td class="text-right"><b>{{ item.amount }}</b></td>
		</tr>
		@endif
		@endeach
	</tbody>
	<tfoot>
		<tr class="bg-light">
			<td>{{ summary.period }}</td>
			<td></td>
			<td></td>
			<td>Total:</td>
			<td id="totalAmount" class="text-right"><b>{{ summary.totalAmount }}</b></td>
		</tr>
	</tfoot>
</table>

@endsection

@section('script')
<script>
$( function() {

	$(".btnInvoice").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url = '{{caption.route}}/' + id + '?_method=POST'
		$('#sendSmsForm').attr('action', url)
		$('#sendSmsRecord').html(item)
	})
	
	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[1].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						parseFloat(row.cells[4].innerText).toFixed(2).replace('.', ',').toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Forex_Bulls_Commission_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
});
</script>
@endsection
