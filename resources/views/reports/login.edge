@layout('layout.report_main')

@section('content')
	<div class="row">
		<div class="col-8 col-sm-8 col-md-5 col-lg-4 mx-auto text-center">
			<div class="card mt-5">
				<div class="card-header">{{ title }}</div>
				<div class="card-body">
					<form action="{{ route('checkLogin') }}" class="form" method="POST">
						{{ csrfField() }}
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fa fa-at"></i></div>
							</div>
							<input type="text" class="form-control {{ elIf('is-invalid','', hasErrorFor('email')) }}" value="{{ old('email') }}" id="email" name="email" placeholder="{{ emailCaption }}" autofocus>
							{{ elIf('<div class="invalid-feedback">$self</div>', getErrorFor('email'), hasErrorFor('email')) }}
						</div>
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fa fa-key"></i></div>
							</div>
							<input type="password" class="form-control {{ elIf('is-invalid','', hasErrorFor('pass')) }}" name="pass" id="pass" placeholder="{{ passwordCaption }}" >
							{{ elIf('<div class="invalid-feedback">$self</div>', getErrorFor('pass'), hasErrorFor('pass')) }}
						</div>
						
						<div class="input-group">
							<button type="submit" class="btn btn-primary mx-auto">{{ buttonCaption }}</button>
						</div>
					</form>
				</div>
			</div>
			@if(old('alert'))
				<div class="alert alert-danger mt-3">{{ old('alert') }}</div>
			@endif
		</div>
	</div>
@endsection
