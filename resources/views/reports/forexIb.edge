@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Data List  --}}
<div class="jumbotron mt-3 mb-3 p-3">
	<h1 class="display-4">{{summary.businessName}} <span class="badge bg-secondary text-white">{{ summary.name }}</span></h1>
	
	<div class="row mt-0 mb-0 p-0 my-0">
		<div class="col-7 text-left">
			<p class="lead"><b>Period: </b>{{ summary.period }} <b>Commission:</b> {{ summary.provision}} <b>Count:</b> {{ data.length }}</p>
		</div>
		<div class="col-5 text-right">
			<p class="lead"><b>Trade ID:</b> {{ summary.login }}</p>
		</div>
	</div>
	
	<hr class="my-2">
	<p class="lead">
		<div class="row mt-0 mb-0 p-0 my-0">
			<div class="col-6">
				<form action="{{ route('previewForexPdf') + '?_method=PUT' }}" class="form" method="POST" enctype="multipart/form-data">
					{{ csrfField() }}
					<input type="hidden" id="invoiceType" name="invoiceType" value="{{ summary.invoiceType }}">
					<input type="hidden" id="cid" name="cid" value="{{ summary.id }}">
					<input type="hidden" id="ccid" name="ccid" value="{{ summary.ccid }}">
					<input type="hidden" id="login" name="login" value="{{ summary.login }}">
					<input type="hidden" id="businessName" name="businessName" value="{{ summary.businessName }}">
					<input type="hidden" id="name" name="name" value="{{ summary.name }}">
					<input type="hidden" id="recipientTitle" name="recipientTitle" value="{{ summary.recipientTitle }}">
					<input type="hidden" id="recipientName" name="recipientName" value="{{ summary.recipientName }}">
					<input type="hidden" id="addressFirst" name="addressFirst" value="{{ summary.addressFirst }}">
					<input type="hidden" id="addressSecond" name="addressSecond" value="{{ summary.addressSecond }}">
					<input type="hidden" id="addressThird" name="addressThird" value="{{ summary.addressThird }}">
					<input type="hidden" id="period" name="period" value="{{ summary.period }}">
					<input type="hidden" id="provision" name="provision" value="{{ summary.provision }}">
					<input type="hidden" id="moneyType" name="moneyType" value="{{ summary.moneyType }}">
					
					@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==21)
					<button type="submit" class="btn btn-primary btn-lg">
						<i class="fas fa-file-invoice"></i> Invoice</button>
					<a id="btnExport" href="#" role="button" class="btn btn-info btn-lg" aria-pressed="true">
						<i class="fas fa-download"></i>
					</a>
					@endif
					
				</form>
			</div>
			<div class="col-6 text-right">
				<div class="row">
					<div class="col">
						@if(1==2)
						<form action="{{ route('saveForexData') + '?_method=PUT' }}" class="form" method="POST" enctype="multipart/form-data">
							{{ csrfField() }}
							<input type="hidden" id="period" name="period" value="{{ summary.period }}">
							<input type="hidden" id="reportType" name="reportType" value="ForexIB">
							
							@if(auth.user.role==3 && saveAble)
							<button type="submit" class="btn btn-success btn-lg"
								@if(status > 0)
									disabled
								@endif
							>
								@if(status > 0)
									<i class="fas fa-database"></i> From DB
								@else
									<i class="fas fa-save"></i> Export DB
								@endif
							</button>
							@endif
						</form>
						@endif
					</div>
				</div>
			</div>
		</div>
	</p>
	
</div>

<table class="table table-striped table-sm" id="tblData">
	<thead>
		<tr>
			<th scope="col"><a href="{{ url }}/order/date{{order.asc}}">Date</a></th>
			<th scope="col">Comment</th>
			<th scope="col"><a href="{{ url }}/order/login{{order.asc}}">Trader</a></th>
			<th scope="col" class="text-right"><a href="{{ url }}/order/amount{{order.asc}}">Amount</a></th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		@if(item.amount > 0)
		<tr class="dataLine">
			<td>{{ item.date }}</td>
			<td class="mm-fs-8">{{ item.comment }}</td>
			<td class="mm-fs-8">{{ item.login }} {{ item.name.substring(0, 20) }}</td>
			<td class="text-right"><b>{{ item.amount }}</b></td>
		</tr>
		@endif
		@endeach
	</tbody>
	<tfoot>
		<tr class="bg-light">
			<td>{{ summary.period }}</td>
			<td></td>
			<td>Total:</td>
			<td id="totalAmount" class="text-right"><b>{{ summary.totalAmount }}</b></td>
		</tr>
	</tfoot>
</table>

@endsection

@section('script')
<script>
$( function() {

	$(".btnInvoice").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url = '{{caption.route}}/' + id + '?_method=POST'
		$('#sendSmsForm').attr('action', url)
		$('#sendSmsRecord').html(item)
	})
	
	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[1].innerText.toString(),
						row.cells[2].innerText.toString(),
						parseFloat(row.cells[3].innerText).toFixed(2).replace('.', ',').toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Forex_IB_Commission_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
});
</script>
@endsection
