@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection


@section('content')

	<div class="jumbotron mt-5">
		<h1 class="display-4">All Reports</h1>
		
		<hr class="my-4">
		
		<p class="lead">
			@if(setting.company == 'tradecenter24')
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)			
					<a class="btn btn-primary btn-lg" href="{{ route('getForexIbs') }}" role="button">Commission</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexContests') }}">Contest</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexBulls') }}">Bulls</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexQuizes') }}">Quiz</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==13)
					<a class="btn btn-primary btn-lg" href="{{ route('getSags') }}" role="button">SAG</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
				<a class="btn btn-primary btn-lg" href="{{ route('getBfis') }}" role="button">BFI</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getIbs') }}" role="button">Sub-IB</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==12)
					<a class="btn btn-primary btn-lg" href="{{ route('getCcs') }}" role="button">CC</a>
				@endif
			@endif
			
			@if(setting.company == 'forex724')
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)			
					<a class="btn btn-primary btn-lg" href="{{ route('getForexIbs') }}" role="button">IB Report</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexContests') }}">Contest Report</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexBulls') }}">Bulls Report</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexQuizes') }}">Quiz Report</a>
				@endif
				@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==11)
					<a class="btn btn-primary btn-lg" href="{{ route('getForexMtcs') }}">MTC Report</a>
				@endif
			@endif
		</p>
		
	</div>

	@if(old('error'))
		<div class="alert alert-danger fade show" role="alert">
			<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
		</div>
	@endif

	@if(old('info'))
		@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
			@slot('strong')
				<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
			@endslot
		@endcomponent
	@endif
	
@endsection
