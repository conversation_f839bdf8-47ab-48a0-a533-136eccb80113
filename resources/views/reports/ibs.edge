@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Data List  --}}

<div class="container-fluid mt-3">
	<div class="row">
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="beginDateLabel">Period</div>
				</div>
				<select name="nPeriod" id="iPeriod" 
					class="form-control custom-select {{ elIf('is-invalid','', hError) }}" 
					data-toggle="tooltip" data-placement="top" title="Periode" aria-label="Periode">
					<option value="" selected hidden>All</option>
					@each(item in periods)
						<option value="{{ item.id }}" {{ elIf('selected','', oldValue==item.id )}} >
							{{ item.title }}
						</option>
					@endeach
				</select>
				<div class="input-group-append">
					<button role="button" id="btnRefresh" class="btn btn-success" aria-pressed="true">
						<i class="fas fa-sync fa-fw"></i>
					</button>
				</div>
			</div>
		</div>
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="beginDateLabel">Start</div>
				</div>
				<input type="date" class="form-control {{ elIf('is-invalid', '', hError) }}" value=""
					id="beginDate" name="beginDate" placeholder="Begin Date"
					data-toggle="tooltip" data-placement="top" title="BeginDate">			
			</div>
		</div>
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="endDateLabel">End</div>
				</div>
				<input type="date" class="form-control {{ elIf('is-invalid', '', hError) }}" value=""
					id="endDate" name="endDate" placeholder="End Date"
					data-toggle="tooltip" data-placement="top" title="EndDate">				
			</div>
		</div>
	</div>
</div>

<div class="container-fluid mt-3">
	<div class="px-0 py-1 border rounded">
	<table class="table table-striped table-sm table-borderless mb-0" id="tblData">
		<thead class="border-bottom">
			<tr>
				<th scope="col"><a href="{{ url }}/order/name{{order.asc}}">Name</a></th>
				<th scope="col"><a href="{{ url }}/order/institute{{order.asc}}">Institute</a></th>
				<th scope="col"><a href="{{ url }}/order/group{{order.asc}}">Group</a></th>
				<th scope="col"><a href="{{ url }}/order/login{{order.asc}}">Trader ID</a></th>
				<th scope="col" class="text-right"><a href="{{ url }}/order/amount{{order.asc}}">Commission</a></th>
				<th scope="col" class="text-right">
					<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
						<i class="fas fa-download"></i>
					</a>
				</th>
			</tr>
		</thead>
		<tbody>
			@each(item in data)
			<tr class="dataLine">
				<td>{{ item.surname.substring(0,20) + ' ' + item.name.substring(0,20) }}</td>
				<td class="mm-fs-8">{{ item.institute }}</td>
				<td class="mm-fs-8">{{ item.groupName }}</td>
				<td>{{ item.login }}</td>
				<td class="text-right {{ item.amount > 0 ? 'mm-mavi mm-kalin' : 'mm-gri mm-fs-8' }}">{{ item.amount }}</td>
				<td class="text-right">
					<div class="btnColumn">
						@if(auth.user.role==3 || auth.user.role==2)
						<a href="/" role="button" data-id="{{ item.id }}" data-login="{{ item.login }}" 
							class="btn btn-outline-primary btn-sm btnIbDetail" aria-pressed="true">
							<i class="fas fa-scroll"></i>
						</a>
						@endif
					</div>
				</td>
			</tr>
			@endeach
		</tbody>
		<tfoot>
			<tr class="bg-light">
				<td><b>{{period.title}}</b></td>
				<td></td>
				<td></td>
				<td>Total:</td>
				<td id="totalAmount" class="text-right"><b>{{ totalAmount }}</b></td>
				<td></td>
			</tr>
		</tfoot>
	</table>
	</div>
</div>
@endsection

@section('script')
<script>
$(function () {
	const tarih = new Date()
	const periods = {{{ toJSON(periods) }}}
	const period = {{{ toJSON(period) }}}
	
	function changeUrl(periodId){		
		const begin = $("#beginDate").val()
		const end = $("#endDate").val()
		const unixBegin = new Date(begin).getTime()
		const unixEnd = new Date(end).getTime()
		
		$(".btnIbDetail").each(function(index, item){
			const login = $(this).attr('data-login')
			
			const url = "/report/ib/" + login + "/" + periodId + "/" + unixBegin + "/" + unixEnd
			$(this).attr("href", url)
		})
	}
	
	$("#btnRefresh").click(function () {
		const periodId = $("#iPeriod").val()
		const begin = $("#beginDate").val()
		const end = $("#endDate").val()
		const unixBegin = new Date(begin).getTime()
		const unixEnd = new Date(end).getTime()
		const url = "/report/ibs/period/" + periodId + "/" + unixBegin + "/" + unixEnd
		window.location.href = url
	})
	
	$("#iPeriod").change(function() {
		const periodId =  $(this).val()
		
		const begin = periods[periodId].beginDate
		const end = periods[periodId].endDate
		$("#beginDate").val(begin.substring(0,10))
		$("#endDate").val(trTarih(end.substring(0,10)))

		changeUrl(periodId)
	})
	
	$("#beginDate").change(() => changeUrl(0))
	$("#endDate").change(() => changeUrl(0))
	
	setTimeout(() => {
		$("#iPeriod").val(period.id);
		$("#iPeriod").change()
	}, 500);
	
	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[1].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[4].innerText.toString(),
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "IB_List.csv");
        document.body.appendChild(link);

        link.click();
	})
	
});
</script>
@endsection
