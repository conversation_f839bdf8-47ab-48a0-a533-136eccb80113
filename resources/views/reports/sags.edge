@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Data List  --}}
<div class="container-fluid mt-3">
	<div class="row">
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="beginDateLabel">Period</div>
				</div>
				<select name="nPeriod" id="iPeriod" 
					class="form-control custom-select {{ elIf('is-invalid','', hError) }}" 
					data-toggle="tooltip" data-placement="top" title="Periode" aria-label="Periode">
					<option value="" selected hidden>All</option>
					@each(item in periods)
						<option value="{{ item.id }}" {{ elIf('selected','', oldValue==item.id )}} >
							{{ item.title }}
						</option>
					@endeach
				</select>
			</div>
		</div>
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="beginDateLabel">Start</div>
				</div>
				<input type="date" class="form-control {{ elIf('is-invalid', '', hError) }}" value=""
					id="beginDate" name="beginDate" placeholder="Begin Date"
					data-toggle="tooltip" data-placement="top" title="BeginDate">			
			</div>
		</div>
		<div class="col-md-4">
			<div class="input-group mb-2">
				<div class="input-group-prepend">
					<div class="input-group-text" id="endDateLabel">End</div>
				</div>
				<input type="date" class="form-control {{ elIf('is-invalid', '', hError) }}" value=""
					id="endDate" name="endDate" placeholder="End Date"
					data-toggle="tooltip" data-placement="top" title="EndDate">				
			</div>
		</div>
	</div>
</div>

<div class="container-fluid mt-3 rounded">
	@each(item in data)
	<div class="card mb-3">
		<div class="card-header" id="headingOne">
			<div class="float-left">
				<p class="lead mm-fs-16"><i class="{{caption.icon}}"></i> {{ item.name }}</p>
			</div>
			
			<div class="float-right">
				@if(auth.user.role==3 || auth.user.role==2)
				<a href="/" role="button" data-id="{{ item.id }}" data-login="{{ item.login }}" 
					class="btn btn-outline-primary btnSagDetail" aria-pressed="true">
					<i class="fas fa-scroll"></i>
				</a>
				@endif
			</div>
		</div>

		<div class="card-body px-0 py-1">			
			<table class="table table-striped table-sm table-borderless" id="tblCustomerData">
				<thead class="border-bottom">
					<tr>
						<th scope="col">Business Name</th>
						<th scope="col">Contact Name</th>
						<th scope="col">Trader IDs</th>
					</tr>
				</thead>
				<tbody>
					@each(bfi in item.bfis)
					<tr class="dataLine">
						<td>{{ bfi.businessName.substring(0,20) }}</td>
						<td class="mm-fs-8">{{ bfi.contactName }}</td>
						<td>{{ bfi.logins }}</td>
					</tr>
					@endeach
				</tbody>
			</table>
		</div>
	</div>
	@endeach
</div>
<br>

@endsection

@section('script')
<script>
$( function() {
	
	const tarih = new Date()
	const periods = {{{ toJSON(periods) }}}
	
	function changeUrl(periodId){
		
		const begin = $("#beginDate").val()
		const end = $("#endDate").val()
		
		const unixBegin = new Date(begin).getTime()
		const unixEnd = new Date(end).getTime()
		
		$(".btnSagDetail").each(function(index, item){
			const id = $(this).attr('data-id')
			
			const url = "/report/sag/" + id + "/" + periodId + "/" + unixBegin + "/" + unixEnd
			$(this).attr("href", url)
		})
	}
	
	$("#iPeriod").change(function() {
		const periodId =  $(this).val()
		
		const begin = periods[periodId].beginDate
		const end = periods[periodId].endDate
		$("#beginDate").val(begin.substring(0,10))
		$("#endDate").val(trTarih(end.substring(0,10)))

		changeUrl(periodId)
	})
	
	$("#beginDate").change(() => changeUrl(0))
	$("#endDate").change(() => changeUrl(0))	
	
	setTimeout(() => {
		$("#iPeriod").val(8);
		$("#iPeriod").change()
	}, 500);
	
});
</script>
@endsection
