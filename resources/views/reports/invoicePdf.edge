@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
<div class="bg-white p-4 rounded mt-4" id="printSpace">

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Invoice Detail  --}}
<div class="row mt-4">
	<div class="col-lg-6 col-sm-6 col-xs-12">
		<img src="{{ assetsUrl('images/invoice_logo.png')}}">
	</div>
	<div class="col-lg-6 col-sm-6 col-xs-12 mt-3">
		<div class="row">
			<div class="col-4"><b>Telefon:</b></div>
			<div class="col-8">{{ invoiceSetting.phone }}</div>
		</div>
		<div class="row">
			<div class="col-4 nowrap"><b>E-Mail:</b></div>
			<div class="col-8">{{ invoiceSetting.email }}</div>
		</div>
	</div>
</div>

<div class="row mt-4 mb-4">
	<div class="col-12 text-center"><h4>Monatsabrechnung: {{summary.invoiceType}}</h4></div>
</div>

<div class="row">
	<div class="col-lg-4 col-sm-5 mt-3">
		<div class="row">
			<div class="col-4"><b>Datum:</b></div>
			<div class="col-8 bg-light">{{summary.date}}</div>
		</div>
		<div class="row">
			<div class="col-4"><b>Periode:</b></div>
			<div class="col-8 bg-light">{{summary.period}}</div>
		</div>
	</div>
	<div class="col-lg-2 col-sm-1"></div>
	<div class="col-lg-6 col-sm-6 mt-3">
		<div class="row">
			<div class="col-lg-8 col-sm-12 bg-light">
				@if(summary.businessName && summary.businessName != '')
					{{ summary.businessName }}<br/>
				@endif
				{{ summary.name }}
			</div>
		</div>
		<div class="row">
			<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressFirst}}</div>
		</div>
		<div class="row">
			<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressSecond}}</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<table class="table table-sm mt-4 mm-print">
	<tbody>
		@if(summary.invoiceType=='BFI' || summary.invoiceType=='CC' || summary.invoiceType=='SAG')
			@each(item in summary.provisionList)
			<tr {{ elIf('class="bg-light"','', item.id %3 == 0 )}}>
				<td><b>Provision {{ item.title }}</b></td>
				<td class="text-right"><b>{{ item.value }}</b></td>
			</tr>
			@endeach
		@else
			<tr class="bg-light">
				<td><b>Provision {{ summary.moneyType }}</b></td>
				<td class="text-right"><b>{{ summary.provision }}</b></td>
			</tr>
		@endif
		
		@if(summary.invoiceType!='IB' && summary.invoiceType!='CC')
		<tr>
			<td><b>Kosten 1</b></td>
			<td class="text-right"><b>{{ summary.cost1 > 0 || ""}}</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Kosten 2</b></td>
			<td class="text-right"><b>{{ summary.cost2 > 0 || "" }}</b></td>
		</tr>
		@endif
		
		<tr>
			<td><b>MWST {{ summary.taxRate }} %</b></td>
			<td class="text-right"><b>Inklusive MwSt.</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Total Provision {{ summary.moneyType }}</b></td>
			<td id="totalAmount" class="text-right"><b>{{ summary.provision }}</b></td>
		</tr>
	</tbody>
</table>

<div class="row mt-4 p-2">
	<div class="col">
		<p><b>Hinweis:</b> {{ invoiceSetting.note }}</p>
	</div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<table class="table table-sm mt-4 mm-print">
	<tbody>
		<tr class="bg-light">
			<td colspan="2"><b>ÜBERWEISUNG</b></td>
		</tr>
		@if(summary.invoiceType!='BFI' && summary.invoiceType!='IB')
		<tr>
			<td><b>Bank Name:</b></td>
			<td>{{ summary.bankName || "" }}</td>
		</tr>
		<tr class="bg-light">
			<td><b>Bank Adresse:</b></td>
			<td>{{ summary.bankAddress || ""}}</td>
		</tr>
		<tr>
			<td class="nowrap"><b>Konto Name und Adresse:</b></td>
			<td>{{ summary.accountName || ""}}</td>
		</tr>
		<tr class="bg-light">
			<td class="nowrap"><b>IBAN-Nummer:</b></td>
			<td>{{ summary.iban || ""}}</td>
		</tr>
		<tr>
			<td><b>BIC/SWIFT:</b></td>
			<td>{{ summary.bicSwift  || ""}}</td>
		</tr>
		<tr class="bg-light">
			<td><b>Betrag</b></td>
			<td><b>{{ summary.moneyType }} {{ summary.provision }}</b></td>
		</tr>
		@elseif(summary.invoiceType=='BFI')
		<tr>
			<td class="nowrap col-1"><b>Provisionskonto:</b></td>
			<td class="text-left col-11"><b>{{ summary.provisionAccount || "" }}</b></td>
		</tr>
		<tr>
			<td colspan="2">{{ invoiceSetting.note2 }}</td>
		</tr>
		@elseif(summary.invoiceType=='IB')
		<tr>
			<td class="nowrap col-1"><b>Provisionskonto:</b></td>
			<td class="text-left col-11"><b>{{ summary.login || "" }}</b></td>
		</tr>
		<tr>
			<td colspan="2">{{ invoiceSetting.note3 }}</td>
		</tr>
		@endif
	</tbody>
</table>

</div>

@endsection


@section('script')
<script>
$( function() {
	  
});
</script>
@endsection
