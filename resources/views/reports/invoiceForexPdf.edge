@layout('layout.report_main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
<div class="bg-white p-4 rounded mt-4" id="printSpace">

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Invoice Detail  --}}
<div class="row mt-4">
	<div class="col-lg-6 col-sm-6 col-xs-12">
		<img src="{{ assetsUrl(invoiceSetting.logoFile)}}">
	</div>
	<div class="col-lg-6 col-sm-6 col-xs-12 mt-3">
		<div class="row">
			<div class="col-4"><b>Phone:</b></div>
			<div class="col-8">{{ invoiceSetting.phone }}</div>
		</div>
		<div class="row">
			<div class="col-4 nowrap"><b>E-Mail:</b></div>
			<div class="col-8">{{ invoiceSetting.email }}</div>
		</div>
	</div>
</div>
<div class="row mt-4"></div>
<div class="row mt-4 mb-2">
	<div class="col-12 text-left mm-pmavi"><h4>Statement</h4></div>
</div>

<div class="row">
	<div class="col-lg-4 col-sm-5 mt-3">
		<div class="row">
			<div class="col-4 mm-pmavi"><b>Date:</b></div>
			<div class="col-8 bg-light">{{summary.date}}</div>
		</div>
		<div class="row">
			<div class="col-4 mm-pmavi"><b>Period:</b></div>
			<div class="col-8 bg-light">{{summary.period}}</div>
		</div>
	</div>
	<div class="col-lg-1 col-sm-1"></div>
	<div class="col-lg-7 col-sm-6 mt-3">
		<div class="row">
			<div class="col-3 mm-pmavi"><b>Recipient:</b></div>
			<div class="col-9">
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">
						@if(summary.recipientName && summary.recipientName != '')
							{{ summary.recipientName }}<br/>
						@endif
						{{ summary.recipientTitle }}
					</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressFirst}}</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressSecond}}</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressThird}}</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<table class="table table-sm mt-4 mm-print">
	<tbody>
		<tr class="bg-light">
			<td><b>Total Commission {{ summary.moneyType }}</b></td>
			<td class="text-right"><b>{{ summary.provision }}</b></td>
		</tr>
		<tr>
			<td><b>Total Contest</b></td>
			<td class="text-right"><b>{{ summary.contest}}</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Total Bulls</b></td>
			<td class="text-right"><b>{{ summary.bulls}}</b></td>
		</tr>
		<tr>
			<td><b>Total Quiz</b></td>
			<td class="text-right"><b>{{ summary.quiz}}</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Tax</b></td>
			<td class="text-right"><b>VAT included</b></td>
		</tr>
		<tr>
			<td><b>Net Commission {{ summary.moneyType }}</b></td>
			<td id="totalAmount" class="text-right"><b>{{ summary.subTotal }}</b></td>
		</tr>
	</tbody>
</table>

<div class="row mt-4 p-2">
	<div class="col">
		<p><b>Note:</b> {{ invoiceSetting.note }}</p>
	</div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<table class="table table-sm mt-4 mm-print">
	<tbody>
		@if(summary.invoiceType!='ForexIB')
		<tr class="bg-light">
			<td colspan="2"><b>ÜBERWEISUNG</b></td>
		</tr>
		<tr>
			<td><b>Bank Name:</b></td>
			<td>{{ summary.bankName || "" }}</td>
		</tr>
		<tr class="bg-light">
			<td><b>Bank Adresse:</b></td>
			<td>{{ summary.bankAddress || ""}}</td>
		</tr>
		<tr>
			<td class="nowrap"><b>Konto Name und Adresse:</b></td>
			<td>{{ summary.accountName || ""}}</td>
		</tr>
		<tr class="bg-light">
			<td class="nowrap"><b>IBAN-Nummer:</b></td>
			<td>{{ summary.iban || ""}}</td>
		</tr>
		<tr>
			<td><b>BIC/SWIFT:</b></td>
			<td>{{ summary.bicSwift  || ""}}</td>
		</tr>
		<tr class="bg-light">
			<td><b>Betrag</b></td>
			<td><b>{{ summary.moneyType }} {{ summary.provision }}</b></td>
		</tr>
		@endif
	</tbody>
</table>

</div>

@endsection


@section('script')
<script>
$( function() {
	
});
</script>
@endsection
