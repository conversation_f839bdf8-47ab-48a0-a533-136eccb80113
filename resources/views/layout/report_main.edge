<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	
	{{ style('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css')}}
	{{ style('report_style') }}
	@!section('css')
	{{ script('https://kit.fontawesome.com/d9022d75fa.js')}}<!--FontAwesome V5-->
	
	<link id="favicon" rel="shortcut icon" href="{{ assetsUrl('images') }}/favicon.ico">
	<title>
		@!section('title')
	</title>
</head>
<body class="mm-desktop">

	<header>
		@include('layout.report_navbar')
	</header>

	<main role="main" class="container">
		@!section('content')
	</main>

	{{ script('https://code.jquery.com/jquery-3.6.0.min.js') }}
	{{ script('https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js') }}
	{{ script('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js') }}
	
	@!section('script')

	<script>
		$( () => {
			$('[data-toggle="tooltip"]').tooltip()
			setTimeout(function(){
				$(".autohide").alert("close");
			}, 2500);
			
			$("#btnPrint").click(function() {
				window.print();
			});
			
		});	
		
		//'2024-08-13 00:00'
		function trTarihSaat(dateStr, showSeconds=false){
			if(dateStr){
				if(dateStr.indexOf('-') > -1){
					const typeArr = dateStr.split(" ")
					const dateArr = typeArr[0].split("-")
					if(dateArr[1].length==1){
						dateArr[1]='0'+dateArr[1]
					}
					if(dateArr[2].length==1){
						dateArr[2]='0'+dateArr[2]
					}
					let result = dateArr[0] + '-' + dateArr[1] + '-' + dateArr[2]
					//console.log(result)
					
					if(typeArr[1].substring(0,8).length > 0){
						result += ' ' + typeArr[1].substring(0,5)
					}
					
					if(showSeconds){
						if(typeArr[1].substring(6,8)!=""){
							result += ':' + typeArr[1].substring(6,8)
						}else{
							result += ':00'
						}
					}
					
					return result
				}
				if(dateStr.indexOf('.') > -1){
					const typeArr = dateStr.split(" ")
					const dateArr = typeArr[0].split(".")
					if(dateArr[1].length==1){
						dateArr[1]='0'+dateArr[1]
					}
					if(dateArr[2].length==1){
						dateArr[2]='0'+dateArr[2]
					}
					let result=dateArr[2] + '-' + dateArr[1] + '-' + dateArr[0]
					
					if(typeArr[1].substring(0,8).length > 0){
						result += ' ' + typeArr[1].substring(0,5)
					}
					
					if(showSeconds){
						if(typeArr[1].substring(6,8)!=""){
							result += ':' + typeArr[1].substring(6,8)
						}else{
							result += ':00'
						}
					}
					
					return result
				}
			}else{
				if(showSeconds){
					dateStr="2000-01-01 00:00:01"
				}
				else{
					dateStr="2000-01-01 00:00"
				}
			}
			
			return dateStr
		}
		
		function trTarih(dateStr){
			//console.log(dateStr)
			if(dateStr){
				if(dateStr.indexOf('-') > -1){
					const dateArr = dateStr.split("-")
					if(dateArr[1].length==1){
						dateArr[1]='0'+dateArr[1]
					}
					if(dateArr[2].length==1){
						dateArr[2]='0'+dateArr[2]
					}
					let result = dateArr[0] + '-' + dateArr[1] + '-' + dateArr[2]
					//console.log(result)
					return result
				}
				if(dateStr.indexOf('.') > -1){
					const dateArr = dateStr.split(".")
					if(dateArr[1].length==1){
						dateArr[1]='0'+dateArr[1]
					}
					if(dateArr[2].length==1){
						dateArr[2]='0'+dateArr[2]
					}
					let result=dateArr[2] + '-' + dateArr[1] + '-' + dateArr[0]
					//console.log(result)
					return result
				}
			}else{
				dateStr="2000-01-01"
			}
			
			return dateStr
		}
		
		async function postJsonData(url, data) {
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(data)
			});
			return response.json();
		}
		
	</script>

</body>
</html>
