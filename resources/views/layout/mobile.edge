<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	{{ style('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css')}}
	{{ style('style') }}
	@!section('css')
	{{ script('https://kit.fontawesome.com/d9022d75fa.js')}}<!--FontAwesome V5-->
	<link id="favicon" rel="shortcut icon" href="images/favicon.ico">
	<title>
		@!section('title')
	</title>
</head>
<body class="mm-mobile bg-white">

	<header>
		@!section('title')
	</header>

	<main role="main" class="container ml-auto mr-auto">
		@!section('content')
	</main>

	{{--  {{ script('https://code.jquery.com/jquery-3.3.1.slim.min.js') }}  --}}
	{{ script('https://code.jquery.com/jquery-3.6.0.min.js') }}
	{{ script('https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js') }}
	{{ script('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js') }}
	@!section('script')

	<script>
		$( () => {
			$('[data-toggle="tooltip"]').tooltip()
			setTimeout(function(){
				$(".autohide").alert("close");
			}, 2500);
		});

		function trTarih(dateStr){
			if(dateStr.indexOf('-') > -1){
				const dateArr = dateStr.split("-")
				if(dateArr[1].length==1){
					dateArr[1]='0'+dateArr[1]
				}
				if(dateArr[2].length==1){
					dateArr[2]='0'+dateArr[2]
				}
				return dateArr[0] + '-' + dateArr[1] + '-' + dateArr[2]
			}
			return dateStr
		}
		
	</script>

</body>
</html>
