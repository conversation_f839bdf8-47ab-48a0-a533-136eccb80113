<nav class="navbar navbar-expand-lg navbar-light fixed-top shadow">

		<a class="navbar-brand" href="{{ route('home') }}">
			@if(setting.company == 'tradecenter24')
			<img src="{{ assetsUrl('images/tradecenter.png')}}" height="30" alt="Trade Center">
			@endif
			
			@if(setting.company == 'forex724')
			<img src="{{ assetsUrl('images/forex724_logo.png')}}" height="30" alt="Forex724">
			@endif
		</a>

		@if(setting.test_mode == 1)
			<i class="fas fa-vial"></i>
		@endif
		
		<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
			aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>
		
		<div class="collapse navbar-collapse" id="navbarSupportedContent">
			<ul class="navbar-nav mx-auto">
				<li class="nav-item {{caption.navHome}}">
					<a class="nav-link" href="{{ route('home') }}"><i class="fas fa-home"></i></a>
				</li>

				@loggedIn
					@if(setting.company == 'tradecenter24')
						
						@if(auth.user.role==3 || auth.user.role==2 || (auth.user.role>19 && auth.user.role<30))
							<li class="nav-item {{caption.navBankReport}}">
								<a class="nav-link" href="{{ route('getForexIbs') }}"><i class="fas fa-university"></i> Bank Report</a>
							</li>
						@endif
						
						@if(auth.user.role==3 || auth.user.role==2 || (auth.user.role>9 && auth.user.role<20))
							@if(auth.user.role!=12)
							<li class="nav-item {{caption.navSagReport}}">
								<a class="nav-link" href="{{ route('getSags') }}"><i class="fas fa-building"></i> SAG Report</a>
							</li>
							<li class="nav-item {{caption.navBfiReport}}">
								<a class="nav-link" href="{{ route('getBfis') }}"><i class="fas fa-users"></i> BFI Report</a>
							</li>
							<li class="nav-item {{caption.navIbReport}}">
								<a class="nav-link" href="{{ route('getIbs') }}"><i class="fas fa-users"></i> Sub-IB Report</a>
							</li>
							@endif
							
							<li class="nav-item {{caption.navCcReport}}">
								<a class="nav-link" href="{{ route('getCcs') }}"><i class="fas fa-phone"></i> CC Report</a>
							</li>
						@endif
					@endif
					
					<!--  Obsolete  -->
					@if(setting.company == 'forex724')
						@if(auth.user.role==3 || auth.user.role==2 || (auth.user.role>19 && auth.user.role<30))
							<li class="nav-item {{caption.navForexIbReport}}">
								<a class="nav-link" href="{{ route('getForexIbs') }}"><i class="fas fa-users"></i> IB Report</a>
							</li>
							<li class="nav-item {{caption.navForexContestReport}}">
								<a class="nav-link" href="{{ route('getForexContests') }}"><i class="fas fa-crown"></i> Contest Report</a>
							</li>
							<li class="nav-item {{caption.navForexBullReport}}">
								<a class="nav-link" href="{{ route('getForexBulls') }}"><i class="fas fa-star-half-alt"></i> Bulls Report</a>
							</li>
							<li class="nav-item {{caption.navForexQuizReport}}">
								<a class="nav-link" href="{{ route('getForexQuizes') }}"><i class="fas fa-question-circle"></i> Quiz Report</a>
							</li>
							<li class="nav-item {{caption.navForexMtcReport}}">
								<a class="nav-link" href="{{ route('getForexMtcs') }}"><i class="fas fa-sun"></i> MTC Report</a>
							</li>
						@endif
					@endif
					
					<li class="nav-item dropdown pull-right {{caption.navLogin}}">
						<a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false">
							@if(auth.user.role==3)
							<i class="fas fa-user-tie"></i> Admin
							@elseif(auth.user.role==2)
							<i class="fas fa-user-md"></i> Manager
							@elseif(auth.user.role==1)
							<i class="fas fa-user"></i> User
							@elseif(auth.user.role==11)
							<i class="fas fa-user"></i> IB
							@elseif(auth.user.role==12)
							<i class="fas fa-user"></i> Call Center
							@elseif(auth.user.role==13)
							<i class="fas fa-user"></i> SAG
							@elseif(auth.user.role==21)
							<i class="fas fa-user"></i> CRM
							@endif
						</a>
						<div class="dropdown-menu" aria-labelledby="navbarDropdown">
							<a class="dropdown-item" href="{{ route('getProfile') }}"><i class="fas fa-id-card"></i> {{ auth.user.username }}</a>
							<div class="dropdown-divider"></div>
							<a class="dropdown-item" href="{{ route('logout') }}"><i class="fas fa-sign-out-alt"></i> Logout</a>
						</div>
					</li>
					
					@if(caption.isPrintable==true && (auth.user.role==3 || auth.user.role==2 || auth.user.role==21 || auth.user.role==11 || auth.user.role==12 || auth.user.role==13))
					<button id="btnPrint" class="btn btn-primary ml-4 mt-lg-0 mt-sm-4" type="button" aria-pressed="true">
						<i class="fas fa-print"></i> Print
					</button>
					@endif
					
				@else
					<li class="nav-item dropdown {{ caption.navLogin }}">
						<a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false">
							<i class="fas fa-user-circle"></i> Guest
						</a>
						<div class="dropdown-menu" aria-labelledby="navbarDropdown">
							<a class="dropdown-item" href="{{ route('login') }}"><i class="fas fa-sign-in-alt"></i> Login</a>
						</div>
					</li>
				@endloggedIn

			</ul>
		</div>

</nav>

