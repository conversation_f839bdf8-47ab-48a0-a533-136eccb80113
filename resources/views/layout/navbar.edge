<nav class="navbar navbar-expand-lg navbar-light shadow fixed-top">
	<a class="navbar-brand" href="{{ route('home') }}">
		<img src="{{ assetsUrl('images/tradecenter.png')}}" height="30" alt="Trade Center Management Panel">
	</a>

	@if(setting.test_mode == 1)
		<i class="fas fa-vial"></i>
	@endif
	
	<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
		aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
		<span class="navbar-toggler-icon"></span>
	</button>
	
	@if(setting.appType == 'panel')
		<div class="collapse navbar-collapse" id="navbarSupportedContent">
			{{--  <ul class="navbar-nav ml-auto">  --}}
			<ul class="navbar-nav mx-auto">
				<li class="nav-item {{caption.navHome}}">
					<a class="nav-link" href="{{ route('home') }}"><i class="fas fa-home"></i></a>
				</li>

				@loggedIn	
					@if(auth.user.role==3 || auth.user.role==2 || (auth.user.role>19 && auth.user.role<30))
						<li class="nav-item {{caption.navForexMtcReport}}">
							<a class="nav-link" href="{{ route('getForexMtcs') }}"><i class="fas fa-university"></i> Banks</a>
						</li>
					@endif
					
					@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==1 || (auth.user.role>19 && auth.user.role<30))
					<li class="nav-item {{caption.navBfi}}">
						<a class="nav-link" href="{{ route('getBfi') }}?type=1"><i class="fas fa-building"></i> BFI</a>
					</li>
					<li class="nav-item {{caption.navTerminal}}">
						<a class="nav-link" href="{{ route('queryTerminals') }}"><i class="fas fa-desktop"></i> Terminals</a>
					</li>
					@endif
					
					@if(auth.user.role==3 || auth.user.role==2 || (auth.user.role==12))
						<li class="nav-item {{caption.navCustomer}}">
							<a class="nav-link" href="{{ route('getCustomers') }}"><i class="fas fa-users"></i> Accounts</a>
						</li>
						<li class="nav-item {{caption.navTransfer}}">
							<a class="nav-link" href="{{ route('getTransfers') }}"><i class="fas fa-hand-holding-usd"></i> Transactions</a>
						</li>
					@endif
						
					@if(auth.user.role==3 || auth.user.role==2)
						<li class="nav-item {{caption.navStocks}}">
							<a class="nav-link" href="{{ route('getStocks') }}"><i class="fas fa-boxes"></i> Stock Money</a>
						</li>
					@endif
					
					@if(auth.user.role==3 || auth.user.role==2)
					<li class="nav-item dropdown pull-right {{caption.navSetting}}">
						<a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false">
							<i class="fas fa-cog fa-fw"></i>
						</a>
						<div class="dropdown-menu" aria-labelledby="navbarDropdown">
							@if(auth.user.role==3 || auth.user.role==2)
							<a class="dropdown-item" href="{{ route('getLogs') }}"><i class="fas fa-history fa-fw"></i> Logs</a>
							<a class="dropdown-item" href="{{ route('getInstitutes') }}"><i class="fas fa-university fa-fw"></i> Institutes</a>
							<a class="dropdown-item" href="{{ route('getButtons') }}"><i class="fas fa-grip-horizontal fa-fw"></i> Menu</a>

							<a class="dropdown-item" href="{{ route('getUsers') }}"><i class="fas fa-user-friends fa-fw"></i> Users</a>
							<a class="dropdown-item" href="{{ route('getPlaylist') }}"><i class="fas fa-list fa-fw"></i> Playlist</a>
							@endif
							@if(auth.user.role==3)
							<a class="dropdown-item" href="{{ route('getSag') }}"><i class="fas fa-warehouse fa-fw"></i> SAGs</a>
							<a class="dropdown-item" href="{{ route('getStockAccounts') }}"><i class="fas fa-users fa-fw"></i> Own Accounts</a>
							<a class="dropdown-item" href="{{ route('getLanguages') }}"><i class="fas fa-flag fa-fw"></i> Languages</a>
							<div class="dropdown-divider"></div>
							<a class="dropdown-item" href="{{ route('getSettings') }}"><i class="fas fa-cog fa-fw"></i> Change Settings</a>
							@endif
						</div>
					</li>
					@endif

					<li class="nav-item dropdown pull-right {{caption.navLogin}}">
						<a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false">
							@if(auth.user.role==3)
							<i class="fas fa-user-tie"></i> Admin
							@elseif(auth.user.role==2)
							<i class="fas fa-user-md"></i> Manager
							@elseif(auth.user.role==1)
							<i class="fas fa-user"></i> User
							@elseif(auth.user.role==11)
							<i class="fas fa-user"></i> IB
							@elseif(auth.user.role==12)
							<i class="fas fa-user"></i> Call Center
							@elseif(auth.user.role==13)
							<i class="fas fa-user"></i> SAG
							@elseif(auth.user.role==21)
							<i class="fas fa-user"></i> CRM
							@endif
						</a>
						<div class="dropdown-menu" aria-labelledby="navbarDropdown">
							<a class="dropdown-item" href="{{ route('getProfile') }}"><i class="fas fa-id-card"></i> {{ auth.user.username }}</a>
							<div class="dropdown-divider"></div>
							<a class="dropdown-item" href="{{ route('logout') }}"><i class="fas fa-sign-out-alt"></i> Logout</a>
						</div>
					</li>
					
					@if(caption.isPrintable==true && (auth.user.role==3 || auth.user.role==2 || auth.user.role==21 || auth.user.role==11 || auth.user.role==12 || auth.user.role==13))
					<button id="btnPrint" class="btn btn-primary ml-4 mt-lg-0 mt-sm-4" type="button" aria-pressed="true">
						<i class="fas fa-print"></i> Print
					</button>
					@endif
					
				@else
					<li class="nav-item dropdown {{ caption.navLogin }}">
						<a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false">
							<i class="fas fa-user-circle"></i> Guest
						</a>
						<div class="dropdown-menu" aria-labelledby="navbarDropdown">
							<a class="dropdown-item" href="{{ route('login') }}"><i class="fas fa-sign-in-alt"></i> Login</a>
						</div>
					</li>
				@endloggedIn

			</ul>
		</div>
	@endif

</nav>
