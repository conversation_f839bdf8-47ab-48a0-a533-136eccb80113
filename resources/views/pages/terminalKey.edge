@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, actionParams:{terminalId: terminalId}, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@if(item.n != 'created_at' && item.n != 'terminalId' && item.n != 'terminalCode' )
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
			@if(item.n != 'created_at' && item.n != 'terminalId' )
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<table class="table table-striped table-sm" id="tblData">
	<thead>
		<tr>
			<th scope="col">#</th>
			<th scope="col"></th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[2].c }}</th>
			<th scope="col">{{ fields[3].c }}</th>
			<th scope="col" class="text-right">
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal" data-target="#newModal">
					<i class="fas fa-plus"></i> {{ caption.new }}</a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.terminalCode }}</td>
			<td>{{ item.keyName }}</td>
			<td>{{ item.ownerName }}</td>
			<td class="text-right mm-min">
				<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.keyName }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
			</td>
		</tr>
		@endeach
	</tbody>
</table>
@endsection

@section('script')
<script>
$( function() {

	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#n_keyName').val('')
			$('#n_ownerName').val('')
			$('#n_keyName').focus()
		}, 500)
	})
	
	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#e_keyName').val('{{item.keyName}}')
			$('#e_ownerName').val('{{item.ownerName}}')
			$('#e_terminalCode').val('{{item.terminalCode}}')
		}
		@endeach
		setTimeout(()=>{
			$('#e_keyName').focus()
		}, 500)
	})
	
	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif

});
</script>
@endsection
