@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>
	
</style>
@endsection

@section('content')

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
		@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n), 
		hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n + 'New', caption: caption,
		icon: item.i, placeholder: item.c, enabled: item.e } )
		@endeach
		@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n), 
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, caption: caption,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
			@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
		</div>
	</div>
@endcomponent


{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3 mb-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			@each(field in fields)
			<th scope="col">{{ field.c }}</th>
			@endeach
			<th scope="col"></th>
			<th scope="col" class="text-right">
				@if(auth.user.role==3)
					<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal"
					data-target="#newModal"><i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th scope="row"><span class="mm-mavi"><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.caption }}</td>
			<td>{{ item.menuOrder }}</td>
			<td>{{ item.url }}</td>
			<td><img class="zoom" src="MenuImages/de/{{item.image}}" alt="" height="30px"></td>
			<td><img class="zoom" src="MenuImages/fr/{{item.image}}" alt="" height="30px"></td>
			<td><img class="zoom" src="MenuImages/it/{{item.image}}" alt="" height="30px"></td>
			<td><img class="zoom" src="MenuImages/en/{{item.image}}" alt="" height="30px"></td>
			<td>{{ (item.active===1) ? caption.active : caption.passive }}</td>
			<td class="text-right  mm-min">
				@if(auth.user.role==3 || auth.user.role==2)
					<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.caption }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

{{--  info  --}}

@component('components.alert', { type: 'warning', autohide: '', message: '' } )
	@slot('strong')
		<i class="fas fa-lightbulb"></i> <strong>Tip : </strong>Button Link <strong>account, transfer, payout, qrcode</strong> are predefined words to linking <u><i>Account Opening</i></u>, <u><i>Money Transfer</i></u> ,<u><i>Money Payout</i></u> and <u><i>QR Code</i></u>
	@endslot
@endcomponent

@component('components.alert', { type: 'danger', autohide: '', message: '' } )
	@slot('strong')
		<i class="fas fa-exclamation"></i> <strong>Important : </strong>All Button <u>image file names</u> must be same for all languages.</i></u>
	@endslot
@endcomponent

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@endsection

@section('script')
<script>
$( function() {
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[0].n}}']").focus()
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})
	
	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)
		
		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#caption').val( '{{item.caption}}' )
			$('#menuOrder').val( '{{item.menuOrder}}' )
			$('#url').val( ('{{item.url}}').replace('&amp;', '&').replace('amp;','') )
			$('#image').val( '{{item.image}}' )
			$('#imageFr').val( '{{item.image}}' )
			$('#imageIt').val( '{{item.image}}' )
			$('#imageEn').val( '{{item.image}}' )
			$('#imageLabel').text( '{{item.image}}' )
			$('#imageFrLabel').text( '{{item.image}}' )
			$('#imageItLabel').text( '{{item.image}}' )
			$('#imageEnLabel').text( '{{item.image}}' )
			{{item.active}} == 1 ? $('#editModal #active').click() : $('#editModal #passive').click()
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif
	
	$('input[type="file"]').change(function(e){
        var fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
    });
});
</script>
@endsection
