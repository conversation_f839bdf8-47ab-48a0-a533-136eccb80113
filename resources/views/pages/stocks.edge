@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Detail Modal  --}}
@component('components.modal', { type:'detail', id:'detailModal', lg:'', action:'', caption: caption })
	{{{ caption.detailInfo }}}
	<strong><div id="detailField"></div></strong>
@endcomponent

{{-- Query Modal --}}
@component('components.modal', { type:'query', id:'queryModal', lg:'', action: '', caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in queryFields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'q_'+item.n, 
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@if(item.n != 'status' && item.n != 'name' && item.n != 'surname' && item.n != 'created_date')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@if(item.n == 'login' || item.n == 'terminalCode' || item.n == 'financeInstitute' || item.n == 'amount' || item.n == 'currencyType' || item.n == 'bId' || item.n == 'created_date')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Sub Menu  --}}
<div class="container-fluid mt-3">
	<ul class="nav nav-pills">
		<li class="nav-item no-line">
			<a class="nav-link active" href="{{ route('getStocks') }}">Cash Refill</a>
		</li>
		<li class="nav-item no-line">
			<a class="nav-link" href="{{ route('getCashbox') }}">Cash Retrieval</a>
		</li>
	  </ul>
</div>

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[3].c }}</th>
			<th scope="col">{{ fields[4].c }}</th>
			<th scope="col">{{ fields[5].c }}</th>
			<th scope="col">{{ fields[6].c }}</th>
			<th scope="col">{{ fields[7].c }}</th>
			<th scope="col">{{ fields[8].c }}</th>
			<th scope="col" class="text-right pr-3">
				@if(auth.user.role==3)
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal" data-target="#newModal">
					<i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
				<a id="btnQuery" href="#" role="button" class="btn btn-outline-info btn-sm btnQuery position-relative" aria-pressed="true" data-toggle="modal"
					data-target="#queryModal"><i class="fas fa-search"></i>
					<span class="mm-beyaz position-absolute badge rounded-pill bg-info">{{data.length}}</span>
				</a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr class={{ (item.amount>0) ? '' : "mm-pkirmizi" }}>
			<td>{{ item.id }}</td>
			<th scope="row"><span class={{ (item.amount>0) ? "mm-mavi" : "mm-pkirmizi" }}><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.login }}</td>
			<td class="mm-kaydir">{{ item.surname.substring(0,15) + ' ' + item.name.substring(0,15) }}</td>
			<td class="fi mm-fs-8">{{ item.financeInstitute }}</td>
			<td class="text-right">{{ item.amount }}</td>
			<td>{{ item.currencyType }}</td>
			<td>{{ item.terminalCode }}</td>
			<td class="mm-fs-8 mm-kaydir">{{ item.businessName }}</td>
			<td class="mm-fs-8">{{ item.created_date }}</td>
			<td class="text-right mm-min">
				@if((auth.user.role==3 || auth.user.role==2) && item.detail)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.detail }}" class="btn btn-outline-info btn-sm btnDetail" aria-pressed="true" data-toggle="modal"
					data-target="#detailModal"><i class="fas fa-info-circle"></i></a>
				@endif
				@if(auth.user.role==3)
				<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.amount }} {{ item.currencyType }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
	<tfoot>
		<tr>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td>Total:</td>
			<td id="totalAmount" class="text-right"><b>{{ summary.totalAmount }}</b></td>
			<td><b>{{ summary.moneyType }}</td>
			<td></td>
			<td></td>
			<td></td>
		</tr>
	</tfoot>
</table>
</div></div>

@endsection

@section('script')
<script>
$( function() {
	const institutes = {{{ toJSON(fields[3].v) }}} //adonis controller objecti view javascript objecte çevirdik
	$('.fi').each(function(index, item){
		let id = $(this).text()
		if(id && id!="null"){ //null gelirse institute id bilgisi
			let institute = institutes.find(i => i.id==id)
			$(this).html(institute.title)
		}
	})
	
	const bfis = {{{ toJSON(fields[7].v) }}} //adonis controller objecti view javascript objecte çevirdik
	$('.bfi').each(function(index, item){
		let id = $(this).text()
		if(id && id!="null"){ //null gelirse institute id bilgisi
			let bfi = bfis.find(i => i.id==id)
			$(this).html(bfi.title)
		}
	})
	
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[0].n}}']").focus()
			$("#n_financeInstitute").prop("selectedIndex", 3)
			$("#n_currencyType").val('CHF')
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#e_login').val('{{item.login}}')
			$('#e_financeInstitute').val('{{item.financeInstitute}}')
			$('#e_terminalCode').val('{{item.terminalCode}}')
			$('#e_amount').val('{{item.amount}}')
			$('#e_currencyType').val('{{item.currencyType}}')
			$('#e_bId').val('{{item.bId}}')
			$('#e_created_date').val(trTarihSaat('{{item.created_date}}', true))
		}
		@endeach
		setTimeout(()=>{
			$('#e_login').focus()
		}, 500)
	})

	$("#btnQuery").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.queryRoute}}' + '?_method=PATCH'
		$('#queryForm').attr('action', url)

		const currentQuery = {{{ toJSON(currentQuery) }}}
		$('#q_login').val( currentQuery.tradeId )
		$('#q_amount').val( currentQuery.amount )
		$('#q_terminalCode').val( currentQuery.terminalCode )
		$('#q_financeInstitute').val( currentQuery.financeInstitute || 0)
		$('#q_bId').val( currentQuery.bId || 0 )
		$('#q_beginDate').val( trTarih(currentQuery.begin) )
		$('#q_endDate').val( trTarih(currentQuery.end) )

		setTimeout(()=>{
			$('#q_login').focus()
		}, 500)
	})
	
	$(".btnDetail").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		$('#detailField').html(item)
	})

	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[4].innerText.toString(),
						row.cells[5].innerText.toString(),
						row.cells[6].innerText.toString(),
						row.cells[7].innerText.toString(),
						row.cells[8].innerText.toString(),
						row.cells[9].innerText.toString()
					]
				);
			}
		}		
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Stock_Report.csv");
        document.body.appendChild(link);

        link.click();
	})

	@if(old('queryModalShow'))
		$("#btnQuery").click()
	@endif

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif
	
});
</script>
@endsection
