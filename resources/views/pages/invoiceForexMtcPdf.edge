@layout('layout.main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
<div class="bg-white p-4 rounded mt-4" id="printSpace">

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Invoice Detail  --}}
<div class="row mt-4">
	<div class="col-lg-6 col-sm-6 col-xs-12">
		<img src="{{ assetsUrl(invoiceSetting.logoFile) }}" alt="" height="100px">
	</div>
	<div class="col-lg-6 col-sm-6 col-xs-12 mt-3">
		<div class="row">
			<div class="col-4"><b>Phone:</b></div>
			<div class="col-8">{{ invoiceSetting.phone }}</div>
		</div>
		<div class="row">
			<div class="col-4 nowrap"><b>E-Mail:</b></div>
			<div class="col-8">{{ invoiceSetting.email }}</div>
		</div>
	</div>
</div>
<div class="row mt-4"></div>
<div class="row mt-4 mb-2">
	<div class="col-12 text-left mm-pmavi"><h4>Statement</h4></div>
</div>

<div class="row">
	<div class="col-lg-4 col-sm-5 mt-3">
		<div class="row">
			<div class="col-4 mm-pmavi"><b>Invoice Nr.:</b></div>
			<div class="col-8 bg-light">{{summary.invoiceNumber}}</div>
		</div>
		<div class="row">
			<div class="col-4 mm-pmavi"><b>Date:</b></div>
			<div class="col-8 bg-light">{{summary.date}}</div>
		</div>
		<div class="row">
			<div class="col-4 mm-pmavi"><b>Period:</b></div>
			<div class="col-8 bg-light">{{summary.period}}</div>
		</div>
	</div>
	<div class="col-lg-1 col-sm-1"></div>
	<div class="col-lg-7 col-sm-6 mt-3">
		<div class="row">
			<div class="col-3 mm-pmavi"><b>Recipient:</b></div>
			<div class="col-9">
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">
						@if(summary.businessName && summary.businessName != '')
							{{ summary.businessName }}<br/>
						@endif
						{{ summary.name }}
					</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressFirst}}</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressSecond}}</div>
				</div>
				<div class="row">
					<div class="col-lg-8 col-sm-12 bg-light">{{summary.addressThird}}</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>


<div class="row">
	<div class="col-6"></div>
	<div class="col-6">
		<table class="table table-sm table-bordered mm-pyesil mm-print">
			<tbody>
				<tr class="bg-light text-center">
					<td class="col-6"><b>Com. for Crypto: </b>%{{ summary.cryptoCommissionRate }}</td>
					<td class="col-6"><b>Com. for Bank/MTC: </b>%{{ summary.bankMtcCommissionRate }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>


<table class="table table-sm mt-2 table-bordered mm-pyesil mm-print">
	<tbody>
		<tr class="bg-light">
			<td class="col-10"><b>Total Bank deposits:</b></td>
			<td class="text-right"><b>{{ summary.bankDeposit }}</b></td>
		</tr>
		<tr>
			<td><div><b>Total MTC deposits:</b></div></td>
			<td class="text-right"><b>{{ summary.mtcDeposit}}</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Total Crypto deposits:</b></td>
			<td class="text-right"><b>{{ summary.cryptoDeposit}}</b></td>
		</tr>
		<tr>
			<td><b>Total deposits:</b></td>
			<td class="text-right"><b>{{ summary.deposit}}</b></td>
		</tr>
	</tbody>
</table>

<table class="table table-sm mt-4 table-bordered mm-kirmizi mm-print">
	<tbody>
		<tr class="bg-light">
			<td class="col-10"><b>Total Bank withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.bankWithdraw }}</b></td>
		</tr>
		<tr>
			<td><b>Total MTC withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.mtcWithdraw}}</b></td>
		</tr>
		<tr class="bg-light">
			<td><b>Total Crypto withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.cryptoWithdraw}}</b></td>
		</tr>
		<tr>
			<td><b>Total withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.withdraw}}</b></td>
		</tr>
	</tbody>
</table>

<table class="table table-sm mt-4 table-bordered mm-pmavi mm-print">
	<tbody>
		<tr class="bg-light">
			<td class="col-10"><b>Total Bank/MTC deposits & withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.totalBankMtc }}</b></td>
		</tr>
		<tr>
			<td><b>Total Crypto deposits & withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.totalCrypto }}</b></td>
		</tr>
		<tr>
			<td><b>Total deposits & withdrawals:</b></td>
			<td class="text-right"><b>{{ summary.provision }}</b></td>
		</tr>
	</tbody>
</table>

<table class="table table-sm mt-4 table-bordered mm-pmavi mm-print">
	<tbody>
		<tr class="bg-light">
			<td class="col-10"><b>Bank/MTC Commission:</b></td>
			<td id="totalAmount" class="text-right"><b>{{ summary.commissionBankMtc }}</b></td>
		</tr>
		<tr>
			<td><b>Crypto Commission:</b></td>
			<td class="text-right"><b>{{ summary.commissionCrypto }}</b></td>
		</tr>
		<tr>
			<td><b>Total Commission:</b></td>
			<td class="text-right"><b>{{ summary.commission }}</b></td>
		</tr>
	</tbody>
</table>

<table class="table table-sm mt-4 table-bordered mm-pmavi mm-print">
	<tbody>
		<tr class="bg-light">
			<td class="col-10"><b>[Credit/Debit]</b></td>
			<td class="text-right"><b>{{ summary.requestedCredit || "" }}</b></td>
		</tr>
		<tr>
			<td><b>[Previous Credit]</b></td>
			<td class="text-right"><b>{{ summary.previousCredit || "" }}</b></td>
		</tr>
	</tbody>
</table>

<div class="row mt-4 p-2">
	<div class="col">
		<p><b>Note:</b> {{ invoiceSetting.note }}</p>
	</div>
</div>

<table class="table table-sm mt-4 table-bordered mm-print">
	<tbody>
		@if(summary.invoiceType!='ForexIB')
		<tr class="bg-light">
			<td colspan="2"><b>BANK TRANSFER</b></td>
		</tr>
		<tr>
			<td class="col-3"><b>Bank Name:</b></td>
			<td>{{ summary.bankName || "" }}</td>
		</tr>
		<tr class="bg-light">
			<td><b>Bank Address:</b></td>
			<td>{{ summary.bankAddress || ""}}</td>
		</tr>
		<tr>
			<td class="nowrap"><b>Account Name:</b></td>
			<td>{{ summary.accountName || ""}}</td>
		</tr>
		<tr class="bg-light">
			<td class="nowrap"><b>Beneficiary Address:</b></td>
			<td>{{ summary.beneficiaryAddress || ""}}</td>
		</tr>
		<tr>
			<td class="nowrap"><b>IBAN-Number:</b></td>
			<td>{{ summary.iban || ""}}</td>
		</tr>
		<tr class="bg-light mm-fs-12">
			<td><b>CHF Amount</b></td>
			<td class="mm-pkirmizi"><b>{{ summary.amount }}</b></td>
		</tr>
		@endif
	</tbody>
</table>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<div class="row mt-4">
	<div class="col-12 text-center"></div>
</div>

<table class="table table-striped table-sm mt-4 table-bordered">
	<thead>
		<tr>
			<th scope="col">Date</th>
			<th scope="col">Trader</th>
			<th scope="col">Type</th>
			<th scope="col">Gateway</th>
			<th scope="col" class="text-right">Amount</th>
			<th scope="col" class="text-right">Commission</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr class="dataLine">
			<td>{{ item.date }}</td>
			<td class="mm-fs-8">{{ item.login }} {{ item.name.substring(0, 20) }}</td>
			<td class="mm-fs-8">{{ item.typeName }}</td>
			<td >{{ item.gateway }}</td>
			<td class="text-right"><b>{{ item.amount }}</b></td>
			<td class="text-right"><b>{{ item.commission }}</b></td>
		</tr>
		@endeach
	</tbody>
</table>

</div>

@endsection


@section('script')
<script>
$( function() {
	  
});
</script>
@endsection
