@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, row: item.r } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@if(item.n != 'status')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
					icon: item.i, placeholder: item.c, enabled: item.e, row: item.r } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent


{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#{{data.length}}</span></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[4].c }}</th>
			<th scope="col" class="text-right mm-min">
				@if(auth.user.role==3)
					<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal"
					data-target="#newModal"><i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<td class="mm-fs-8 mm-kaydir mm-mavi">{{ item.key }}</td>
			<td class="mm-fs-8 mm-kaydir">{{ item.de }}</td>
			<td class="mm-fs-8 mm-kaydir">{{{ item.en }}}</td>
			<td class="text-right">
				@if(auth.user.role==3 || auth.user.role==2)
					<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.key }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@endsection

@section('script')
<script>

$( function() {
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[0].n}}']").focus()
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#key').val( '{{{ item.key }}}' )
			$('#de').html( '{{{ item.de }}}' )
			$('#fr').text( '{{{ item.fr }}}' )
			$('#it').text( '{{{ item.it }}}' )
			$('#en').text( '{{{ item.en }}}' )
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif

});
</script>
@endsection
