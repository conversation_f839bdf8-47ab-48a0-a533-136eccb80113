@layout('layout.core')

@section('title')

@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	<div class="row">
		<div class="col-10 col-md-5 col-lg-3 text-center">
			<div class="card">
				<div class="card-body">
					<form action="{{ route('checkInvitation') }}" class="form" method="POST">
						{{ csrfField() }}
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-tag fa-fw"></i></div>
							</div>
							<input type="text" class="form-control {{ elIf('is-invalid','', hasErrorFor('yourId')) }}" value="{{ old('yourId') }}" id="yourId" name="yourId" placeholder="{{ yourIdCaption }}" autofocus>
							{{ elIf('<div class="invalid-feedback">$self</div>', getErrorFor('yourId'), hasErrorFor('yourId')) }}
						</div>
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-tag fa-fw"></i></div>
							</div>
							<input type="text" class="form-control {{ elIf('is-invalid','', hasErrorFor('friendId')) }}" value="{{ old('friendId') }}" id="friendId" name="friendId" placeholder="{{ friendIdCaption }}" autofocus>
							{{ elIf('<div class="invalid-feedback">$self</div>', getErrorFor('friendId'), hasErrorFor('friendId')) }}
						</div>
						
						<div class="input-group">
							<button type="submit" class="btn btn-primary mx-auto">{{ buttonCaption }}</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
	<br/>
	<div class="row">
		<div class="col-12 col-sm-12 col-md-5 col-lg-4 mx-auto">
		@if(old('error'))
			<div class="alert alert-danger fade show" role="alert">
				<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
			</div>
		@endif

		@if(old('info'))
			@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
				@slot('strong')
					<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
				@endslot
			@endcomponent
		@endif
		</div>
	</div>

@endsection