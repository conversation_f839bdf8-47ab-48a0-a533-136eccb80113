@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action:'createUser', caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
			hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n + 'New', caption: caption,
			icon: item.i, placeholder: item.c, enabled: item.e } )
		@endeach
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, caption: caption,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3 mb-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[2].c }}</th>
			<th scope="col" class="text-right">
				@if(auth.user.role==3)
				<a href="{{ route('register') }}" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true"><i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th scope="row"><span class="mm-mavi"><i class="fas fa-user"></i></span></th>
			<td>{{ item.username }}</td>
			<td>{{ item.email }}</td>
			<td class="ro">{{ item.role }}</td>
			<td class="text-right mm-min">
				@if(auth.user.role==3 || auth.user.role==2)
				<a href="#" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.username }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@endsection

@section('script')
<script>
$( function() {
	const roles = {{{ toJSON(roles) }}}
	$('.ro').each(function(index, item){
		let id = $(this).text()
		if(id && id!="null"){
			let role = roles.find(i => i.id==id)
			$(this).html(role.title)
		}
	})

	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='title']").focus()
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='/users/' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='/users/' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		//range(totalNumberOfPages)
		if (id=='{{ item.id }}') {
			$('#username').val( '{{item.username}}' )
			$('#email').val( '{{item.email}}' )
			$('#role').val( '{{item.role}}' )
			$('#password').val( '{{item.password}}' )
			{{item.active}} == 1 ? $('#editModal #active').click() : $('#editModal #passive').click()
		}
		@endeach
		setTimeout(()=>{
			$('#username').focus()
		}, 500)
	})

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif
	@if(old('editModalShow'))
		$('#editModal').modal('show')
	@endif

	$('input[type="file"]').change(function(e){
        var fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
    });
});
</script>
@endsection
