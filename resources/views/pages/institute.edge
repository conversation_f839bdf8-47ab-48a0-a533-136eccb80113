@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'modal-lg', action:'createInstitute', caption: caption, csrfField: csrfField })
	<div class="col-md-6">
		@each(item in fields.slice(0, 12))
			@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
			hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n + 'New', caption: caption,
			icon: item.i, placeholder: item.c, enabled: item.e } )
		@endeach
	</div>
	<div class="col-md-6">
		@each(item in fields.slice(12, 24))
			@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
			hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n + 'New', caption: caption,
			icon: item.i, placeholder: item.c, enabled: item.e } )
		@endeach
		@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'modal-lg', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-6">
			@each(item in fields.slice(0, 12))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, caption: caption,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
		</div>
		<div class="col-md-6">
			@each(item in fields.slice(12, 25))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, caption: caption,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
			@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[2].c }}</th>
			<th scope="col">{{ fields[3].c }}</th>
			<th scope="col">{{ fields[4].c }}</th>
			<th scope="col">{{ fields[5].c }}</th>
			<th scope="col">{{ fields[7].c }}</th>
			<th scope="col">{{ fields[8].c }}</th>
			<th scope="col"></th>
			<th scope="col" class="text-right">
				@if(auth.user.role==3)
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal"
					data-target="#newModal"><i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th scope="row"><span class="mm-mavi"><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.title }}</td>
			<td><img class="zoom" src="FinancialLogos/{{item.icon}}" alt="" height="30px"></td>
			<td>{{ item.account_opening_site }}</td>
			<td>{{ item.account_opening_duration }}</td>
			<td>{{ item.max_leverage_factor }}</td>
			<td>{{ item.markup }}</td>
			<td>{{ item.correspondence_language }}</td>
			<td>{{ item.competition_participation }}</td>
			<td>{{ (item.active===1) ? caption.active : caption.passive }}</td>
			<td class="text-right mm-min">
				@if(auth.user.role==3 || auth.user.role==2)
				<a href="#" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.title }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@endsection

@section('script')
<script>
$( function() {
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='title']").focus()
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='/institutes/' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='/institutes/' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		//range(totalNumberOfPages)
		if (id=='{{ item.id }}') {
			$('#title').val( '{{item.title}}' )
			$('#icon').val( '{{item.icon}}' )
			$('#iconLabel').text( '{{item.icon}}' )
			$('#account_opening_site').val( '{{item.account_opening_site}}' )
			$('#account_opening_duration').val( '{{item.account_opening_duration}}' )
			$('#max_leverage_factor').val( '{{item.max_leverage_factor}}' )
			$('#markup').val( '{{item.markup}}' )
			$('#payment_transactions').val( '{{item.payment_transactions}}' )
			$('#correspondence_language').val( '{{item.correspondence_language}}' )
			$('#competition_participation').val( '{{item.competition_participation}}' )
			$('#commercial_products').val( '{{item.commercial_products}}' )
			$('#email').val( '{{item.email}}' )
			$('#page_link').val( ('{{item.link}}').replace('&amp;', '&').replace('amp;','') )
			$('#signup_finish_link').val( '{{item.signup_finish_link}}' )
			$('#server_host').val( '{{item.server_host}}' )
			$('#server_port').val( '{{item.server_port}}' )
			$('#server_user').val( '{{item.server_user}}' )
			$('#server_pass').val( '{{item.server_pass}}' )
			$('#server_db').val( '{{item.server_db}}' )
			$('#server_table').val( '{{item.server_table}}' )
			$('#loginField').val( '{{item.loginField}}' )
			$('#emailField').val( '{{item.emailField}}' )
			$('#fillFormTemplate').val( '{{item.fillFormTemplate}}' )
			$('#onlineRegister').val( '{{item.onlineRegister}}' )
			$('#order').val( '{{item.order}}' )
			$('#kycMail').val( '{{item.kycMail}}' )
			{{item.active}} == 1 ? $('#editModal #active').click() : $('#editModal #passive').click()
		}
		@endeach
		setTimeout(()=>{
			$('#title').focus()
		}, 500)
	})

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif
	
	@if(old('editModalShow'))
		$('#editModal').modal('show')
	@endif

	$('input[type="file"]').change(function(e){
        var fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
    });
});
</script>
@endsection
