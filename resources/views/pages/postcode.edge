@layout('layout.main')

@section('content')
	<h3>{{ title }} <span class="badge bg-info badge-pill">{{ page.data.length }}</span></h3>

	<table class="table table-striped table-sm">
		<thead>
			<tr>
				<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col">Code</th>
			<th scope="col">Place</th>
			<th scope="col">Kanton</th>
			<th scope="col">Canton</th>
			<th scope="col">Cantone</th>
			<th scope="col">AB</th>
			<th scope="col"></th>
			</tr>
		</thead>
		<tfoot><tr><td colspan="8" class="">
			<nav aria-label="Page navigation">
				<ul class="pagination pagination-sm justify-content-center">
					<li class="page-item disabled">
						<a class="page-link" href="#" tabindex="-1">
							<span aria-hidden="true">&laquo;</span>
						</a>
					</li>
					@each(i in range(10))
					<li class="page-item"><a class="page-link" href="{{ route('getPostcodesPage', {page: $loop.index + 1 } ) }}">{{ $loop.index + 1 }}</a></li>
					@endeach
					<li class="page-item">
						<a class="page-link" href="#">
							<span aria-hidden="true">&raquo;</span>
						</a>
					</li>
				</ul>
			</nav>
		</td></tr></tfoot>
		<tbody>
			@each(item in page.data)
				<tr>
					<td>{{ ($loop.index + 1) }}</td>
					<th scope="row">{{ item.code }}</th>
					<td>{{ item.place }}</td>
					<td>{{ item.kanton }}</td>
					<td>{{ item.canton }}</td>
					<td>{{ item.cantone }}</td>
					<td>{{ item.ab }}</td>
					<td></td>
				</tr>
			@endeach
		</tbody>
	</table>

@endsection
