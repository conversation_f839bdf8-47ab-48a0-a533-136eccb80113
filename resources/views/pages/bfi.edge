@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Image Preview Modal  --}}
@component('components.modal', { type:'imagePreview', id:'imagePreviewModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12 text-center mt-3">
			<img class="img-fluid preview-zoom" id="imagePreviewModalImage" src="" alt="Image preview" height="600px">
		</div>
	</div>
@endcomponent

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'modal-lg', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-6">
		@each(item in fields.slice(0,13))
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v, label: item.l  } )
			@endif
		@endeach
	</div>
	<div class="col-md-6">
		@each(item in fields.slice(13,25))
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v, label: item.l  } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'modal-lg', action:'', caption: caption, csrfField: csrfField })
<div class="container-fluid">	
	<div class="row">
		<div class="col-md-6">
			@each(item in fields.slice(0,17))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v, label: item.l } )
			@endeach
		</div>
		<div class="col-md-6">
			@each(item in fields.slice(17,34))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v, label: item.l } )
			@endeach
		</div>
	</div>
	<div class="row mt-3">
		<div class="col-md-6">
			@each(item in loginFields)
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v, label: item.l } )
			@endeach
		</div>
		<div class="col-md-6">
			@each(item in fields.slice(34,36))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v, label: item.l } )
			@endeach
		</div>
	</div>
	<div class="row mt-3">
		<div class="col-md-12">
			@each(item in linkFields)
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v, label: item.l } )
			@endeach
		</div>
	</div>
</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{-- Query Modal --}}
@component('components.modal', { type:'query', id:'queryModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in queryFields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, 
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Sub Menu  --}}
<div class="container-fluid mt-3">
	<ul class="nav nav-pills">
		<li class="nav-item no-line">
			<a class="nav-link {{ (currentQuery.type==1) ? 'active' : '' }}" href="?type=1">Active</a>
		</li>
		<li class="nav-item no-line">
			<a class="nav-link {{ (currentQuery.type==3) ? 'active' : '' }}" href="?type=3">Passive</a>
		</li>
		<li class="nav-item no-line">
			<a class="nav-link {{ (currentQuery.type==2) ? 'active' : '' }}" href="?type=2">Prospect</a>
		</li>
	  </ul>
</div>

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">	
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col" class="d-none d-lg-table-cell"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col" class="d-none d-md-table-cell"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col" class="d-none d-lg-table-cell">{{ fields[5].c }}</th>
			<th scope="col" class="d-none d-lg-table-cell">{{ fields[6].c }}</th>
			<th scope="col" >{{ fields[4].c }}</th>
			<th scope="col" class="d-none d-md-table-cell">{{ fields[16].c }}</th>
			<th scope="col" class="text-right">
				@if(currentQuery.type==2)
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal" data-target="#newModal">
					<i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
				<a id="btnQuery" href="#" role="button" class="btn btn-outline-info btn-sm btnQuery" aria-pressed="true" data-toggle="modal"
					data-target="#queryModal"><i class="fas fa-search"></i></a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td class="d-none d-lg-table-cell">{{ item.id }}</td>
			<th scope="row" class="d-none d-md-table-cell"><span class="{{ item.isActive==1? 'text-success' : 'text-danger'}}"><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.businessName.substring(0,20) }}</td>
			<td class="d-none d-lg-table-cell">{{ item.contactName.substring(0,20)  }}</td>
			<td class="d-none d-lg-table-cell">{{ item.contactPhone }}</td>
			<td class="">{{ item.city }}</td>
			<td class="cat d-none d-md-table-cell">{{ item.category }}</td>
			<td class="text-right">
				@if((item.type!=2) && (auth.user.role==3 || auth.user.role==2))
				<a href="{{ route('queryTerminalByBfi', {bId: item.id}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-primary btn-sm btnTransfer" aria-pressed="true"
					><i class="fas fa-hand-holding-usd"></i></a>
				@endif
				
				@if((item.type==2) || (auth.user.role==3 || auth.user.role==2))
				<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
				
				@if(item.type==2 || (item.type!=2 && auth.user.role==3))
				<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.businessName }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
					data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

<div class="row align-items-center" id="HiddenFileRow" hidden>
	<form id="fileUploadForm" action="{{ route('bfiFileUpload') }}" class="form" method="POST" enctype="multipart/form-data" data-id="">
		{{ csrfField() }}
		<input id="fileUploadBfiId" type="hidden" name="id" value="">
		<input id="fileUploadBfiField" type="hidden" name="field" value="">
		<div class="col">
			<div class="input-group">
				<div class="input-group-prepend">
					<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
				</div>
				<div class="custom-file">
					<input type="file" class="form-control custom-file-input"
							id="fileUploadInput" name="fileUploadFile" data-toggle="tooltip" data-placement="top" 
							title="File Upload" accept=".jpg,.jpeg,.png,.pdf">
					<label id="fileUploadLabel" class="custom-file-label text-truncate" for="fileUploadInput" data-browse="Browse">File Upload</label>
					<input type="hidden" id="fileUpload" name="fileUpload" value="">
				</div>
			</div>
		</div>
	</form>
</div>

@endsection

@section('script')
<script>
$( function() {
	
	const category = {{{ toJSON(bfiCategories) }}}
	$('.cat').each(function(index, item){
		let id = $(this).text()
		if(id && id!="null"){ 
			let cat = category.find(x => x.id==id)
			$(this).html(cat.title)
		}
	})
	
	function getFileTypeFromPath(path) {
		var extension = path.split(".").pop();
		return extension.toLowerCase();
	}
	
	let imageField="";
	
	$('.btnImage').click(function(e) {
		e.preventDefault();
		//'https://terminaltest.tradecenter24.com/bfi/70/test1.jpg'
		var fileUrl = '{{caption.imgPath}}' + $(this).attr('data-src');
		
		const fileType = getFileTypeFromPath(fileUrl);

		if (fileType === "pdf") {
			$('#commercialRegisterButton').removeAttr('data-toggle')
			$('#commercialRegisterButton').removeAttr('data-target')
			window.open(fileUrl, "_blank");
		} else if (fileType === "jpg" || fileType === "jpeg" || fileType === "png") {
			$('#imagePreviewModalImage').attr('src', fileUrl);
			$('#imagePreviewModal').modal('show');
			$('#imagePreviewModal').css('z-index', '9999');
		}
	});
	
	$('#fileUploadInput').change(function(e){
        const fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
		takeAndSend(imageField);
    });
	
	function takeAndSend(field){
		const fileUpload = $('#fileUpload').val();
		if (fileUpload == ''){
			
		} else {
			const id = $('#fileUploadForm').attr('data-id')
			$('#fileUploadBfiId').val(id)
			$('#fileUploadBfiField').val(field)
			
			$("#fileUploadForm").submit();
		}
	}
	
	$('.btnImageFile').click(function() {
		var field = $(this).attr('data-field'); //e_speedtest1
		imageField = field;
		$("#fileUploadLabel").click()
	})
	
	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})

	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[0].n}}']").focus()
			$("#n_location").prop("selectedIndex", 1)
			$("#n_category").prop("selectedIndex", 1)
			$("#n_access").prop("selectedIndex", 1)
			$("#n_premisesClass").prop("selectedIndex", 1)
			$('#n_speedUpload').val(0)
			$('#n_speedDownload').val(0)
			$('#n_socket').val(0)
			$('#n_distanceRouter').val(0)
			$('#n_seats').val(0)
			$('#n_insuranceAmount').val(0)
			$('#n_customerFrequency').val(0)
			$("#n_type").prop("selectedIndex", 1)
		}, 500)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#e_businessName').val('{{ item.businessName }}')
			$('#e_companyName').val('{{ item.companyName }}')
			$('#e_street').val('{{ item.street }}')
			$('#e_zip').val('{{ item.zip }}')
			$('#e_city').val('{{ item.city }}')
			$('#e_contactName').val('{{ item.contactName }}')
			$('#e_contactPhone').val('{{ item.contactPhone }}')
			$('#e_contactEmail').val('{{ item.contactEmail }}')
			//$('#e_speedUpload').val('{{ item.speedUpload }}')
			$('#e_speedDownload').val('{{ item.speedDownload }}')
			$('#e_distanceRouter').val('{{ item.distanceRouter }}')
			$('#e_socket').val('{{ item.socket }}')
			$('#e_router').val('{{ item.router }}')
			$('#e_providerName').val('{{ item.providerName }}')
			$('#e_location').val('{{ item.location }}')
			$('#e_seats').val('{{ item.seats }}')
			$('#e_customerFrequency').val('{{ item.customerFrequency }}')
			$('#e_category').val('{{ item.category }}')
			$('#e_access').val('{{ item.access }}')
			$('#e_transport').val('{{ item.transport }}')
			$('#e_salesCounter').val('{{ item.salesCounter }}')
			$('#e_insuranceName').val('{{ item.insuranceName }}')
			$('#e_insuranceAmount').val('{{ item.insuranceAmount }}')
			$('#e_premisesClass').val('{{ item.premisesClass }}')
			
			$('#e_lat').val('{{ item.lat }}')
			$('#e_long').val('{{ item.long }}')
			
			$('#e_isActive').val('{{ item.isActive }}')
			$('#e_showUrl').val('{{ item.showUrl }}')
			$('#e_login').val( '{{ item.login }}' )
			$('#e_sagId').val( '{{ item.sagId }}' )
			$('#e_type').val( '{{ item.type }}' )
			
			$('#fileUploadForm').attr('data-id','{{ item.id }}')
			
			const speedtest1 = '{{item.speedtest1}}'
			if(speedtest1!='null'){
				$('#e_speedtest1').val(speedtest1)
				$('#e_speedtest1Button').attr('data-src', speedtest1)
			}
			const speedtest2 = '{{item.speedtest2}}'
			if(speedtest2!='null'){
				$('#e_speedtest2').val(speedtest2)
				$('#e_speedtest2Button').attr('data-src', speedtest2)
			}
			const speedtest3 = '{{item.speedtest3}}'
			if(speedtest3!='null'){
				$('#e_speedtest3').val(speedtest3)
				$('#e_speedtest3Button').attr('data-src', speedtest3)
			}
			const localInterior = '{{item.localInterior}}'
			if(localInterior!='null'){
				$('#e_localInterior').val(localInterior)
				$('#e_localInteriorButton').attr('data-src', localInterior)
			}
			const localExterior = '{{item.localExterior}}'
			if(localExterior!='null'){
				$('#e_localExterior').val(localExterior)
				$('#e_localExteriorButton').attr('data-src', localExterior)
			}
			const commercialRegister = '{{item.commercialRegister}}'
			if(commercialRegister!='null'){
				$('#e_commercialRegister').val(commercialRegister)
				$('#e_commercialRegisterButton').attr('data-src', commercialRegister)
			}
			const idPass1 = '{{item.idPass1}}'
			if(idPass1!='null'){
				$('#e_idPass1').val(idPass1)
				$('#e_idPass1Button').attr('data-src', idPass1)
			}
			const idPass2 = '{{item.idPass2}}'
			if(idPass2!='null'){
				$('#e_idPass2').val(idPass2)
				$('#e_idPass2Button').attr('data-src', idPass2)
			}
			
			@each(subItem in item.loginList)
				$('#e_login{{subItem.instituteId}}').val( '{{subItem.login}}' )
			@endeach
			@each(subItem in item.linkList)
				$('#e_link{{subItem.instituteId}}').val( ('{{subItem.link}}').replace('&amp;', '&').replace('amp;','') )
			@endeach
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	$("#btnQuery").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.queryRoute}}' + '?_method=PATCH'
		$('#queryForm').attr('action', url)

		const currentQuery = {{{ toJSON(currentQuery) }}}
		$('#location').val( currentQuery.location || 0)
		$('#category').val( currentQuery.category || 0)
		$('#access').val( currentQuery.access || 0)
		$('#type').val( currentQuery.type || 0)

		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})
	
	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[4].innerText.toString(),
						row.cells[5].innerText.toString(),
						row.cells[6].innerText.toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "BFI_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
	@if(old('queryModalShow'))
		$("#btnQuery").click()
	@endif

	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif
	
});
</script>
@endsection
