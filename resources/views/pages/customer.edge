@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Image Preview Modal  --}}
@component('components.modal', { type:'imagePreview', id:'imagePreviewModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12 text-center mt-3">
			<img class="img-fluid preview-zoom" id="imagePreviewModalImage" src="" alt="Image preview" height="600px">
		</div>
	</div>
@endcomponent

{{--  Info Modal  --}}
@component('components.modal', { type:'info', id:'infoModal', lg:'', action:'', title: caption.infoTitle, caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in infoFields.slice(0,17))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
		</div>
	</div>
@endcomponent

{{--  Answer Modal  --}}
@component('components.modal', { type:'info', id:'answerModal', lg:'', action:'', title: caption.answerTitle, caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in questionFields.slice(0,17))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e } )
			@endeach
		</div>
	</div>
@endcomponent

{{--  SMS Modal  --}}
@component('components.modal', { type:'sendSms', id:'sendSmsModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.sendSmsWarning }}}
	<strong><div id="sendSmsRecord"></div></strong>
@endcomponent

{{--  Custom SMS Modal  --}}
@component('components.modal', { type:'sendCustomSms', id:'sendCustomSmsModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.sendSmsWarning }}}
	<strong><div id="sendCustomSmsRecord"></div></strong>
	<div class="row mt-3">
		<div class="col-md-12">
			<div class="input-group mb-2">
				<textarea class="form-control" id="sendCustomSmsMessage" rows="5" name="sendCustomSmsMessage" placeholder="Message"
				data-toggle="tooltip" data-placement="top" title="Message" required>{{ oldValue }}</textarea>
			</div>
			<div class="input-group mb-2">
				<a  href="#" role="button" id="btnKeywordTraderId" class="btn btn-outline-info btn-sm ml-2" aria-pressed="true">TraderID</a>
				<a  href="#" role="button" id="btnKeywordName" class="btn btn-outline-info btn-sm ml-2" aria-pressed="true">Name</a>
				<a  href="#" role="button" id="btnKeywordAppUrl" class="btn btn-outline-info btn-sm ml-2" aria-pressed="true">AppUrl</a>
			</div>
		</div>
	</div>
@endcomponent

{{--  Multiple SMS Modal  --}}
@component('components.modal', { type:'sendMultiSms', id:'sendMultiSmsModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.sendMultiSmsWarning }}}
	<div class="row mt-3">
		<div class="col-md-12">
			<div class="input-group mb-2">
				<textarea class="form-control" id="sendMultiSmsMessage" rows="5" name="sendMultiSmsMessage" placeholder="Message"
				data-toggle="tooltip" data-placement="top" title="Message" required>{{ oldValue }}</textarea>
			</div>
		</div>
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField, saveDisabled: (auth.user.role!=2 && auth.user.role!=3) })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields.slice(0,12))
			@if(item.n != 'gender' && item.n != 'terminalCode')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
			@endif
			@endeach
			
			@each(item in fields.slice(12,16))
				<div class="row">
					<div class="col-9">
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
						hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
						icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
					</div>
				</div>
			@endeach
			
			@each(item in fields.slice(16,17))
				<div class="row">
					<div class="col-9">
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
						hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
						icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
					</div>
					<div class="col-3 text-right">
						<a href="#" id="btn{{item.n}}" role="button" class="btn btn-outline-info btn-sm btnRiskCheck">Check Risk</i></a>
					</div>
				</div>
			@endeach
			@each(item in fields.slice(17,18))
				<div class="row">
					<div class="col-9">
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
						hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
						icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
					</div>
					<div class="col-3 text-right">
						<a href="#" id="btn{{item.n}}" role="button" data-id="" class="btn btn-outline-info btn-sm btnInfo">Kyc</i></a>
					</div>
				</div>
			@endeach
			@each(item in fields.slice(18,19))
				<div id="utilityBillRow">
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				</div>
			@endeach
			@each(item in fields.slice(19,21))
				<div class="row">
					<div class="col-9">
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
						hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
						icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
					</div>
					<div class="col-3 text-right">
						<a href="#" id="btn{{item.n}}" role="button" data-id="" class="btn btn-outline-info btn-sm btnInfo">Kyc</i></a>
					</div>
				</div>
			@endeach
			@each(item in fields.slice(21,22))
				<div id="contractRow">
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				</div>
			@endeach
			
			@each(item in fields.slice(22,25))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
			@endeach
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{-- Query Modal --}}
@component('components.modal', { type:'query', id:'queryModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in queryFields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n, 
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col" class="d-none d-lg-table-cell"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col" class="d-none d-sm-table-cell">KYC</th>
			<th scope="col" class="d-none d-md-table-cell">Status</th>
			<th scope="col" class="d-none d-table-cell">Name</th>
			<th scope="col" class="d-none d-lg-table-cell">Phone</th>
			<th scope="col" class="d-none d-lg-table-cell">Institute</th>
			<th scope="col" class="d-none d-xl-table-cell">Group</th>
			<th scope="col" class="d-none d-md-table-cell">Trader ID</th>
			<th scope="col" class="d-none d-xl-table-cell">Date</th>
			<th scope="col" class="d-none d-table-cell">
				@if((auth.user.role==3))
				<a href="#" role="button" id="btnSendMultiSms" class="btn btn-outline-success btn-sm" aria-pressed="true" data-toggle="modal"
					data-target="#sendMultiSmsModal"><i class="fas fa-sms"></i></a>
				@endif
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
				<a id="btnQuery" href="#" role="button" class="btn btn-outline-info btn-sm btnQuery position-relative" aria-pressed="true" data-toggle="modal"
					data-target="#queryModal"><i class="fas fa-search"></i>
					<span class="mm-beyaz position-absolute badge rounded-pill bg-info">{{data.length}}</span>
				</a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr class="dataLine">
			<td class="d-none d-lg-table-cell">{{ item.id }}</td>
			<td scope="row" class="d-none d-sm-table-cell">
				@if(item.kycIdentification==0 && item.kycAddressValidation==0 && item.kycUtilityBill==0 && item.kycChatbot==0)
					<span class="mm-gri"><i class="fas fa-circle"></i></span>
				@elseif(item.kycIdentification==1 && item.kycAddressValidation==1 && item.kycUtilityBill==1 && item.kycChatbot==1)
					@if(item.pdfOk)
						<span class="mm-tamyesil"><i class="fas fa-circle"></i></span>
					@else
						<span class="mm-mavi"><i class="fas fa-circle"></i></span>
					@endif
				@else
					<span class="text-warning"><i class="fas fa-circle"></i></span>
				@endif
				&nbsp;
				@if(item.financeinstitute!=0 && item.deposit>=2500)
					<span class="mm-normal">D</span>
				@endif
				@if(item.financeinstitute!=0 && item.deposit<2500 && item.kycForce==1)
					<span class="mm-normal">W</span>
				@endif
			</td>
			<td class="d-none d-md-table-cell">
				<i class="fas fa-exclamation {{ item.kycUtilityBill == 1 ? 'mm-tamyesil' : 'mm-gri' }}"></i>
				<i class="fas fa-home {{ item.kycAddressValidation == 1 ? 'mm-tamyesil' : 'mm-gri' }}"></i>
				<i class="fas fa-id-card {{ item.kycIdentification == 1 ? 'mm-tamyesil' : 'mm-gri' }}"></i>
				<i class="fas fa-file {{ item.kycChatbot == 1 ? 'mm-tamyesil' : 'mm-gri' }}"></i>
			</td>
			<td class="d-none d-table-cell">{{ item.surname.substring(0,20) + ' ' + item.name.substring(0,20) }}</td>
			<td class="d-none d-lg-table-cell mm-fs-8"><a href="tel:{{ item.phone }}" class="mm-mavi">{{ item.phone }}</a></td>
			<td class="d-none d-lg-table-cell mm-fs-8 fi">{{ item.financeinstitute }}</td>
			<td class="d-none d-xl-table-cell mm-fs-8 ">{{ item.groupName }}</td>
			<td class="d-none d-md-table-cell">{{ item.login }}</td>
			<td class="d-none d-xl-table-cell mm-fs-8">{{ item.created_date }}</td>
			<td class="d-none d-table-cell">
				<div class="btnColumn text-right">
					@if(1==2)
						@if(item.kycIdentificationDescription!=null)
						<a href="#" id="btnInfo{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnInfo rounded-pill" aria-pressed="true" data-toggle="modal"
						data-target="#infoModal">IDs</i></a>
						@endif
						@if(item.hasAnswer==true)
						<a href="#" id="btnAnswer{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnAnswer rounded-pill" aria-pressed="true" data-toggle="modal"
						data-target="#answerModal">WB Form</a>
						@endif
					@endif
					
					@if((auth.user.role==3 || auth.user.role==2) && item.phone)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.phone }}" class="btn btn-outline-success btn-sm btnSendCustomSms" aria-pressed="true" data-toggle="modal"
						data-target="#sendCustomSmsModal"><i class="fas fa-sms"></i></a>
					@endif
					@if(auth.user.role==3 || auth.user.role==2)
					<a href="{{ route('queryTransfersByLogin', {login: item.login}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-primary btn-sm btnTransfer" aria-pressed="true"
						><i class="fas fa-hand-holding-usd"></i></a>
					@endif
					@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==12)
					<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
						data-target="#editModal"><i class="fas fa-pen"></i></a>
					@endif
					@if(auth.user.role==3)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.email }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
						data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
					@endif
					
				</div>
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

<div class="row align-items-center" id="HiddenFileRow" hidden>
	<form id="utilityBillForm" action="{{ route('utilityBillUpload') }}" class="form" method="POST" enctype="multipart/form-data">
		{{ csrfField() }}
		<input id="utilityBillId" type="hidden" name="id" value="">
		<div class="col">
			<div class="input-group">
				<div class="input-group-prepend">
					<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
				</div>
				<div class="custom-file">
					<input type="file" class="form-control custom-file-input"
							id="utilityBillInput" name="utilityBillFile" data-toggle="tooltip" data-placement="top" 
							title="Utility Bill" accept=".jpg,.jpeg,.png,.pdf">
					<label id="utilityBillLabel" class="custom-file-label text-truncate" for="utilityBillInput" data-browse="Browse">Utility Bill</label>
					<input type="hidden" id="utilityBill" name="utilityBill" value="">
				</div>
			</div>
		</div>
	</form>
</div>

@endsection

@section('script')
<script>
$( function() {
	const institutes = {{{ toJSON(fields[7].v) }}}
	$('.fi').each(function(index, item){
		let id = $(this).text()
		if(id && id!="null"){ //null gelirse institute id bilgisi
			let institute = institutes.find(i => i.id==id)
			if(institute){
				$(this).html(institute.title)
			}else{
				$(this).html('null')
			}
		}
	})
	
	/*
	$(".btnColumn").hide()
	$(".dataLine").hover( function() {
		$(".btnColumn").hide()
		$(this).find(".btnColumn").show()
	})
	*/
	
	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})
	
	$(".btnSendSms").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url = '{{caption.sendSmsRoute}}/' + id + '?_method=POST'
		$('#sendSmsForm').attr('action', url)
		$('#sendSmsRecord').html(item)
	})
	
	$(".btnSendCustomSms").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url = '{{caption.sendCustomSmsRoute}}/' + id + '?_method=POST'
		$('#sendCustomSmsForm').attr('action', url)
		$('#sendCustomSmsRecord').html(item)
		$("#btnKeywordTraderId").attr('data-id', id)
	})
	
	$("#btnSendMultiSms").click( function() {
		var url = '{{caption.sendMultiSmsRoute}}' + '?_method=POST'
		$('#sendMultiSmsForm').attr('action', url)
	})
	
	$("#btnKeywordTraderId").click( function() {
		var id = $(this).attr('data-id')
		let message = $('#sendCustomSmsMessage').val()
		$('#sendCustomSmsMessage').val(message + " [TraderID] ")
	})
	$("#btnKeywordName").click( function() {
		var id = $(this).attr('data-id')
		let message = $('#sendCustomSmsMessage').val()
		$('#sendCustomSmsMessage').val(message + " [Name] ")
	})
	$("#btnKeywordAppUrl").click( function() {
		var id = $(this).attr('data-id')
		let message = $('#sendCustomSmsMessage').val()
		$('#sendCustomSmsMessage').val(message + " [AppUrl] ")
	})

	$(".btnAnswer, #btnkycChatbot").click(function() {
		var id = $(this).attr('data-id')
		
		@each(item in dataAnswer)
		if (id=='{{ item.personId }}') {
			$('#answer1').val('{{item.answer1}}')
			$('#answer2').val('{{item.answer2}}')
			$('#answer3').val('{{item.answer3}}')
			$('#answer4').val('{{item.answer4}}')
			$('#answer5').val('{{item.answer5}}')
			$('#answer6').val('{{item.answer6}}')
			$('#answer7').val('{{item.answer7}}')
			$('#answer8').val('{{item.answer8}}')
			$('#answer9').val('{{item.answer9}}')
			$('#answer10').val('{{item.answer10}}')
			$('#answer11').val('{{item.answer11}}')
			$('#answer12').val('{{item.answer12}}')
			$('#answer13').val('{{item.answer13}}')
			$('#answer14').val('{{item.answer14}}')
		}
		@endeach
		setTimeout(()=>{
			$('#{{questionFields[0].n}}').focus()
		}, 500)
	})
	
	function getFileTypeFromPath(path) {
		var extension = path.split(".").pop();
		return extension.toLowerCase();
	}
	
	$('.btnImage').click(function(e) {
		e.preventDefault();
		//'https://terminaltest.tradecenter24.com/images/uploads/70/s__e49f053c-cdfc-4f04-b4be-eac3b7f0a2e0.jpg'
		var fileUrl = '{{caption.imgPath}}' + $(this).attr('data-src');
		
		const fileType = getFileTypeFromPath(fileUrl);

		if (fileType === "pdf") {
			$('#utilityBillFotoButton').removeAttr('data-toggle')
			$('#utilityBillFotoButton').removeAttr('data-target')
			$('#contractButton').removeAttr('data-toggle')
			$('#contractButton').removeAttr('data-target')
			window.open(fileUrl, "_blank");
		} else if (fileType === "jpg" || fileType === "jpeg" || fileType === "png") {
			$('#imagePreviewModalImage').attr('src', fileUrl);
			$('#imagePreviewModal').modal('show');
			$('#imagePreviewModal').css('z-index', '9999');
		}
	});
	
	$('#infoModal input[type="checkbox"]').change(function() {
		let id = $('#infoModal').attr('data-id')
		let infoData = {
			isValid: $('#kycApiIsValid').is(':checked'),
			frontIsValid: $('#kycApiFrontIsValid').is(':checked'),
			backIsValid: $('#kycApiBackIsValid').is(':checked'),
			frontFullName: $('#kycApiFrontFullName').is(':checked'),
			frontBirthDate: $('#kycApiFrontBirthDate').is(':checked'),
			frontNationality: $('#kycApiFrontNationality').is(':checked'),
			frontGender: $('#kycApiFrontGender').is(':checked'),
			backFullName: $('#kycApiBackFullName').is(':checked'),
			backBirthDate: $('#kycApiBackBirthDate').is(':checked'),
			backNationality: $('#kycApiBackNationality').is(':checked'),
			backGender: $('#kycApiBackGender').is(':checked'),
		}
		
		//ajax ile request atılırsa güvenliğe takılmamak için csrf token ekle
		var csrf_token = "{{ csrfToken }}"
		$.ajaxPrefilter(function (options, originalOptions, jqXHR) {
            jqXHR.setRequestHeader('csrf-token', csrf_token);
		});
		
		$.getJSON('/customer/updateInfo/' + id, infoData, (data) =>{ 
			let badgeStatus=$('#infoStatusBadge')
			badgeStatus.text(data.info)
			
			if(data.success) {
				badgeStatus.addClass('badge-success')
				setTimeout(function() {
					badgeStatus.text('')
				}, 1000)
			}else{
				badgeStatus.addClass('badge-danger')
			}
		});
	})
	
	$(".btnInfo").click(function() {
		var id = $(this).attr('data-id')
		$('#infoModal').attr('data-id', id)
		
		@each(item in dataInfo)
		if (id=='{{ item.id }}') {
			$('#kycIdentificationRate').val('{{item.kycIdentificationRate}}')
			$('#kycIdCardFront').val('{{item.kycIdCardFront}}')
			$('#kycIdCardBack').val('{{item.kycIdCardBack}}')
			$('#kycIdCardFoto').val('{{item.kycIdCardFoto}}')
			$('#kycIdCardSelfie').val('{{item.kycIdCardSelfie}}')
			$('#kycIdCardFrontButton').attr('data-src', '{{item.kycIdCardFront}}')
			$('#kycIdCardBackButton').attr('data-src', '{{item.kycIdCardBack}}')
			$('#kycIdCardFotoButton').attr('data-src', '{{item.kycIdCardFoto}}')
			$('#kycIdCardSelfieButton').attr('data-src', '{{item.kycIdCardSelfie}}')
			
			$('#kycApiIsValid').attr('checked', {{item.isValid}})
			$('#kycApiFrontIsValid').attr('checked', {{item.frontIsValid}})
			$('#kycApiBackIsValid').attr('checked', {{item.backIsValid}})
			$('#kycApiFrontFullName').attr('checked', {{item.front.fullName}})
			$('#kycApiFrontBirthDate').attr('checked', {{item.front.birthDate}})
			$('#kycApiFrontNationality').attr('checked', {{item.front.nationality}})
			$('#kycApiFrontGender').attr('checked', {{item.front.gender}})
			$('#kycApiBackFullName').attr('checked', {{item.back.fullName}})
			$('#kycApiBackBirthDate').attr('checked', {{item.back.birthDate}})
			$('#kycApiBackNationality').attr('checked', {{item.back.nationality}})
			$('#kycApiBackGender').attr('checked', {{item.back.gender}})
		}
		@endeach
		setTimeout(()=>{
			$('#{{infoFields[0].n}}').focus()
		}, 500)
	})
	
	$('#utilityBillInput').change(function(e){
		const fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
		takeAndSend();
	});
	
	function takeAndSend(){
		const utilityBill = $('#utilityBill').val();
		if (utilityBill == ''){
			
		} else {
			const id = $('#editModal').attr('data-id')
			$('#utilityBillId').val(id)
			$("#utilityBillForm").submit();
		}
	}
	
	$('#btnkycAddressValidation').click(function() {
		$("#utilityBillLabel").click()			
	})
			
	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		$('#editModal').attr('data-id', id)
		
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)
	
		$('#infoModal').css('z-index', '9998');
		$('#answerModal').css('z-index', '9997');
	
		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#surname').val('{{item.surname}}')
			$('#name').val('{{item.name}}')
			$('#gender').val('{{item.gender}}')
			$('#email').val('{{item.email}}')
			$('#avenue').val('{{item.avenue}}')
			$('#address').val("{{ item.postcode!=null ? item.postcode : '' }}" +' '+ '{{item.city}}' )
			$('#phone').val('{{item.phone}}')
			$('#financeinstitute').val('{{item.financeinstitute}}')
			$('#login').val('{{item.login}}')
			$('#terminalCode').val('{{item.terminalCode}}')
			$('#leverage').val('{{item.leverage}}')
			$('#nickname').val('{{item.nickname}}')
			$('#balance').val('{{item.balance}}')
			$('#deposit').val('{{item.deposit}}')
			$('#equity').val('{{item.equity}}')
			$('#withdrawalCapacity').val('{{item.withdrawalCapacity}}')
			
			$('#kycRiskCheck').val('{{item.kycUtilityBill}}')
			$('#kycRiskCheckEnd').text('{{item.kycUtilityBillDate}}')
			$('#kycAddressValidation').val('{{item.kycAddressValidation}}')
			$('#kycAddressValidationEnd').text('{{item.kycAddressValidationDate}}')
			
			const utilityBill = '{{item.utilityBill}}'
			if(utilityBill!='null'){
				$('#utilityBillFoto').val(utilityBill)
				$('#utilityBillFotoButton').attr('data-src', utilityBill)
				$('[name="utilityBillFotoImage"]').text("Loaded Utility Bill")
				$('#utilityBillRow').show()
			}else{
				$('#utilityBillRow').hide()
			}
			
			$('#kycIdentification').val('{{item.kycIdentification}}')
			$('#kycIdentificationEnd').text('{{item.kycIdentificationDate}}')
			$('#kycChatbot').val('{{item.kycChatbot}}')
			$('#kycChatbotEnd').text('{{item.kycChatbotDate}}')
			
			$('#btnkycRiskCheck').text('Risk Check')
			$('#btnkycRiskCheck').attr('href','/customer/riskCheck/{{item.id}}')

			$('#btnkycAddressValidation').text('UB Upload')
			$('#btnkycAddressValidation').attr('data-id','{{item.id}}')
			
			$('#btnkycIdentification').text('IDs')
			$('#btnkycIdentification').attr('data-id','{{item.id}}')
			$('#btnkycIdentification').attr('aria-pressed','true')
			$('#btnkycIdentification').attr('data-toggle','modal')
			$('#btnkycIdentification').attr('data-target','#infoModal')
			
			$('#btnkycChatbot').text('WB Form')
			$('#btnkycChatbot').attr('data-id','{{item.id}}')
			$('#btnkycChatbot').attr('aria-pressed','true')
			$('#btnkycChatbot').attr('data-toggle','modal')
			$('#btnkycChatbot').attr('data-target','#answerModal')
			
			$('#created_date').val('{{item.created_date}}')
			$('#depositLimit').val('{{item.depositLimit}}')
			$('#withdrawalLimit').val('{{item.withdrawalLimit}}')
			
			const contractState = '{{item.kycChatbot}}'
			const pdfOk= '{{item.pdfOk}}'
			let contractFile = id + '/contract.pdf'
			if(contractState==1 && pdfOk=="true"){
				$('#contract').val(contractFile)
				$('#contractButton').attr('data-src', contractFile)
				$('#contractRow').show()
				
				//özel istekler
				const contractImage = $('[name="contractImage"]')
				contractImage.text('Signed WB Form')
				$('#contract').val("Signed-WB-Form.pdf")
			}else{
				$('#contractRow').hide()
			}	
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})
	
	$("#btnQuery").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.queryRoute}}' + '?_method=PATCH'
		$('#queryForm').attr('action', url)

		const currentQuery = {{{ toJSON(currentQuery) }}}
		$('#login').val( currentQuery.tradeId )
		$('#amount').val( currentQuery.amount )
		$('#terminalCode').val( currentQuery.terminalCode )
		$('#financeInstitute').val( currentQuery.financeInstitute || 0)
		$('#beginDate').val( trTarih(currentQuery.begin) )
		$('#endDate').val( trTarih(currentQuery.end) )
		
		@if(currentQuery.kycForce=='on')
		$('#kycForce').attr('checked', true)
		@endif
		
		@if(currentQuery.kycProcessed=='on')
		$('#kycProcessed').attr('checked', true)
		@endif
		
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[4].innerText.toString(),
						row.cells[5].innerText.toString(),
						row.cells[6].innerText.toString(),
						row.cells[7].innerText.toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Customer_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
	@if(old('queryModalShow'))
		$("#btnQuery").click()
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif
	
	@if(old('infoModalShow'))
		$("#btnInfo{{old('id')}}").click()
	@endif
	
	@if(old('answerModalShow'))
		$("#btnAnswer{{old('id')}}").click()
	@endif
	
});
</script>
@endsection
