@layout('layout.main')

@section('title')
	{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@if(item.n != 'transactionId')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v, label: item.l } )
			@endif
		@endeach
	
		<input type="hidden" id="nn_invoiceNumber" name="invoiceNumber" value="{{ summary.invoiceNumber }}">
		<input type="hidden" id="nn_period" name="period" value="{{ summary.period }}">
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@if(item.n != 'x')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent

{{--  Invoice Edit Modal  --}}
@component('components.modal', { type:'editCustom', formId:'invoiceEditForm', id:'invoiceEditModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in invoiceFieldList)
				@if(item.n != 'x')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'ei_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="record"></div></strong>
@endcomponent

{{--  Data List  --}}
<div class="jumbotron mt-3 mb-3 p-3">
	<h1 class="display-4"><i class="bi bi-stopwatch"></i>{{summary.businessName}} <span class="badge bg-secondary text-white">{{ summary.name }}</span></h1>
	
	<div class="row mt-0 mb-0 p-0 my-0">
		<div class="col-7 text-left">
			<p class="lead">
				<b>Period: </b>{{ summary.period }} <b>Count:</b> {{ data.length }}
				<br/><b>Crypto Rate: </b>%{{ summary.cryptoCommissionRate }} <b>Bank-MTC Rate: </b>%{{ summary.bankMtcCommissionRate }}
				<br/>
				<br/><b>Commission:</b> -{{ summary.commission}} <b>Amount:</b> <span class="text-danger font-weight-bold">{{ summary.amount}}</span>
				<br/>
				@if(status == 0)
				<i class="fas fa-tag"></i>
				@else
				<i class="fas fa-key"></i>
				@endif
				<b>Invoice Nr: </b>{{ summary.invoiceNumber }} 
			</p>
		</div>
		<div class="col-5 text-right">
			<p class="lead"></p>
		</div>
	</div>
	
	<hr class="my-2">
	<p class="lead">
		<div class="row mt-0 mb-0 p-0 my-0">
			<div class="col-sm-12 col-md-4">
				<form action="{{ route('previewForexMtcPdf') + '?_method=PUT' }}" class="form" method="POST" enctype="multipart/form-data">
					{{ csrfField() }}
					<input type="hidden" id="period" name="period" value="{{ summary.period }}">
					<input type="hidden" id="invoiceNumber" name="invoiceNumber" value="{{ summary.invoiceNumber }}">
					
					<input type="hidden" id="invoiceType" name="invoiceType" value="{{ summary.invoiceType }}">
					<input type="hidden" id="cid" name="cid" value="{{ summary.id }}">
					<input type="hidden" id="login" name="login" value="{{ summary.login }}">
					<input type="hidden" id="businessName" name="businessName" value="{{ summary.businessName }}">
					<input type="hidden" id="name" name="name" value="{{ summary.name }}">
					<input type="hidden" id="addressFirst" name="addressFirst" value="{{ summary.addressFirst }}">
					<input type="hidden" id="addressSecond" name="addressSecond" value="{{ summary.addressSecond }}">
					<input type="hidden" id="addressThird" name="addressThird" value="{{ summary.addressThird }}">
					<input type="hidden" id="provision" name="provision" value="{{ summary.provision }}">
					<input type="hidden" id="moneyType" name="moneyType" value="{{ summary.moneyType }}">
			
					<input type="hidden" id="commission" name="commission" value="{{ summary.commission }}">
					<input type="hidden" id="cryptoCommissionRate" name="cryptoCommissionRate" value="{{ summary.cryptoCommissionRate }}">
					<input type="hidden" id="bankMtcCommissionRate" name="bankMtcCommissionRate" value="{{ summary.bankMtcCommissionRate }}">
					<input type="hidden" id="deposit" name="deposit" value="{{ summary.deposit }}">
					<input type="hidden" id="withdraw" name="withdraw" value="{{ summary.withdraw }}">
					<input type="hidden" id="bankDeposit" name="bankDeposit" value="{{ summary.bankDeposit }}">
					<input type="hidden" id="bankWithdraw" name="bankWithdraw" value="{{ summary.bankWithdraw }}">
					<input type="hidden" id="mtcDeposit" name="mtcDeposit" value="{{ summary.mtcDeposit }}">
					<input type="hidden" id="mtcWithdraw" name="mtcWithdraw" value="{{ summary.mtcWithdraw }}">
					<input type="hidden" id="cryptoDeposit" name="cryptoDeposit" value="{{ summary.cryptoDeposit }}">
					<input type="hidden" id="cryptoWithdraw" name="cryptoWithdraw" value="{{ summary.cryptoWithdraw }}">
					
					<input type="hidden" id="requestedCredit" name="requestedCredit" value="{{ invoiceData.requestedCredit }}">
					<input type="hidden" id="previousCredit" name="previousCredit" value="{{ invoiceData.previousCredit }}">
					<input type="hidden" id="invoiceDate" name="invoiceDate" value="{{ invoiceData.date }}">
					
					<input type="hidden" id="status" name="status" value="{{ status }}">
					
					@if(auth.user.role==3 || auth.user.role==2 || auth.user.role==21)
					<button type="submit" class="btn btn-primary btn-lg">
						<i class="fas fa-file-invoice"></i> Invoice</button>
					<a id="btnExport" href="#" role="button" class="btn btn-info btn-lg" aria-pressed="true">
						<i class="fas fa-download"></i>
					</a>
					@endif	
				</form>
			</div>
			<div class="col-sm -12 col-md-8 mt-2 mt-md-0 text-md-right">
				<div class="row justify-content-end">
					<div class="col-12">
						@if((auth.user.role==3 || auth.user.role==2) && status==1 && invoiceData)
							<a href="#" id="btnInvoiceEdit" role="button" data-id="{{ summary.invoiceNumber }}" class="btn btn-success btn-lg btnEdit d-inline-block" aria-pressed="true" data-toggle="modal"
								data-target="#invoiceEditModal"><i class="fas fa-pen"></i> Edit</a>
						@endif

						<form action="{{ route('saveForexMtc') + '?_method=PUT' }}" class="form d-inline-block" method="POST" enctype="multipart/form-data">
							{{ csrfField() }}
							<input type="hidden" id="period" name="period" value="{{ summary.period }}">
							<input type="hidden" id="invoiceNumber" name="invoiceNumber" value="{{ summary.invoiceNumber }}">
							<input type="hidden" id="status" name="status" value="{{ status }}">
							
							@if(auth.user.role==3 && saveAble)
							<button type="submit" class="btn btn-success btn-lg"
								@if(status > 0)
									disabled
								@endif
							>
								@if(status > 0)
									<i class="fas fa-database"></i> From DB
								@else
									<i class="fas fa-save"></i> Export DB
								@endif
							</button>
							@endif
						</form>
					
						<form action="{{ route('freezeForexMtc') + '?_method=PUT' }}" class="form d-inline-block" method="POST" enctype="multipart/form-data">
							{{ csrfField() }}
							<input type="hidden" id="period" name="period" value="{{ summary.period }}">
							<input type="hidden" id="invoiceNumber" name="invoiceNumber" value="{{ summary.invoiceNumber }}">
							
							@if(auth.user.role==3)
							<button type="submit" class="btn btn-warning btn-lg"
								@if(status == 0 || status == 2)
									disabled
								@endif
								@if(status == 1 && !saveAble)
									disabled
								@endif
							>
								@if(status == 2)
									<i class="fas fa-lock"></i> Locked
								@else
									@if(saveAble)
										@if(status == 1)
											<i class="fas fa-lock-open"></i> Lock
										@else
											<i class="fas fa-server"></i> API
										@endif
									@else
										@if(status == 1 && !saveAble)
											<i class="fas fa-server"></i> From DB
										@else
											<i class="fas fa-server"></i> From API
										@endif
									@endif
								
								@endif
							</button>
							@endif
						</form>
					</div>
				</div>
			</div>
		</div>
	</p>
	
</div>

<table class="table table-striped table-sm" id="tblData">
	<thead>
		{{--  filter  --}}
		<tr>
			<td colspan="7">
				<div class="row justify-content-end align-items-right text-right">
					<div class="col-sm-0 col-md-3 col-lg-5 d-inline-block"></div>
					<div class="col-sm-5 col-md-4 col-lg-3 d-inline-block">
						<div class="input-group mb-2 input-group-sm">
							<div class="input-group-prepend">
								<div class="input-group-text" id="typeLabel">Type</div>
							</div>
							<select name="nType" id="iType"
								class="form-control custom-select {{ elIf('is-invalid','', hError) }}" 
								data-toggle="tooltip" data-placement="top" title="Type" aria-label="Type">
								@each(item in transactionTypes)
									<option value="{{ item.id }}" {{ elIf('selected','', item.id==params.type)}} >
										{{ item.title }}
									</option>
								@endeach
							</select>
						</div>
					</div>
					<div class="col-sm-5 col-md-4 col-lg-3 d-inline-block">
						<div class="input-group mb-2 input-group-sm">
							<div class="input-group-prepend">
								<div class="input-group-text" id="gatewayLabel">Gateway</div>
							</div>
							<select name="nGateway" id="iGateway" 
								class="form-control custom-select {{ elIf('is-invalid','', hError) }}" 
								data-toggle="tooltip" data-placement="top" title="Gateway" aria-label="Gateway">
								@each(item in transactionGateways)
									<option value="{{ item.id }}" {{ elIf('selected','', item.id==params.gateway)}} >
										{{ item.title }}
									</option>
								@endeach
							</select>
						</div>
					</div>
					<div class="col-sm-2 col-md-1 d-inline-block">
						<a href="/" role="button" id="btnFilter"
							class="btn btn-outline-primary btn-sm" aria-pressed="true">
							<i class="fas fa-search"></i>
						</a>
					</div>
				</div>
			</td>
		</tr>
		{{--  header  --}}
		<tr>
			<th scope="col"><a href="{{ url }}/order/date{{order.asc}}">Date</a></th>
			<th scope="col"><a href="{{ url }}/order/login{{order.asc}}">Trader</a></th>
			<th scope="col"><a href="{{ url }}/order/type{{order.asc}}">Type</a></th>
			<th scope="col"><a href="{{ url }}/order/gateway{{order.asc}}">Gateway</a></th>
			<th scope="col" class="text-right"><a href="{{ url }}/order/amount{{order.asc}}">Amount</a></th>
			<th scope="col" class="text-right"><a href="{{ url }}/order/commission{{order.asc}}">Commission</a></th>
			<th scope="col" class="text-right">
				@if(auth.user.role==3 && status==1)
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal"
					data-target="#newModal"><i class="fas fa-plus"></i> New Line</a>
				@endif
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr class="dataLine">
			<td>{{ item.date }}</td>
			<td class="mm-fs-8">{{ item.login }} {{ item.name.substring(0, 20) }}</td>
			<td class="mm-fs-8">{{ item.typeName }}</td>
			<td >{{ item.gateway }}</td>
			<td class="text-right"><b>{{ item.amount }}</b></td>
			<td class="text-right"><b>{{ item.commission }}</b></td>
			<td class="text-right">
				<div class="btnColumn">
					@if((auth.user.role==3 || auth.user.role==2) && status==1)
						<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
							data-target="#editModal"><i class="fas fa-pen"></i></a>
					@endif
					@if(auth.user.role==3 && status==1)
					<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.amount }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
						data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
					@endif
				</div>
			</td>
		</tr>
		@endeach
	</tbody>
	<tfoot>
		<tr class="bg-light">
			<td>{{ summary.period }}</td>
			<td></td>
			<td></td>
			<td>Total:</td>
			<td id="totalAmount" class="text-right"><b>{{ summary.provision }}</b></td>
			<td id="totalCommission" class="text-right"><b>{{ summary.commission }}</b></td>
			<td></td>
		</tr>
	</tfoot>
</table>

@endsection

@section('script')
<script>
$( function() {
	
	function generateFilterUrl(){
		const instituteId = {{params.instituteId}}
		const periodId = {{params.periodId}}
		const unixBegin = {{params.begin}}
		const unixEnd ={{params.end}}
		const type = $("#iType").val() || 0
		const gateway = $("#iGateway").val() || "all"		
		const url = "/forexMtc/" + instituteId +"/"+ type +"/"+ gateway +"/"+ periodId +"/"+ unixBegin +"/"+ unixEnd
		$("#btnFilter").attr("href", url)
	}
	
	$("#iType").change(()=>generateFilterUrl())
	$("#iGateway").change(()=>generateFilterUrl())
	
	setTimeout(()=>generateFilterUrl(), 500)
	
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[1].n}}']").focus()
			$('#n_invoiceNumber').val(parseInt('{{ summary.invoiceNumber }}'))
			$('#n_period').val('{{ summary.period }}')
			$("#n_type").prop("selectedIndex", 1)
			$("#n_gateway").prop("selectedIndex", 1)
			$('#n_amount').val(0)
			$('#n_commission').val(0)
			$('#n_commissionRate').val(0)
			$('#n_date').val(trTarihSaat('{{ summary.date }}', true))
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='/forexMtc/' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#record').html(item)
	})
	
	$("#btnInvoiceEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='/Invoice/' + id + '?_method=PATCH'
		$('#invoiceEditForm').attr('action', url)
		
		if (id=='{{ invoiceData.invoiceNumber }}') {
			$('#ei_invoiceNumber').val('{{ invoiceData.invoiceNumber }}')	
			$('#ei_date').val(trTarih('{{ invoiceData.date }}'))
			$('#ei_requestedCredit').val('{{ invoiceData.requestedCredit }}' || 0)
			$('#ei_previousCredit').val('{{ invoiceData.previousCredit }}' || 0)
		}
		setTimeout(()=>{
			$('#ei_date').focus()
		}, 500)
	})
	
	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='/forexMtc/' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)
		
		@each(item in data)
		//range(totalNumberOfPages)
		if (id=='{{ item.id }}') {
			$('#e_invoiceNumber').val('{{ item.invoiceNumber }}')
			$('#e_transactionId').val('{{ item.transactionId }}')
			$('#e_period').val('{{ item.period }}')			
			$('#e_date').val(trTarihSaat('{{ item.date }}', true))
			$('#e_login').val('{{ item.login }}')
			$('#e_name').val('{{ item.name }}')
			$('#e_gateway').val('{{ item.gateway }}')
			$('#e_type').val('{{ item.type }}')
			$('#e_amount').val('{{ item.amount }}' || 0)
			$('#e_commission').val('{{ item.commission }}' || 0)
			$('#e_commissionRate').val('{{ item.commissionRate }}' || 0)
			$('#e_comment').val('{{ item.comment }}')
			
			// var dateValue = $('#e_date').val()
			// if (dateValue.substring(17).length == 0) {
			// 	$('#e_date').val(dateValue + ':01')
			// }
		}
		@endeach
		setTimeout(()=>{
			$('#login').focus()
		}, 500)
	})
	
	
	
	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif
	
	@if(old('editModalShow'))
		$('#editModal').modal('show')
	@endif
	
	$(".btnInvoice").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url = '{{caption.route}}/' + id + '?_method=POST'
		$('#sendSmsForm').attr('action', url)
		$('#sendSmsRecord').html(item)
	})
	
	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[1].innerText.toString(),
						row.cells[2].innerText.toString(),
						parseFloat(row.cells[3].innerText).toFixed(2).replace('.', ',').toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Forex_MTC_Commission_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
});
</script>
@endsection
