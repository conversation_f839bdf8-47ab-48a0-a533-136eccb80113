@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	@if(auth.user.role==3)
	<div class="row">
		<div class="col-8 col-sm-8 col-md-5 col-lg-4 mx-auto text-center">
			<div class="card mt-5">
				<div class="card-header">{{ caption.title }}</div>
				<div class="card-body">
					<form action="{{ route('createUser') }}" class="form" method="POST">
						{{ csrfField() }}
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-suitcase"></i></div>
							</div>
							<select class="form-control custom-select {{ elIf('is-invalid','', hError) }}" name="role" id="role">
								<option value="" selected hidden>{{ caption.role }}</option>
								@each(item in roles)
									<option value="{{ item.id }}" {{ (item.id==1) ? "selected":"" }}>{{ item.title }}</option>
								@endeach
							</select>
						</div>

						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-tag"></i></div>
							</div>
							<input type="text" class="form-control" value="{{ old('username') }}" id="username" name="username" placeholder="{{ caption.username }}" autofocus>
						</div>
						{{ elIf('<div class="alert alert-danger mb-2 mx-auto">$self</div>', getErrorFor('username'), hasErrorFor('username')) }}

						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-at"></i></div>
							</div>
							<input type="text" class="form-control" value="{{ old('email') }}" id="email" name="email" placeholder="{{ caption.email }}" >
						</div>
						{{ elIf('<div class="alert alert-danger mb-2 mx-auto">$self</div>', getErrorFor('email'), hasErrorFor('email')) }}
						
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-key"></i></div>
							</div>
							<input type="password" class="form-control" name="pass" id="pass" placeholder="{{ caption.password }}" >
                        </div>
						
                        <div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fas fa-check-double"></i></div>
                            </div>
                            <input type="password" class="form-control" name="pass_confirmation" id="passr" placeholder="{{ caption.passwordR }}" >
                        </div>
						{{ elIf('<div class="alert alert-danger mb-2 mx-auto">$self</div>', getErrorFor('pass'), hasErrorFor('pass')) }}
						
						<div class="input-group">
							<button type="submit" class="btn btn-primary mx-auto">{{ caption.add }}</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
	@else
	<div class="row">
		<div class="col-8 col-sm-8 col-md-5 col-lg-4 mx-auto text-center">
			<div class="card mt-5">
				<div class="card-header">Forbidden</div>
				<div class="card-body">
					<p>This page for Only Admins</p>
				</div>
			</div>
		</div>
	</div>
	@endif
@endsection
