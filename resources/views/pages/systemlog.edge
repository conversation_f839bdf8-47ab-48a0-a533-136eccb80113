@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{--  Detail Modal  --}}
@component('components.modal', { type:'detail', id:'detailModal', lg:'', action:'', caption: caption })
	<div>
		<div class="text-center mm-nkalin" id="detailTerminal"></div>
		<div class="text-center mm-fs-8"id="detailLog"></div>
	</div>
@endcomponent

{{--  Deposit Modal  --}}
@component('components.modal', { type:'detail', id:'depositModal', lg:'', action:'', caption: caption })
	<div>
		<div class="text-center mm-nkalin" id="depositTerminal"></div>
		<div class="text-center mm-fs-8"id="depositLog"></div>
	</div>
@endcomponent

{{-- Query Modal --}}
@component('components.modal', { type:'query', id:'queryModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in queryFields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'q_'+item.n, 
				icon: item.i, placeholder: item.c, enabled: item.e, value: item.v } )
			@endif
		@endeach
	</div>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3 mb-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[2].c }}</th>
			<th scope="col" class="text-right">
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
				<a id="btnQuery" href="#" role="button" class="btn btn-outline-info btn-sm btnQuery" aria-pressed="true" data-toggle="modal"
					data-target="#queryModal"><i class="fas fa-search"></i></a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th scope="row"><span class="mm-mavi"><i class="{{caption.icon}}"></i></span></i></th>
			<td>{{ item.terminalCode }}</td>
			<td class={{ (item.log.indexOf('ERR: ') > -1) || (item.log.indexOf('!!!') > -1) ? "mm-pkirmizi mm-fs-8" : (item.log.indexOf('>_') > -1) ? "mm-turuncu mm-fs-8" : (item.log.indexOf('&') > -1) ? "mm-yesil mm-fs-8" : "mm-siyah mm-fs-8" }}>{{ item.log.substring(0,200) }}</td>
			<td class="mm-fs-8">{{ item.created_date }}</td>
			<td class="text-right mm-min">
				@if(item.log.indexOf('Payout Routed') > -1)
				<a href="transfers/log/{{ item.id }}" role="button" 
				data-id="{{ item.id }}" 
				data-date="{{ item.created_date }}" 
				data-terminal="{{ item.terminalCode }}" 
				data-log="{{ item.log }}" 
				class="btn btn-outline-success btn-sm btnDeposit" aria-pressed="true"><i class="fas fa-money-bill-alt"></i></a>
				@endif

				<a href="#" role="button" 
				data-id="{{ item.id }}" 
				data-date="{{ item.created_date }}" 
				data-terminal="{{ item.terminalCode }}" 
				data-log="{{ item.log }}" 
				data-target="#detailModal" 
				class="btn btn-outline-info btn-sm btnDetail" aria-pressed="true" data-toggle="modal"><i class="fas fa-info-circle"></i></a>
			</td>
		</tr>
		@endeach
	</tbody>
	<tfoot>
		<tr>
			<td></td>
			<td></td>
			<td></td>
			<td class="mm-t-ortala"><b>Last 2000 data</td>
			<td></td>
			<td></td>
		</tr>
	</tfoot>
</table>
</div></div>

@endsection

@section('script')
<script>
$( function() {

	$("#btnQuery").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.queryRoute}}' + '?_method=PATCH'
		$('#queryForm').attr('action', url)

		const currentQuery = {{{ toJSON(currentQuery) }}}
		$('#q_log').val( currentQuery.log )
		$('#q_terminalCode').val( currentQuery.terminalCode )
		$('#q_beginDate').val( trTarih(currentQuery.begin) )
		$('#q_endDate').val( trTarih(currentQuery.end) )

		setTimeout(()=>{
			$('#q_terminalCode').focus()
		}, 500)
	})

	$(".btnDetail").click( function() {
		var id = $(this).attr('data-id')
		$('#detailLog').html($(this).attr('data-log'))
		$('#detailTerminal').html($(this).attr('data-terminal'))
	})

	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[4].innerText.toString()
					]
				);
			}
		}		
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Log_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
	@if(old('queryModalShow'))
		$("#btnQuery").click()
	@endif

});
</script>
@endsection
