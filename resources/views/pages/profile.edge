@layout('layout.main')

@section('content')
	<div class="row">
		<div class="col-8 col-sm-8 col-md-5 col-lg-4 mx-auto text-center">
			<div class="card mt-5">
				<div class="card-header">{{ title }}</div>
				<div class="card-body">
					<form action="{{ route('updateProfile', {id: auth.user.id}) }}" class="form" method="POST">
						{{ csrfField() }}
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fa fa-envelope"></i></div>
							</div>
							<input type="text" disabled class="form-control" name="email" id="email" value="{{ auth.user.email}}">
						</div>
						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fa fa-key"></i></div>
							</div>
							<input type="password" class="form-control" name="pass" id="pass" placeholder="{{ passwordCaption }}" >
						</div>

						<div class="input-group mb-3">
							<div class="input-group-prepend">
								<div class="input-group-text"><i class="fa fa-key"></i></div>
							</div>
							<input type="password" class="form-control" name="pass_confirmation" id="passr" placeholder="{{ passwordRCaption }}" >
						</div>
						{{ elIf('<div class="alert alert-danger mb-2 mx-auto">$self</div>', getErrorFor('pass'), hasErrorFor('pass')) }}

						<div class="input-group">
							<button type="submit" class="btn btn-primary mx-auto">{{ buttonCaption }}</button>
						</div>
					</form>
				</div>
			</div>
			@if(old('alert'))
				<div class="alert alert-success mt-3">{{ old('alert') }}</div>
			@endif
		</div>
	</div>
@endsection