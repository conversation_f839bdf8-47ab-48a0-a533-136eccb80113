@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')

{{-- New Modal --}}
@component('components.modal', { type:'new', id:'newModal', lg:'', action: caption.newAction, caption: caption, csrfField: csrfField })
	<div class="col-md-12">
		@each(item in fields)
			@if(item.n != 'status')
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'n_'+item.n,
				icon: item.i, placeholder: item.c, enabled: item.ne, value: item.v } )
			@endif
		@endeach
		@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
	</div>
@endcomponent

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields.slice(0,16))
				@if(item.n != 'status' && item.n != 'command')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				@endif
				@if(auth.user.role==3 && item.n == 'command')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: 'e_'+item.n,
					icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
				@endif
			@endeach
			
			@!component('components.input', { sm:'input-group-sm', type: 'active', name:'active', active:'Active', passive :'Passive' } )
		</div>
	</div>
@endcomponent

{{--  Migrate Modal  --}}
@component('components.modal', { type:'migrate', id:'migrateModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			{{{ caption.migrateWarning }}}
			<strong><div id="migrateRecord"></div></strong>
			<hr/>
			@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(targetTerminal.n),
			hError: hasErrorFor(targetTerminal.n), type: targetTerminal.t, options: targetTerminal.v, name: targetTerminal.n, id: targetTerminal.n,
			icon: targetTerminal.i, placeholder: targetTerminal.c, enabled: targetTerminal.e } )
		</div>
	</div>	
@endcomponent

{{--  Delete Modal  --}}
@component('components.modal', { type:'delete', id:'deleteModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	{{{ caption.deleteWarning }}}
	<strong><div id="deleteRecord"></div></strong>
@endcomponent

{{--  Command Modal  --}}
@component('components.modal', { type:'command', id:'commandModal', lg:'', action: caption.commandAction, caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields)
				@if(item.n == 'command')
					@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
					hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
					icon: item.i, placeholder: item.c, enabled: item.e } )
				@endif
			@endeach
		</div>
	</div>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col" class="d-none d-lg-table-cell"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col" class="d-none d-lg-table-cell"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col" class="d-none d-md-table-cell">{{ fields[1].c }}</th>
			<th scope="col">{{ fields[12].c }}</th>
			<th scope="col" class="d-none d-lg-table-cell">{{ fields[5].c }}</th>
			<th scope="col">{{ fields[6].c }}</th>
			<th scope="col" class="text-right pr-3">
				@if(auth.user.role==3)
				<a href="#" role="button" class="btn btn-outline-primary btn-sm btnCommand" aria-pressed="true" data-toggle="modal"
					data-target="#commandModal"><i class="fas fa-terminal"></i> </a>
				@endif
				@if(auth.user.role==3)
				<a href="#" role="button" class="btn btn-outline-success btn-sm btnNew" aria-pressed="true" data-toggle="modal"
					data-target="#newModal"><i class="fas fa-plus"></i> {{ caption.new }}</a>
				@endif
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr class="dataLine">
			<td class="d-none d-lg-table-cell">{{ item.id }}</td>
			<th scope="row" class="d-none d-lg-table-cell"><span class="mm-mavi"><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.terminalCode }}</td>
			<td class="mm-fs-8 d-none d-md-table-cell">{{ item.description.substring(0,30) }}</td>
			<td class="{{ item.status==1? 'mm-pyesil' : 'mm-kirmizi'}}" id="status{{item.id}}" data-value="{{item.status}}">{{{ fields[12].v[item.status].title }}}</td>
			<td class="mm-fs-8 mm-kaydir mm-mavi d-none d-lg-table-cell">{{ item.answer }}</td>
			<td><span class="mm-mavi"><i class="fas fa-coins"></i></span> <b>{{ item.payoutTotalMoney }}</b></td>
			<td class="text-right mm-min">
				<div class="btnColumn">
					@if(auth.user.role==3 || auth.user.role==2)
					<a href="{{ route('queryTransfersByTerminal', {terminalCode: item.terminalCode}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-primary btn-sm btnTransfer" aria-pressed="true"
					><i class="fas fa-hand-holding-usd"></i></a>
					@endif
					@if(auth.user.role==3 || auth.user.role==2)
						<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
						data-target="#editModal"><i class="fas fa-pen"></i></a>
					@endif
					<a href="{{ route('queryNoteByTerminalId', {terminalId: item.id}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-secondary btn-sm btnNote" aria-pressed="true"
						><i class="fas fa-sticky-note"></i></a>
					<a href="{{ route('queryKeyByTerminalId', {terminalId: item.id}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-secondary btn-sm btnKey" aria-pressed="true"
						><i class="fas fa-key"></i></a>
					@if(auth.user.role==3)
						<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.terminalCode }}" class="btn btn-outline-danger btn-sm btnDelete" aria-pressed="true" data-toggle="modal"
						data-target="#deleteModal"><i class="fas fa-trash-alt"></i></a>
					@endif
					@if(auth.user.role==3)
						<a href="#" role="button" data-id="{{ item.id }}" data-item="{{ item.terminalCode }}" class="btn btn-outline-primary btn-sm btnMigrate" aria-pressed="true" data-toggle="modal"
						data-target="#migrateModal"><i class="fas fa-share"></i></a>
					@endif
				</div>
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: '', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('info'))
	<div style="text-align:center; color:grey; font-size:0.8em ">
		Please Wait, Processing . . .  <i class="fas fa-sync fa-spin"></i> <span id="time"></span>
	<div>
@endif

@endsection

@section('script')
<script>

function geriSay(sure, nerde) {
	var sayac = sure, saniye;
	setInterval(function () {
		saniye = parseInt(sayac % sure, 10);
		saniye = saniye < 10 ? '0' + saniye : saniye;
		//nerde.textContent = saniye + ' sn';
		if (--sayac < 0) {
			sayac = sure;
			location.reload();
		}
	}, 1000);
};

$( function() {
	@if(old('info'))
		var sure = 4
		var nerde = document.querySelector('#time');
		geriSay(sure, nerde);
	@endif

	$('#commandModal').find("select[name='command']").prop("selectedIndex", 1);
	
	/*
	$(".btnColumn").hide()
	$(".dataLine").hover( function() {
		$(".btnColumn").hide()
		$(this).find(".btnColumn").show()
	})
	*/
	
	$(".btnNew").click( function() {
		setTimeout(()=>{
			$('#newModal').find("input[name='{{fields[0].n}}']").focus()
			$('#n_devicePort').val( 'COM3' )
			$('#n_lang').val( 'de' )
			$('#n_cashboxTotalMoney').val(0)
			$('#n_isPrint').val( 1 )
			$('#n_bfi').val( 1 )
		}, 500)
	})

	$(".btnDelete").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}' + id + '?_method=DELETE'
		$('#deleteForm').attr('action', url)
		$('#deleteRecord').html(item)
	})

	$(".btnMigrate").click( function() {
		var id = $(this).attr('data-id')
		var item = $(this).attr('data-item')
		var url='{{caption.route}}'+ 'migrate/' + id + '?_method=PATCH'
		$('#migrateForm').attr('action', url)
		$('#migrateRecord').html(item)
	})

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)
		
		@if(auth.user.role==3)
			$('#command').attr('disabled', false)
		@endif
		
		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#e_terminalCode').val( '{{item.terminalCode}}' )
			$('#e_description').val( '{{item.description}}' )
			$('#e_showUrl').val( '{{item.showUrl}}' )
			$('#e_devicePort').val( '{{item.devicePort}}' )
			$('#e_lang').val( '{{item.startupLang}}' )
			$('#e_answer').val( '{{item.answer}}' )
			
			@if(auth.user.role==3)
			if( $('#e_status'+id).attr('data-value') == 0 ){
				$('#e_command').attr('disabled', true)
				$('#e_command').val( '' )
			}else{
				$('#e_command').val( 'check' )
			}
			@endif
			
			{{item.active}} == 1 ? $('#editModal #active').click() : $('#editModal #passive').click()
			$('#e_payoutTotalMoney').val( '{{item.payoutTotalMoney}}' )
			$('#e_payoutMoneyDetail').val( '{{item.payoutMoneyDetail}}' )
			$('#e_cashboxTotalMoney').val( '{{item.cashboxTotalMoney}}' )
			$('#e_isPrint').val( '{{item.isPrint}}' )
			$('#e_bId').val( '{{item.bId}}' )
			$('#e_depositLimit').val( '{{item.depositLimit}}' )
			$('#e_withdrawalLimit').val( '{{item.withdrawalLimit}}' )
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
						row.cells[6].innerText.toString()
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "Terminal_Report.csv");
        document.body.appendChild(link);

        link.click();
	})
	
	@if(old('newModalShow'))
		$('#newModal').modal('show')
	@endif

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif

	@if(old('migrateModalShow'))
		$('#migrateModal').modal('show')
	@endif

});
</script>
@endsection
