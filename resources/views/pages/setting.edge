@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	@if(auth.user.role==3)
		{{--  Data Listing  --}}
		@component('components.card', { action: caption.route + '1?_method=PATCH', caption: caption, csrfField: csrfField })
		<div class="container-fluid">
			<div class="row">
				<div class="col-md-6">
					@each(item in fields.slice(0,15))
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
							hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
							icon: item.i, placeholder: item.c, enabled: item.e, label: item.l} )
					@endeach
				</div>
				<div class="col-md-6">
					@each(item in fields.slice(15,31))
						@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
							hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
							icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
					@endeach
				</div>
			</div>
		</div>
		@endcomponent
	@else
		<div class="row">
			<div class="col-8 col-sm-8 col-md-5 col-lg-4 mx-auto text-center">
				<div class="card mt-5">
					<div class="card-header">Forbidden</div>
					<div class="card-body">
						<p>This page for Only Admins</p>
					</div>
				</div>
			</div>
		</div>
	@endif


	{{--  Autohide Alert  --}}
	@if(old('info'))
		@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
			@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
			@endslot
		@endcomponent
	@endif

@endsection

@section('script')
<script>
	$(function () {
		const data = {{{ toJSON(data[0]) }}}
		const fields = {{{ toJSON(fields) }}}

		fields.forEach(f => {
			$('#'+ f.n).val(data[f.n])
		});
		
		setTimeout(() => {
			$('#{{fields[0].n}}').focus()
		}, 500)

	});
</script>
@endsection
