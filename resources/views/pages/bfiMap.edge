<!DOCTYPE html>
<html>
<head>
  <title>BFI</title>
  
	<link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    
  <style>		
		body {
			font-family: 'Roboto', sans-serif;
    }
				
    #map {
			min-height: 480px;
			width: 100%;
		}
		
		.gm-style-iw-ch {
			font-weight: bold;
		}
		
		@media (min-width: 768px) {
			.container {
				display: flex;
				width: 100%;
			}
			
			.card-list {
				width: 300px;
				height: 480px;
				overflow-y: auto;
				padding: 20px;
			}

			.map-container {
				flex: 1;
				height: 480px;
				padding: 20px;
			}	
		}
		
		@media (max-width: 768px) {
			.container {
				display: flex;
				width: 100%;
				flex-direction: column;
			}
		
			.card-list {
				height: 480px;
				overflow-y: auto;
				padding: 20px;
				order: 2;
			}

			.map-container {
				height: 480px;
				padding: 20px;
				order: 1;
			}
		}
		
		.card {
			border: 1px solid #ddd;
			border-radius: 5px;
			padding: 20px;
			margin-bottom: 10px;
			cursor: pointer;
			transition: box-shadow 0.3s ease;
			background-color: #f5f5f5; /* Light gray background */
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			color: #333;
		}
		
		.card:hover {
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);	
		}
		
		.card a {
			text-decoration: none;
			color: inherit;
			display: block;
		}
		
		.card-title {
			font-size: 1.2em;
			font-weight: bold;
			margin-bottom: 10px;
		}

		.card-address {
			color: #555;
			margin-bottom: 10px;
		}

		.card-new {
			color: green;
			margin-bottom: 5px;
		}
  </style>
 
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCLRJoUM_FHVuQSoJ1nbwTCOTeYD8_j5rA&callback=initMap" async defer></script>
 
  <script>
		let map;
    let infoWindow;
		let geocoder;
	
    function initMap() {
			map = new google.maps.Map(document.getElementById("map"), {
				center: { lat: 47.377562, lng: 8.540672 },
				zoom: 10,
			});
			
			infoWindow = new google.maps.InfoWindow();
			geocoder = new google.maps.Geocoder();
			
			const places = [
				@each(item in bfis)
				{ lat: {{item.lat}}, lng: {{item.long}}, businessName: "{{item.businessName}}", companyName: "{{item.companyName}}", zip:"{{item.zip}}", city: "{{item.city}}", street: "{{item.street}}", address: "{{item.street}}, {{item.zip}} {{item.city}}" },
				@endeach
			];		
			
			places.forEach( (place) => {
				geocoder.geocode({ address: place.address }, (results, status) => {
					if (status === "OK") {
						const firstResult = results[0];
						
						const marker = new google.maps.Marker({
							//position: { lat: place.lat, lng: place.lng },
							position: firstResult.geometry.location,
							map: map,
							title: place.name,
							icon: {
								url: "images/marker.png",
								scaledSize: new google.maps.Size(30, 30),
							},
						});
						
						/*
						const contentString="<h2>" + place.businessName +", "+ place.zip  +" "+ place.city+ "</h2><p>"+ place.companyName + "</p>" + 
						'<a target="_blank" href="https://www.google.com/maps?q=' + place.lat + ',' + place.lng + '&z=14&mapclient=apiv3" tabindex="0"><span>Google Maps</span></a>';
						*/

						const contentString="<p>"+ place.companyName +"<br/>"+ place.street +"<br/>"+ place.zip +" "+ place.city +"</p>"+ 
						'<a target="_blank" href="https://www.google.com/maps/search/' + place.address + '" tabindex="0"><span>Google Maps</span></a>';
						
						marker.addListener("click", () => {
							infoWindow.setHeaderContent(place.businessName +", "+ place.zip  +" "+ place.city);
							infoWindow.setContent(contentString);
							infoWindow.open(map, marker);
						});
					} 
					else 
					{
						console.error("Geocoding failed: " + status);
					}
				});
			});
		}
	</script>

</head>

<body>
  <div class="container">
		<div class="card-list">
			@each(item in bfis)
			<div class="card">
				<a href="https://www.google.com/maps/search/{{item.street}}, {{item.zip}} {{item.city}}">
					<div class="card-title">{{item.businessName}}</div>
					<div class="card-address">{{item.street}}<br> {{item.zip}} {{item.city}}</div>
					<div class="card-new">{{item.companyName}}</div>
				</a>
			</div>
			@endeach
		</div>
		<div class="map-container">
			<div id="map"></div>
		</div>
	</div>
</body>
</html>

