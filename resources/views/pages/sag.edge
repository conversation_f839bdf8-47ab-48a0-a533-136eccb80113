@layout('layout.main')

@section('title')
{{ caption.pageTitle }}
@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
{{--  Autohide Alert  --}}
@if(old('info'))
	@component('components.alert', { type: 'success', autohide: 'autohide', message: old('info') } )
		@slot('strong')
			<i class="fas fa-lightbulb"></i> <strong>Success!</strong>
		@endslot
	@endcomponent
@endif

@if(old('error'))
<div class="alert alert-danger fade show" role="alert">
	<i class="fas fa-exclamation-triangle"></i> <strong>Error!</strong> {{ old('error') }}
</div>
@endif

{{--  Edit Modal  --}}
@component('components.modal', { type:'edit', id:'editModal', lg:'', action:'', caption: caption, csrfField: csrfField })
	<div class="row">
		<div class="col-md-12">
			@each(item in fields.slice(0,16))
				@!component('components.input', { sm:'input-group-sm', gError: getErrorFor(item.n),
				hError: hasErrorFor(item.n), type: item.t, options: item.v, name: item.n, id: item.n,
				icon: item.i, placeholder: item.c, enabled: item.e, label: item.l } )
			@endeach
		</div>
	</div>
@endcomponent

{{--  Data List  --}}
<div class="container-fluid mt-3"><div class="px-0 py-1 border rounded table-responsive">
<table class="table table-striped table-sm table-borderless" id="tblData">
	<thead>
		<tr>
			<th scope="col"><span class="badge bg-warning badge-pill">#</span></th>
			<th scope="col"></th>
			<th scope="col">{{ fields[0].c }}</th>
			<th scope="col">{{ fields[2].c }}</th>
			<th scope="col" class="text-right">
				<a id="btnExport" href="#" role="button" class="btn btn-outline-info btn-sm" aria-pressed="true">
					<i class="fas fa-download"></i>
				</a>
			</th>
		</tr>
	</thead>
	<tbody>
		@each(item in data)
		<tr>
			<td>{{ item.id }}</td>
			<th scope="row"><span class="mm-mavi"><i class="{{caption.icon}}"></i></span></th>
			<td>{{ item.businessName }}</td>
			<td>{{ item.commissionRate }}</td>
			<td class="text-right">
				
				<!--  @if(auth.user.role==3 || auth.user.role==2)
				<a href="{{ route('queryTransfersBySag', {sagId: item.id}) }}" role="button" data-id="{{ item.id }}" class="btn btn-outline-primary btn-sm btnTransfer" aria-pressed="true"
					><i class="fas fa-hand-holding-usd"></i></a>
				@endif  -->
				
				@if(auth.user.role==3 || auth.user.role==2)
				<a href="#" id="btnEdit{{item.id}}" role="button" data-id="{{ item.id }}" class="btn btn-outline-info btn-sm btnEdit" aria-pressed="true" data-toggle="modal"
					data-target="#editModal"><i class="fas fa-pen"></i></a>
				@endif
			</td>
		</tr>
		@endeach
	</tbody>
</table>
</div></div>

@endsection

@section('script')
<script>
$( function() {

	$(".btnEdit").click(function() {
		var id = $(this).attr('data-id')
		var url='{{caption.route}}' + id + '?_method=PATCH'
		$('#editForm').attr('action', url)

		@each(item in data)
		if (id=='{{ item.id }}') {
			$('#businessName').val('{{item.businessName}}')
			$('#commissionRate').val('{{ item.commissionRate }}')
			$('#isActive').val('{{ item.isActive }}')
		}
		@endeach
		setTimeout(()=>{
			$('#{{fields[0].n}}').focus()
		}, 500)
	})

	$("#btnExport").click(function() {
		let table = document.getElementById("tblData");;

		let rows =[];
		let i=0;
		for(const row of table.rows){
			i++
			if(i>1){
				rows.push(
					[
						i-1,
						row.cells[0].innerText.toString(),
						row.cells[2].innerText.toString(),
						row.cells[3].innerText.toString(),
					]
				);
			}
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(";")).join("\\n");

        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "StockAccount_Report.csv");
        document.body.appendChild(link);

        link.click();
	})

	@if(old('editModalShow'))
		$("#btnEdit{{old('id')}}").click()
	@endif
});
</script>
@endsection
