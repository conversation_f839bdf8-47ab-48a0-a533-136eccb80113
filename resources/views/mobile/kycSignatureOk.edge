@layout('layout.mobile')

@section('title')

@endsection

@section('css')
<style>
	canvas {
		border: 1px solid black;
		width: 300px;
		height: 180px;
		background-color: white;
	}
</style>
@endsection

@section('content')
	<div class="row">
		<div class="col-12 text-center">
			<h4>Die nächsten Schritte</h4>
			<div class="card text-left">
				<div class="card-body">
					<form id="kycForm" action="{{ route('kycSignatureOk') }}" class="form" method="POST" enctype="multipart/form-data">
						{{ csrfField() }}
						<input type="hidden" name="id" value="{{ data.id }}">
						
						<div class="row align-items-center mt-3">
							<div class="col text-center">
								<canvas id="signature-pad"></canvas>
							</div>
						</div>
						<div id="pnlHide">
							<div class="row align-items-center mt-5">
								<div class="col-11 text-center mm-fs-16 mx-auto">
									<a href="#" id="btnClear" class="btn btn-sm">Clear</a>
								</div>
							</div>
							
							<input type="hidden" id="signature" name="signatureFile" value="">
							
							<div class="input-group mt-5">
								<button id="btnSubmit" class="btn btn-primary mx-auto mm-fs-12 kycButton">Weiter</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
@endsection

@section('script')
<script src="https://cdnjs.cloudflare.com/ajax/libs/signature_pad/1.3.5/signature_pad.min.js" integrity="sha512-kw/nRM/BMR2XGArXnOoxKOO5VBHLdITAW00aG8qK4zBzcLVZ4nzg7/oYCaoiwc8U9zrnsO9UHqpyljJ8+iqYiQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
var canvas = document.getElementById("signature-pad");

function resizeCanvas() {
	var ratio = Math.max(window.devicePixelRatio || 1, 1);
	canvas.width = canvas.offsetWidth * ratio;
	canvas.height = canvas.offsetHeight * ratio;
	canvas.getContext("2d").scale(ratio, ratio);
}
window.onresize = resizeCanvas;
resizeCanvas();

var signaturePad = new SignaturePad(canvas, {
	backgroundColor: 'rgb(250,250,250)'
});

$( function() {
	$('#btnClear').click(function(){
		signaturePad.clear();
	});
	
	$("#kycForm").submit(function(e){
    	$('#signature').val(canvas.toDataURL('image/png'))
  	});
});
</script>
@endsection
