@layout('layout.mobile')

@section('title')

@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	<div class="row">
		<div class="col-12 text-center">
			<h4>Die nächsten Schritte</h4>
			<div class="card text-left">
				<div class="card-body">
					<form id="kycForm" action="{{ route('kycPhotoSelfie') }}" class="form" method="POST" enctype="multipart/form-data">
						{{ csrfField() }}
						<input type="hidden" name="id" value="{{ data.id }}">
						
						<div class="row align-items-center mt-3">
							<div class="col text-center"><img id="imgCol" src="images/idBack.png" alt="" height="180px"></div>
						</div>
						<div id="pnlHide">
							<div class="row align-items-center mt-3">
								<div class="col-10 text-center mm-fs-16 mx-auto">
									<p>Bitte fotografieren
										Sie jetzt die
										<u>Rückseite</u>
										<PERSON><PERSON><PERSON></p>
								</div>
							</div>
							<div class="row align-items-center" id="HiddenFileRow" hidden>
								<div class="col">
									<div class="input-group">
										<div class="input-group-prepend">
											<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
										</div>
										<div class="custom-file">
											<input type="file" class="form-control custom-file-input"
													id="backIdInput" name="backIdFile" data-toggle="tooltip" data-placement="top" 
													title="Vorseite" accept="image/*" capture required>
											<label id="backIdLabel" class="custom-file-label text-truncate" for="backIdInput" data-browse="Scan">Vorseite</label>
											<input type="hidden" id="backId" name="backId" value="">
										</div>
									</div>
								</div>
							</div>
							<div class="input-group mt-4">
								<button id="btnSubmit" class="btn btn-primary mx-auto mm-fs-12 kycButton">OK, ich bin bereit!</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
@endsection

@section('script')
<script>
$( function() {
	$('input[type="file"]').change(function(e){
        var fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
		takeAndSend();
    });
	
	$('#btnSubmit').click(function(){
		$("#backIdLabel").click()
	});
	
	function takeAndSend(){
		$("#pnlHide").attr("hidden", "hidden")
		$("#imgCol").removeAttr("src")
		$("#imgCol").attr("src", "images/ok.png")
		var backId = $('#backId').val();
		if (backId == ''){
			$("#pnlHide").removeAttr("hidden")
			$("#imgCol").attr("src", "images/idFront.png")
		} else {
			$("#kycForm").submit();
		}
	}
});
</script>
@endsection
