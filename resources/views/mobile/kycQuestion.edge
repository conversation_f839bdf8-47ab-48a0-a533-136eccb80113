@layout('layout.mobile')

@section('title')

@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	<div class="row">
		<div class="col-12 text-center">
			<h4>Fragen [ {{data.name}} {{data.surname}} ]</h4>
			<div class="card text-left">
				<div class="card-body p-4">	
					<form action="{{ route('kycFinish') }}" class="form" method="POST">
						{{ csrfField() }}
						<input type="hidden" name="id" value="{{ data.id }}">
						
						<div class="row align-items-center">
							<p>
								Was ist der Zweck der mit Tele Jet Com AG einzugehenden Geschäftsbeziehung?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer1" id="answer1" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="voucherkarten"><PERSON><PERSON><PERSON> Voucher-Karten</option>
										<option value="telefonkarten">Einkauf Telefonkarten</option>
										<option value="zahlungsverkehr">Zahlungsverkehrsdienstleistungen</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Wie hoch ist der gesamte Betrag in CHF, den Sie jetzt investieren möchten?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer2" id="answer2" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="unter_2500">Unter 2‘500</option>
										<option value="uber_2500">Über 2‘500</option>
										<option value="uber_10000">Über 10‘000</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								In Bezug auf die Entwicklung der Geschäftsbeziehung mit Tele Jet Com AG: Wie viel planen Sie in Zukunft zu investieren oder überweisen (in CHF)?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer3" id="answer3" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="weniger_10000">weniger als 10’000</option>
										<option value="10000_30000">10’001 bis 30’000</option>
										<option value="30000_100000">30’000 bis 100’000</option>
										<option value="100000_300000">100’001 bis 300’000</option>
										<option value="300000_1000000">300’001 bis 1’000’000</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Woher stammen diese Gelder?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer4" id="answer4" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="espartes">Erspartes</option>
										<option value="genossenschaft">Geschäftstätigkeit</option>
										<option value="erbschaft">Erbschaft</option>
										<option value="andere_quellen">andere Quellen</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Handeln Sie in Ihrem eigenen Namen oder im Namen einer anderen Person oder einer Gesellschaft?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer5" id="answer5" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="selbst">Ich handle für mich selbst</option>
										<option value="andere">Ich handle im Namen einer anderen Person</option>
										<option value="gesellschaft">Ich handle im Namen einer Gesellschaft</option>
										<option value="unternehmen">Ich handle im Namen meines Unternehmens</option>
										<option value="keine">Keine der obigen Möglichkeiten</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Welche Rolle/Funktion nehmen Sie bei Ihrer beruflichen Tätigkeit wahr?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer6" id="answer6" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="mitarbeiter">Mitarbeiter</option>
										<option value="kader">Mittleres Kader</option>
										<option value="fuehrer">Führungskraft</option>
										<option value="selbststaetig">Selbstständiger</option>
										<option value="nicht_erwerbstaetig">Nicht erwerbstätig</option>
										<option value="andere">Andere</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4" id="answer7Row">
							<p>
								Wie lautet der Name Ihres Arbeitgebers?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<input type="text" class="form-control" name="answer7" id="answer7" value="{{ old('answer7') }}" maxlength="500">
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4" id="answer8Row">
							<p>
								Wie lautet die Adresse von Ihrem Arbeitgeber?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<input type="text" class="form-control" name="answer8" id="answer8" value="{{ old('answer8') }}" maxlength="500">
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4" id="answer9Row">
							<p>
								In welcher Branche ist Ihr Arbeitgeber tätig?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<input type="text" class="form-control" name="answer9" id="answer9" value="{{ old('answer9') }}" maxlength="500">
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Sind Sie involviert im Handel mit Munition und Waffen, rohen Edelsteinen / Diamanten, Schmuck, in internationalem Handel mit exotischen Tieren, in Casino- und Lotteriegeschäften oder im Erotikgewerbe?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer10" id="answer10" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="ja">Ja</option>
										<option value="nein">Nein</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Bitte bestätigen Sie die Höhe Ihres jährlichen Einkommens (in CHF).
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer11" id="answer11" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="weniger_10000">weniger als 10’000</option>
										<option value="10000_30000">10’001 bis 30’000</option>
										<option value="30000_100000">30’000 bis 100’000</option>
										<option value="100000_300000">100’001 bis 300’000</option>
										<option value="uber_300000">über 300’000</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Bitte bestätigen Sie die Höhe Ihres Vermögens (in CHF).
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer12" id="answer12" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="weniger_10000">weniger als 10’000</option>
										<option value="10000_30000">10’001 bis 30’000</option>
										<option value="30000_100000">30’000 bis 100’000</option>
										<option value="100000_300000">100’001 bis 300’000</option>
										<option value="300000_1000000">300’001 bis 1’000’000</option>
										<option value="uber_1000000">über 1’000’000</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Bitte spezifizieren Sie die Höhe Ihrer Schulden (in CHF ohne Hypotheken)
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer13" id="answer13" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="weniger_10000">weniger als 10’000</option>
										<option value="10000_30000">10’001 bis 30’000</option>
										<option value="30000_100000">30’000 bis 100’000</option>
										<option value="100000_300000">100’001 bis 300’000</option>
										<option value="300000_1000000">300’001 bis 1’000’000</option>
										<option value="uber_1000000">über 1’000’000</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<p>
								Können Sie über die eingebrachten Gelder uneingeschränkt verfügen?
							</p>
							<div class="col-12 mx-auto">
								<div class="input-group">
									<select class="form-control custom-select" name="answer14" id="answer14" required>
										<option value="" selected hidden>Bitte auswählen</option>
										<option value="ja">Ja</option>
										<option value="nein">Nein</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-4">
							<div class="input-group">
								<button type="submit" class="btn btn-primary mx-auto kycButton">Ich stimme den Antworten zu</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
@endsection

@section('script')
<script>
$( function() {
	$("#answer6").change(function(){
		const selectedAnswer=$(this).val();
		$("#answer7").attr('required', false);
		$("#answer8").attr('required', false);
		$("#answer9").attr('required', false);
		
		$("#answer7Row").show();
		$("#answer8Row").show();
		$("#answer9Row").show();
			
		if(selectedAnswer=="nicht_erwerbstaetig"){
			$("#answer7Row").hide();
			$("#answer8Row").hide();
			$("#answer9Row").hide();
		}else if(selectedAnswer=="andere"){
			$("#answer7").attr('required', false);
			$("#answer8").attr('required', false);
			$("#answer9").attr('required', false);
		}else{
			$("#answer7").attr('required', true);
			$("#answer8").attr('required', true);
			$("#answer9").attr('required', true);
		}
	})
});
</script>
@endsection
