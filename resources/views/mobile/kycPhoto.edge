@layout('layout.mobile')

@section('title')

@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	<div class="row">
		<div class="col-12 text-center">
			<h3>Scan/Foto</h3>
			<div class="card text-left">
				<div class="card-body">
					<form action="{{ route('kycUploadPhoto') }}" class="form" method="POST" enctype="multipart/form-data">
						{{ csrfField() }}
						<input type="hidden" name="id" value="{{ data.id }}">
						
						<div class="row align-items-center">
							<div class="col text-center"><img src="images/id1.png" alt="" height="160px"></div>
						</div>
						<div class="row align-items-center">
							<div class="col">
								<div class="input-group">
									<div class="input-group-prepend">
										<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
									</div>
									<div class="custom-file">
										<input type="file" class="form-control custom-file-input"
												id="frontIdInput" name="frontIdFile" data-toggle="tooltip" data-placement="top" 
												title="Vorseite" accept="image/*" capture required>
										<label id="frontIdLabel" class="custom-file-label text-truncate" for="frontIdInput" data-browse="Scan">Vorseite</label>
										<input type="hidden" id="frontId" name="frontId" value="">
									</div>
								</div>
								<div class="input-group mt-1">							
									<div class="input-group-prepend">
										<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
									</div>
									<div class="custom-file">
										<input type="file" class="form-control custom-file-input"
												id="backIdInput" name="backIdFile" data-toggle="tooltip" data-placement="top" 
												title="Rückseite" accept="image/*" capture required>
										<label id="backIdLabel" class="custom-file-label text-truncate" for="backIdInput" data-browse="Scan">Rückseite</label>
										<input type="hidden" id="backId" name="backId" value="">
									</div>
								</div>
							</div>
						</div>
						
						<div class="row align-items-center mt-1">
							<div class="col text-center"><img src="images/id2.png" alt="" height="160px"></div>
						</div>
						<div class="row">
							<div class="col">
								<div class="input-group">
									<div class="input-group-prepend">
										<div class="input-group-text"><i class="fa fa-camera fa-fw"></i></div>
									</div>
									<div class="custom-file">
										<input type="file" class="form-control custom-file-input"
												id="selfieInput" name="selfieFile" data-toggle="tooltip" data-placement="top" 
												title="Gesichtsfoto" accept="image/*" capture required>
										<label id="selfieLabel" class="custom-file-label text-truncate" for="selfieInput" data-browse="Foto">Gesichtsfoto</label>
										<input type="hidden" id="selfie" name="selfie" value="">
									</div>
								</div>
							</div>
						</div>
						<div class="input-group mt-4">
							<button type="submit" class="btn btn-primary mx-auto mm-fs-12 kycButton">Erledigt</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
@endsection

@section('script')
<script>
$( function() {
	$('input[type="file"]').change(function(e){
        var fileName = e.target.files[0].name;
		$(this).next('.custom-file-label').text(fileName);
		$(this).next('.custom-file-label').next('input[type=hidden]').val(fileName);
    });
});
</script>
@endsection
