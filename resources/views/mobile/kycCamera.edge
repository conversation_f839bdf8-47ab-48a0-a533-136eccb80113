@layout('layout.mobile')

@section('title')

@endsection

@section('css')
<style>

</style>
@endsection

@section('content')
	<h3>Camera</h3>
	<video id="video" width="320" height="240" controls></video>
	<canvas id="canvas" width="320" height="240"></canvas>
	
	<button id="btn-fotograf">Fotoğra<PERSON></button>

	<form action="#" method="post" enctype="multipart/form-data">
		<input type="file" id="fotograf" name="fotograf" accept="image/*" required>
		<button type="submit">Gönder</button>
	</form>

@endsection

@section('script')
<script>
	const video = document.getElementById('video');
	const btnFotograf = document.getElementById('btn-fotograf');
	const fotografInput = document.getElementById('fotograf');

	// Kullanıcının kamerasına erişim izni isteme
	navigator.mediaDevices.getUserMedia({
		video: true,
		audio: false,
	}).then((stream) => {
		video.srcObject = stream;
		video.play();
	});

	btnFotograf.addEventListener('click', () => {
		const canvas = document.createElement('canvas');
		canvas.width = video.videoWidth;
		canvas.height = video.videoHeight;

		const ctx = canvas.getContext('2d');
		ctx.drawImage(video, 0, 0);

		// Çekilen fotoğrafın base64 kodunu `input file` değerine ata
		fotografInput.value = canvas.toDataURL('image/png');
	});

	// Form submit edildiğinde video akışı durdurulur
	document.querySelector('form').addEventListener('submit', () => {
		video.srcObject.getTracks().forEach(track => track.stop());
	});

/*
	const canvas = document.getElementById("canvas");

	navigator.mediaDevices.getUserMedia({
		video: true,
		audio: false,
	}).then((stream) => {
		video.srcObject = stream;
		video.play();

		const context = canvas.getContext("2d");
  		context.drawImage(video, 0, 0, canvas.width, canvas.height);

		const drawBorder = () => {
			context.clearRect(0, 0, canvas.width, canvas.height);

			// Yüzün koordinatlarını kullanarak dairesel bir border çizin...

			context.fillStyle = "black";
			context.beginPath();
			context.arc(x, y, radius, 0, 2 * Math.PI);
			context.fill();
		};

		// Her saniye borderı yeniden çizin...
		setInterval(drawBorder, 1000);
	});

	function startCamera() {
		const dataURL = canvas.toDataURL("image/png");

		// Fotoğrafı bir bağlantı olarak kaydetmek için:
		const link = document.createElement("a");
		link.href = dataURL;
		link.download = "fotograf.png";
		link.click();
	}

	function startCamera() {
		video.srcObject = stream;
		video.play();
	}

	function stopCamera() {
		video.srcObject = null;
		video.pause();
	}
*/
$( function() {
	
});
</script>
@endsection
