@import url('https://fonts.googleapis.com/css?family=Montserrat:300,700');
/*@import url('https://fonts.googleapis.com/css?Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900');*/
@import url('https://fonts.cdnfonts.com/css/helvetica-neue-55?styles=30124');

@media print {
  main {
    margin-top: -100px;
    padding-top: 0;
  }

  table.mm-print tbody tr.bg-light td {
    background-color: #f5f4f4 !important;
    print-color-adjust: exact;
  }
}

html {
  position: relative;
  min-height: 100%;
  height: 100%;
  width: 100%;
}

body.mm-desktop {
  padding-top: 4.5rem;
  /*font-family: 'Montserrat', sans-serif;*/
  /*font-weight: 300;*/
  background-color: rgb(255, 255, 255);
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}

body.mm-mobile {
  padding-top: 1.0rem;
  /* font-family: 'Roboto'; */
  font-family: 'Helvetica Neue', sans-serif;
  /*font-weight: 300;*/
  background-color: rgb(255, 255, 255);
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}

* {
  margin: 0;
  padding: 0;
}

.navbar {
  background-color: rgb(255, 255, 255);
  border-bottom: 10px solid transparent;
  border-image: linear-gradient(to left, rgb(228, 201, 29), white) 10 stretch;
}

.nav-item.active {
  border-bottom: 2px solid rgb(173, 137, 78);
}

.nav-item:not(.no-line):hover {
  border-bottom: 2px dotted rgb(173, 137, 78);
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 60px;
  line-height: 60px;
  /* Vertically center the text there */
  background-color: #f5f5f5;
}

/* fonts */
.mm-fs-6 {
  font-size: 0.6em;
  text-shadow: none;
}

.mm-fs-8 {
  font-size: 0.8em;
  text-shadow: none;
}

.mm-fs-12 {
  font-size: 1.2em;
}

.mm-fs-14 {
  font-size: 1.4em;
}

.mm-fs-16 {
  font-size: 1.6em;
}

.mm-fs-22 {
  font-size: 2.2em;
}

.mm-baslik-div {
  margin: 0em 4em 0em 2.1em;
  height: 2.6em !important;
  text-align: center !important;
}

.mm-ustbaslik {
  font-size: 1em;
  padding-top: 0.2em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mm-altbaslik {
  font-size: 0.8em;
  font-weight: normal;
  text-shadow: none;
}

.mm-kaydir {
  white-space: normal !important;
}

.mm-ortala {
  margin: 0 auto !important;
}

.mm-t-ortala {
  text-align: center !important;
}

.mm-t-sol {
  text-align: left !important;
}

.mm-t-sag {
  text-align: right !important;
}

.mm-ngolge {
  text-shadow: none;
}

.mm-normal {
  text-decoration: none;
  text-shadow: none;
}

.mm-nkalin {
  font-weight: normal;
}

/*colors*/
.mm-gri {
  color: DARKGREY !important;
}

.mm-kirmizi {
  color: MAROON !important;
}

.mm-pkirmizi {
  color: RED !important;
}

.mm-mavi {
  color: STEELBLUE !important;
}

.mm-pmavi {
  color: rgb(8, 84, 146) !important;
}

.mm-mor {
  color: DARKVIOLET !important;
}

.mm-beyaz {
  color: WHITE !important;
}

.mm-yesil {
  color: OLIVE !important;
}

.mm-pyesil {
  color: rgb(20, 88, 6) !important;
}

.mm-tamyesil {
  color: rgb(41, 190, 11) !important;
}

.mm-tamsari {
  color: #f6dd68 !important;
}

.mm-turuncu {
  color: #DF7401 !important;
}

.mm-siyah {
  color: black !important;
}

/*bg colors*/
.mm-bg-beyaz {
  background-color: WHITE !important;
}

.mm-bg-sari {
  background-color: BEIGE !important;
}

.mm-bg-pudra {
  background-color: MISTYROSE !important;
}

.mm-bg-gri {
  background-color: LIGHTSLATEGRAY !important;
}

.mm-bg-agri {
  background-color: rgb(226, 227, 227) !important;
}

.mm-bg-yesil {
  background-color: OLIVE !important;
}

.zoom {
  transition: transform .2s;
  /* Animation */
  margin: 0 auto;
}

.zoom:hover {
  z-index: 2;
  position: absolute;
  transform: scale(3);
  /* (300% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
}

.preview-zoom {
  transition: transform .2s;
  /* Animation */
  margin: 0 auto;
}

.preview-zoom:hover {
  z-index: 2;
  position: absolute;
  transform: scale(1.5);
  left: 48px;
}

.mm-min {
  min-width: 112px;
}

.kycButton {
  border-radius: 0%;
  background-color: #2E69AD;
  width: 100%;
}
