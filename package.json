{"name": "adonis-fullstack-app", "version": "4.1.0", "adonis-version": "4.1.0", "description": "The fullstack application boilerplate for Adonisjs", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "test": "node ace test", "build": "node ace build --production", "dev": "node server.js --dev"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "adonis-app"], "author": "", "license": "UNLICENSED", "private": true, "dependencies": {"@adonisjs/ace": "^5.0.8", "@adonisjs/auth": "^3.0.7", "@adonisjs/bodyparser": "^2.0.5", "@adonisjs/cors": "^1.0.7", "@adonisjs/fold": "^4.0.9", "@adonisjs/framework": "^5.0.9", "@adonisjs/ignitor": "^2.0.8", "@adonisjs/lucid": "^6.1.3", "@adonisjs/mail": "^3.1.0", "@adonisjs/session": "^1.0.27", "@adonisjs/shield": "^1.0.8", "@adonisjs/validator": "^5.0.6", "adonis-scheduler": "^3.0.2", "axios": "^1.7.0", "jspdf": "^2.5.1", "moment": "^2.29.1", "mysql": "^2.17.1", "sharp": "^0.33.5"}, "autoload": {"App": "./app"}}