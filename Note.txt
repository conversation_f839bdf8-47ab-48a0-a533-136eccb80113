#Sunucuya SSH ile bağlanıp
HOST tradecenterch.switzerlandnorth.cloudapp.azure.com
USER tradecenterch

#buraya girip
cd /home/<USER>/repos/trade-center-docker-images/prod 

#bu çalıştırılacak
sudo docker system prune && sudo docker compose build --no-cache terminal-tradecenter24 report-tradecenter24 report-forex724 && sudo docker compose up --build --force-recreate --no-deps -d terminal-tradecenter24 report-tradecenter24 report-forex724 && sudo docker compose up --build --force-recreate --no-deps -d nginx-server

#SSH Terminal doğrudan -> tradecenterch@TradeCenterCH:~/repos$
cd trade-center-docker-images/prod
sudo docker system prune 
sudo docker compose build --no-cache terminal-tradecenter24 report-tradecenter24 report-forex724 
sudo docker compose up --build --force-recreate --no-deps -d terminal-tradecenter24 report-tradecenter24 report-forex724 
sudo docker compose up --build --force-recreate --no-deps -d nginx-server
