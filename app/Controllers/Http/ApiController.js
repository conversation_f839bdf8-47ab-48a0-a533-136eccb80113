'use strict'

const BusinessService = use("App/Helpers/BusinessService");

const Common = use('App/Helpers/Common')
const SMS = use('App/Helpers/Sms')

const Database = use("Database")
const Customer = use("App/Models/Customer")
const WinnerPhone = use("App/Models/WinnerPhone")

const axios = require("axios")

class ApiController {

    async checkCustomer({ response, session, params }) {
        const checkingCustomer = await Customer.find(params.id);

        try {
            response.header('Content-type', 'application/json')

            if (checkingCustomer) {
                return response.json(checkingCustomer)
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.json(error)
        }
    }

    async checkLoginCustomer({ response, session, params }) {
        let loginId = parseInt(params.id)
        const checkingCustomer = await Customer.findBy('login', loginId);

        try {
            response.header('Content-type', 'application/json')

            if (checkingCustomer) {
                return response.json(checkingCustomer)
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.json(error)
        }
    }

    //json post yakalamak için test amaçlı
    async updateCustomer({ request, response, session, params }) {
        const checkingCustomer = await Customer.find(params.id);

        try {
            response.header('Content-type', 'application/json')

            if (checkingCustomer) {
                const bestFormat = request.accepts(['json'])

                if (bestFormat === 'json') {
                    const body = request.post()
                    const phone = body.phone
                    const credit = body.profit

                    if (phone.length > 7 && credit > 0) {
                        await WinnerPhone.create(
                            {
                                phone: phone,
                                credit: credit
                            }
                        );

                        const setting = await BusinessService.GetSettings();

                        const link = 'https://client.forex-724.com/register/ib-client/24a47f97-aa5d-4942-ae62-cec573eef0e5?demo=false&branchUuid=622284c0-ec79-4e1a-97ac-ac0cd1f310c1'
                        const message = `Registrieren Sie jetzt ein Konto, um Ihre Prämie zu erhalten. Klicken Sie auf diesen Link, um ein Konto zu eröffnen! ${link}`

                        //SMS.sendSMS(setting.toJSON(), phone, 'Alternate', message);

                        return response.json({ success: true })
                    } else {
                        return response.status(400).send('Phone must be longer than 7 characters and profit must be greater than 0')
                    }

                } else {
                    return response.status(400).send('Bad request')
                }
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).json(error)
        }
    }
}

module.exports = ApiController
