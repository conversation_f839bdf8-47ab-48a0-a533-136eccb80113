'use strict'

const Common = use('App/Helpers/Common');
const BusinessService = use("App/Helpers/BusinessService");

const Database = use("Database")
const BankAccount = use("App/Models/BankAccount")
const Invoice = use("App/Models/Invoice")
const InvoiceSetting = use("App/Models/InvoiceSetting")
const InvoiceTax = use("App/Models/InvoiceTax")
const Commissioner = use("App/Models/Commissioner")
const Customer = use("App/Models/Customer")
const Sag = use("App/Models/Sag")
const Bfi = use("App/Models/Bfi")
const ForexReport = use("App/Models/ForexReport")
const MtcReport = use("App/Models/MtcReport")
const Institute = use("App/Models/Institute")

const axios = require("axios")
const querystring = require('querystring');

//const moment = require("moment");
const moment = require('moment-timezone');
//moment.tz.setDefault("UTC");
moment.tz.setDefault("GMT+2");
moment.defaultFormat = "DD.MM.YYYY HH:mm:ss"

const { type } = require("os");
const { description } = require("@adonisjs/ace/lib/commander");
const { timeStamp, log, group } = require("console");
const { title } = require("process");
const { isNumber } = require("util");
const { isNumberObject } = require("util/types");

const appUrl = process.env.APP_URL
let dateTimeNow = moment().format('YYYY-MM-DD HH:mm:ss')

const customerFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'surname', c: 'Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'name', c: 'Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'gender', c: 'M/F', i: 'fas fa-venus-mars fa-fw', t: 'select', v: Common.Basic.genders, e: true },
	{ n: 'email', c: 'Email', i: 'fas fa-at fa-fw', t: 'email', v: '', e: true },
	{ n: 'phone', c: 'Phone', i: 'fas fa-phone fa-fw', t: 'text', v: '', e: true },
	{ n: 'avenue', c: 'Street', i: 'fas fa-map-marker fa-fw', t: 'text', v: '', e: true },
	{ n: 'address', c: 'Address', i: 'fas fa-map-marked-alt fa-fw', t: 'text', v: '', e: false },
	{ n: 'financeinstitute', c: 'Institute', i: 'fas fa-university fa-fw', t: 'select', v: [], e: true },
	{ n: 'leverage', c: 'Leverage', i: 'fas fa-percent fa-fw', t: 'text', v: '1:1', e: false, l: 'Leverage' },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: false, l: 'Trader ID' },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: false },
	{ n: 'nickname', c: 'Nickname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false },

	{ n: 'balance', c: 'Balance', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Balance' },
	{ n: 'equity', c: 'Equity', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Equity' },
	{ n: 'deposit', c: 'Total Deposit', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Total Deposit' },
	{ n: 'withdrawalCapacity', c: 'Withdrawal Capacity', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Withdrawal Capacity' },

	{ n: 'companyName', c: 'Company Name', i: 'fas fa-building fa-fw', t: 'text', v: '', e: true },
	{ n: 'commissionRate', c: 'Commission Rate', i: 'fas fa-percent', t: 'number', v: 0, e: true },
]

const ccFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'name', c: 'Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: false, ne: true, l: 'Trader ID' },
	{ n: 'ownCommissionRate', c: 'Own Rate', i: 'fas fa-percent', t: 'number', v: 0, e: false, ne: true, l: 'Own Rate' },
	{ n: 'webCommissionRate', c: 'Web Rate', i: 'fas fa-percent', t: 'number', v: 0, e: false, ne: true, l: 'Web Rate' },
	{ n: 'businessName', c: 'Business Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'title', c: 'Title', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'businessAddress', c: 'Business Address', i: 'fas fa-map-marked-alt fa-fw', t: 'text', v: '', e: true, ne: true },
]

const entryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'date', c: 'Date', i: 'fas fa-calendar-alt fa-fw', t: 'date', v: dateTimeNow, e: false, l: 'Date' },
	{ n: 'amount', c: 'Amount', i: 'fas fa-hand-holding-usd', t: 'money', v: 0, e: false, l: 'Amount' },
	{ n: 'comment', c: 'Comment', i: 'fas fa-comment fa-fw', t: 'text', v: '', e: false },
]

const mtcFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'transactionId', c: 'Transaction ID', i: 'fas fa-tag fa-fw', t: 'number', v: 0, e: false, ne: false },
	{ n: 'invoiceNumber', c: 'Invoice Nr', i: 'fas fa-file-invoice fa-fw', t: 'number', v: 0, e: false, ne: false },
	{ n: 'period', c: 'Period', i: 'fas fa-calendar fa-fw', t: 'text', v: '', e: false, ne: false },

	{ n: 'date', c: 'Date', i: 'fas fa-calendar-alt fa-fw', t: 'dateTime', v: dateTimeNow, e: true, ne: true, l: 'Date' },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'name', c: 'Name/Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'gateway', c: 'Gateway', i: 'fas fa-money-check-alt fa-fw', t: 'select', v: Common.Basic.transactionGateways, e: true, ne: true, l: 'Gateway' },
	{ n: 'type', c: 'Type', i: 'fas fa-exchange-alt fa-fw', t: 'select', v: Common.Basic.transactionTypes, e: true, ne: true, l: 'Type' },
	{ n: 'amount', c: 'Amount', i: 'fas fa-hand-holding-usd fa-fw', t: 'money', v: 0, e: true, ne: true, l: 'Amount' },
	{ n: 'commission', c: 'Commission', i: 'fas fa-hand-holding-usd fa-fw', t: 'money', v: 0, e: true, ne: true, l: 'Commission' },
	{ n: 'commissionRate', c: 'Commission Rate', i: 'fas fa-percent fa-fw', t: 'money', v: 0, e: true, ne: true, l: 'Rate' },
	{ n: 'comment', c: 'Comment', i: 'fas fa-comment fa-fw', t: 'textarea', v: '', e: true, ne: true },
]

const invoiceFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'invoiceNumber', c: 'Invoice Nr', i: 'fas fa-file-invoice fa-fw', t: 'number', v: 0, e: false, ne: false, l: 'Invoice Nr' },
	{ n: 'date', c: 'Date', i: 'fas fa-calendar-alt fa-fw', t: 'date', v: dateTimeNow, e: true, l: 'Date' },
	{ n: 'requestedCredit', c: 'Requested Credit', i: 'fas fa-hand-holding-usd', t: 'money', v: 0, e: true, l: 'Requested Credit' },
	{ n: 'previousCredit', c: 'Previous Credit', i: 'fas fa-hand-holding-usd', t: 'money', v: 0, e: true, l: 'Previous Credit' },
]

const state = {
	rangeStart: 0,
	rangeEnd: 1,
	customerLoginList: [],
	forexData: [],
	mtcData: [],
}

const beginTimeDifference = 10800000
const endTimeDifference = 75600000//50400000 //79200000

class ReportController {
	periods = []

	constructor() {
		let periodListId = 0
		const firstOfThisYear = new Date("01.01." + moment().year())

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().subtract(10, 'year').startOf('year').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().endOf('year').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().startOf('week').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().endOf('week').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().subtract(6, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().endOf('year').format('YYYY-MM-DD HH:mm:ss'),
		});

		this.periods.push({
			id: periodListId++,
			title: Common.Report.mainPeriods[periodListId].title,
			en: Common.Report.mainPeriods[periodListId].en,
			beginDate: moment().subtract(1, 'year').startOf('year').format('YYYY-MM-DD HH:mm:ss'),
			endDate: moment().subtract(1, 'year').endOf('year').format('YYYY-MM-DD HH:mm:ss'),
		});

		const thisMonthNumber = moment().month() + 1
		for (let i = thisMonthNumber; i >= 1; i--) {
			const beginDate = new Date(i + ".01." + moment().year())
			this.periods.push({
				id: periodListId++,
				title: moment(beginDate).locale('de').format(`MMMM`) + " " + moment().format("YYYY"),
				en: moment(beginDate).locale('en').format(`MMMM`) + " " + moment().format("YYYY"),
				beginDate: moment(beginDate).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
				endDate: moment(beginDate).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
			});
		}

		for (let i = 12; i >= 1; i--) {
			const beginDate = new Date(i + ".01." + moment(firstOfThisYear).subtract(1, 'year').format("YYYY"))
			this.periods.push({
				id: periodListId++,
				title: moment(beginDate).locale('de').format(`MMMM`) + " " + moment(beginDate).format("YYYY"),
				en: moment(beginDate).locale('en').format(`MMMM`) + " " + moment(beginDate).format("YYYY"),
				beginDate: moment(beginDate).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
				endDate: moment(beginDate).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
			});
		}

	}

	async getAll({ request, response, view, session }) {
		const caption = {
			navHome: "active",
			icon: 'fas fa-home fa-fw',
			pageTitle: "Reports Home",
			route: '/home/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const list = await Bfi.query().orderBy('id', 'desc').fetch()

		return view.render("reports.all", {
			caption: caption,
			data: list.toJSON(),
			setting: setting,
		})
	}

	/*TRADECENTER24 Functions*/
	async getPeriodByParams(params) {
		//http://localhost:3000/report/bfis/period/8/1733011200000/1735603200000
		let periodId = 8
		if (params.periodId) {
			periodId = params.periodId
		}

		const period = this.periods.find(x => x.id == periodId)
		let begin = new Date(period.beginDate).getTime()
		let end = new Date(period.endDate).getTime()

		if (params.begin && params.end) {
			begin = params.begin
			end = params.end
		}

		let periodDescription = period.title
		if (params.periodId == 0) {
			periodDescription = begin + " - " + end
		}

		const rangeStart = Math.floor(begin) - beginTimeDifference;
		const rangeEnd = Math.floor(end) + endTimeDifference;

		return { period, periodDescription, rangeStart, rangeEnd }
	}

	async getCustomerList(loginList) {
		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.whereIn('person.login', loginList)
				this.whereIn('person.financeinstitute', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
			}).orderBy('person.id', 'desc');

		const customerList = customers.map(x => {
			return {
				id: x.id,
				login: x.login,
				name: x.name,
				surname: x.surname,
				instituteId: x.financeinstitute,
			}
		});

		return customerList
	}

	async getEntriesFromApi(setting, institutes, customerList, rangeStart, rangeEnd) {

		const activeInstitutes = institutes.filter(x => x.moneyTransferActive == 1)
		const entries = []
		const ledgerTypes = [4]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

		for (const institute of activeInstitutes) {
			const loginList = customerList.filter(x => x.instituteId == institute.id)
			if (loginList.length == 0) {
				continue
			}

			try {
				//Trader WEB API
				const managerId = institute.restApiUser;
				const password = institute.restApiPassword;

				const resAuth = await axios.post(`${institute.restApiBaseUrl}/v1/register/register`,
					{
						managerID: managerId,
						password: password
					}
				)

				const resGetEntries = await axios.post(`${institute.restApiBaseUrl}/v1/ledger/getEntries`,
					{
						auth: {
							managerID: managerId,
							token: resAuth.data.token
						},
						rangeStart: rangeStart,
						rangeEnd: rangeEnd,
						clientIds: [...loginList.map(x => x.login)],
						ledgerTypes: [...ledgerTypes] //ledgerType
					}
				)

				entries.push(...resGetEntries.data.ledgerEntry)

			} catch (error) {
				console.log(error)
			}
		}

		let provisionList = []
		institutes.filter(x => x.moneyTransferActive == 1).forEach(institute => {
			provisionList.push({
				id: institute.id,
				title: institute.title,
				value: 0
			})
		})

		const allCustomers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.whereIn('person.financeinstitute', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
			}).orderBy('person.id', 'desc');


		let totalProvision = 0
		let data = []

		for (const item of entries.filter(x => x.additionalType == "8")) {
			const instituteId = customerList.find(x => x.login == parseInt(item.clientId)).instituteId
			let instituteName = institutes.find(x => x.id == instituteId).title

			const ledgerLogin = item.comment.substring(item.comment.indexOf('comm:') + 5, 200).split('-')[0]

			let ledgerName = ""
			let agentLogin = ""
			let groupName = ""
			let source = ""
			const customer = allCustomers.find(x => x.login == ledgerLogin)
			if (customer) {
				ledgerName = customer.name + ' ' + customer.surname
				agentLogin = customer.agentLogin
				groupName = customer.groupName
				source = customer.source
			}

			const amount = Number(item.amount / 100)

			provisionList.find(x => x.id == instituteId).value += amount

			totalProvision += amount

			data.push({
				id: item.uid,
				type: item.additionalType,
				date: moment.unix(Number(item.generatedTime) / 1000).format('DD.MM.YYYY HH:mm:ss'),
				comment: item.comment,
				clientId: parseInt(item.clientId),
				login: ledgerLogin,
				agentLogin: agentLogin,
				name: ledgerName,
				amount: Number(item.amount / 100),
				institute: instituteName,
				instituteId: instituteId,
				groupName: groupName,
				source: source,
			})
		}

		return {
			data: data,
			provisionList: provisionList,
			totalProvision: totalProvision
		}

	}

	async getOrderedData(data, orderField) {
		let orderBy = 'login'
		let orderAsc = 'desc'

		const paramOrder = orderField
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'company':
				orderBy = 'companyName'
				orderAsc = 'asc'
				break;
			case 'name':
				orderBy = 'surname'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'group':
				orderBy = 'groupName'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;
			case 'institute':
				orderBy = 'institute'
				orderAsc = 'asc'
				break;
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'source':
				orderBy = 'source'
				orderAsc = 'asc'
				break;

			//desc
			case 'companyd':
				orderBy = 'companyName'
				orderAsc = 'desc'
				break;
			case 'named':
				orderBy = 'surname'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'groupd':
				orderBy = 'groupName'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
			case 'instituted':
				orderBy = 'institute'
				orderAsc = 'desc'
				break;
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'sourced':
				orderBy = 'source'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		const orderObject = { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' }

		return { data: orderedData, order: orderObject }
	}

	/*TRADECENTER24*/
	async getIbs({ view, request, params }) {
		const caption = {
			navIbReport: "active",
			icon: 'fas fa-users fa-fw',
			pageTitle: "IB Report",
			route: '/ibs/',
			isPrintable: false,
		}

		const setting = (await BusinessService.GetSettings()).toJSON();

		const institutes = (await Institute.all()).toJSON()

		const selectedPeriod = await this.getPeriodByParams(params);

		let list = await Database.select('person.*')
			.from('person')
			.whereNotIn('person.login', (query) => query.from('bfilogin').select('bfilogin.login'))
			.where(function () {
				this.whereIn('person.financeinstitute', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
				this.andWhere('person.isIb', 1)
			}).orderBy('person.id', 'desc');

		const loginList = [...list.map(x => x.login)]

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting, institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		list.forEach(element => {
			element.created_date = moment(element.created_date).format()
			element.balance = element.balance != null ? element.balance : 0
			element.utilityBill = element.utilityBill ? `${element.id}/${element.utilityBill}` : null
			element.institute = element.financeinstitute ? institutes.find(x => x.id == element.financeinstitute).title : null
			element.amount = entries.data.filter(x => x.clientId == element.login).reduce((a, b) => a + b.amount, 0).toFixed(2)
		});

		for (let c of list) {
			c.groupName = Common.Basic.customerGroupNames.filter(group => group.id == c.groupName).map(group => group.title)[0]
		}

		const orderedData = await this.getOrderedData(list, params.field)

		return view.render("reports.ibs", {
			caption: caption,
			fields: customerFieldList,
			data: orderedData.data,
			setting: setting,
			periods: this.periods,
			period: selectedPeriod.period,
			order: orderedData.order,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
			totalAmount: entries.totalProvision.toFixed(2),
			provisionList: entries.provisionList,
		})
	}

	async getIb({ request, response, view, session, params }) {
		const caption = {
			navIbReport: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "IB Report",
			route: '/ib/',
			isPrintable: false,
		}

		const setting = (await BusinessService.GetSettings()).toJSON();

		const institutes = (await Institute.all()).toJSON();

		const ib = await Customer.findBy('login', params.login);

		const selectedPeriod = await this.getPeriodByParams(params);

		const loginList = [ib.login]

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting, institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);

		const orderedData = await this.getOrderedData(entries.data, params.field)

		for (const item of orderedData.data) {
			item.amount = item.amount.toFixed(2)
		}

		for (const item of entries.provisionList) {
			item.value = item.value.toFixed(2)
		}

		let summary = {
			invoiceType: "IB",
			id: ib.id,
			login: customerList[0].login,
			businessName: '',
			name: ib.surname + ' ' + ib.name,
			addressFirst: (ib.avenue || ''),
			addressSecond: (ib.postcode || '') + ' ' + (ib.city || ''),
			period: selectedPeriod.periodDescription,
			totalAmount: entries.totalProvision.toFixed(2),
			provision: entries.totalProvision.toFixed(2),
			provisionList: entries.provisionList,
			moneyType: "CHF",
			bank: ib.financeinstitute ? institutes.find(x => x.id == ib.financeinstitute).title : '?',
		}

		return view.render("reports.ib", {
			caption: caption,
			fields: entryFieldList,
			setting: setting,
			period: selectedPeriod.period,
			data: orderedData.data,
			order: orderedData.order,
			summary: summary,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async getBfis({ view, request, params }) {
		const caption = {
			navBfiReport: "active",
			icon: 'fas fa-users fa-fw',
			pageTitle: "BFI Report",
			route: '/bfis/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const institutes = (await Institute.all()).toJSON()

		const selectedPeriod = await this.getPeriodByParams(params);

		let list = await Database.select('person.*', 'bfi.companyName', 'bfi.id as bfiId').distinct()
			.from('person')
			.innerJoin('bfilogin', 'person.login', 'bfilogin.login')
			.innerJoin('bfi', 'bfilogin.bfiId', 'bfi.id')
			.where(function () {
				this.whereIn('person.financeinstitute', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
				this.andWhere('person.isIb', 1)
			})
			.orderBy('person.id', 'desc');

		const bfiIdList = list.map(x => x.bfiId)

		const bfiLoginList = await Database.select('bfilogin.*')
			.from('bfilogin')
			.where(function () {
				this.whereIn('bfilogin.bfiId', bfiIdList)
				this.whereIn('bfilogin.instituteId', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
			});

		const loginList = [...bfiLoginList.map(x => x.login)]

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting.toJSON(), institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		list.forEach(element => {
			element.created_date = moment(element.created_date).format()
			element.balance = element.balance != null ? element.balance : 0
			element.utilityBill = element.utilityBill ? `${element.id}/${element.utilityBill}` : null
			element.institute = element.financeinstitute ? institutes.find(x => x.id == element.financeinstitute).title : null
			element.amount = entries.data.filter(x => x.clientId == element.login).reduce((a, b) => a + b.amount, 0).toFixed(2)
		});

		for (let c of list) {
			c.groupName = Common.Basic.customerGroupNames.filter(group => group.id == c.groupName).map(group => group.title)[0]
		}

		const orderedData = await this.getOrderedData(list, params.field)

		return view.render("reports.bfis", {
			caption: caption,
			fields: customerFieldList,
			setting: setting,
			periods: this.periods,
			period: selectedPeriod.period,
			data: orderedData.data,
			order: orderedData.order,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
			totalAmount: entries.totalProvision.toFixed(2),
			provisionList: entries.provisionList,
		})
	}

	async getBfi({ request, response, view, session, params }) {
		const caption = {
			navBfiReport: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "BFI Report",
			route: '/bfi/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const institutes = (await Institute.all()).toJSON();

		const bfiId = parseInt(params.id);

		const bfiLoginList = await Database.select('bfilogin.*')
			.from('bfilogin')
			.where(function () {
				this.where('bfilogin.bfiId', bfiId)
				this.whereIn('bfilogin.instituteId', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
			});
		const loginList = [...bfiLoginList.map(x => x.login)]

		const ibs = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.whereIn('person.login', [...loginList])
			})

		const bfi = await Bfi.findBy('id', bfiId);

		const selectedPeriod = await this.getPeriodByParams(params);

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting.toJSON(), institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);

		const orderedData = await this.getOrderedData(entries.data, params.field)

		for (const item of orderedData.data) {
			item.amount = item.amount.toFixed(2)
		}

		for (const item of entries.provisionList) {
			item.value = item.value.toFixed(2)
		}

		const ib = ibs[0]

		let summary = {
			invoiceType: "BFI",
			ibs: ibs,
			id: ib.id,
			login: loginList.join(', '),
			businessName: bfi.companyName,
			name: ib.surname + ' ' + ib.name,
			companyName: bfi.companyName,
			addressFirst: (bfi.street || ''),
			addressSecond: (bfi.zip || '') + ' ' + (bfi.city || ''),
			period: selectedPeriod.periodDescription,
			totalAmount: entries.totalProvision.toFixed(2),
			provision: entries.totalProvision.toFixed(2),
			provisionList: entries.provisionList,
			moneyType: "CHF",
			bank: ib.financeinstitute ? institutes.find(x => x.id == ib.financeinstitute).title : '?',
		}

		return view.render("reports.bfi", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData.data,
			summary: summary,
			setting: setting,
			period: selectedPeriod.period,
			order: orderedData.order,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async getCcs({ request, response, view, session, params }) {
		const caption = {
			navCcReport: "active",
			icon: 'fas fa-phone fa-fw',
			pageTitle: "CC Report",
			route: '/ccs/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const institutes = (await Institute.all()).toJSON()

		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		switch (paramOrder) {
			case 'name':
				orderBy = 'name'
				orderAsc = 'asc'
				break;
			case 'institute':
				orderBy = 'financeinstitute'
				orderAsc = 'asc'
				break;
			case 'group':
				orderBy = 'groupName'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'source':
				orderBy = 'source'
				orderAsc = 'asc'
				break;

			case 'named':
				orderBy = 'name'
				orderAsc = 'desc'
				break;
			case 'instituted':
				orderBy = 'financeinstitute'
				orderAsc = 'desc'
				break;
			case 'groupd':
				orderBy = 'groupName'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'sourced':
				orderBy = 'source'
				orderAsc = 'desc'
				break;
		}

		let ccs = await Commissioner.query().where('isActive', 1).andWhere('type', 'CC').orderBy('id').fetch()
		ccs = ccs.toJSON()

		const customers = await Database.select('person.*')
			.from('person')
			.whereIn('person.financeinstitute', ccs.map(x => x.personId))
			.orderBy(orderBy, orderAsc);

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		customers.forEach(element => {
			element.created_date = moment(element.created_date).format()
			element.balance = element.balance != null ? element.balance : 0
			element.utilityBill = element.utilityBill ? `${element.id}/${element.utilityBill}` : null
			element.institute = element.financeinstitute ? institutes.find(x => x.id == element.financeinstitute).title : null
		});

		for (let c of customers) {
			c.groupName = Common.Basic.customerGroupNames.filter(group => group.id == c.groupName).map(group => group.title)[0]
			c.sourceName = Common.Basic.customerSourceNames.filter(source => source.id == c.source).map(source => source.title)[0]
		}

		return view.render("reports.ccs", {
			caption: caption,
			fields: ccFieldList,
			customerFields: customerFieldList,
			data: customers,
			banks: ccs,
			setting: setting,
			periods: this.periods,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
		})
	}

	async getCc({ request, response, view, session, params }) {
		const caption = {
			navCcReport: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "CC Report",
			route: '/cc/',
			isPrintable: false,
		}

		const setting = (await BusinessService.GetSettings()).toJSON();

		const institutes = (await Institute.all()).toJSON();

		const selectedPeriod = await this.getPeriodByParams(params);

		//bu cc db de iki satır eklendi iki banka için ancak sanki tek bir satır olup ona bankalar bağlanarak login çoklamak daha doğru gibi
		let ccs = await Commissioner.query().where('isActive', 1).andWhere('type', 'CC').orderBy('id').fetch()
		ccs = ccs.toJSON()

		const loginList = [...ccs.map(x => x.login)]

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting, institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);

		let totalProvision = 0
		let netProvision = 0
		let totalCommissionerProvision = 0
		let data = []

		let commissionerProvisionList = []
		institutes.filter(x => x.moneyTransferActive == 1).forEach(institute => {
			commissionerProvisionList.push({
				id: institute.id,
				title: institute.title,
				value: 0
			})
		})

		for (const entry of entries.data) {
			const netAmount = entry.amount
			let amount = 0
			let commissionRate = 0
			let commission = 0
			let source = ""

			const cc = ccs.find(x => x.personId == entry.instituteId)

			if (entry.agentLogin == cc.login) {//bizim müşteriyse oranlama hesabına gerek yok.
				amount = netAmount
			} else {
				let rateX = 0
				let rateY = 0
				const clientGroup = Common.Basic.customerGroupNames.filter(group => group.id == entry.groupName)
				if (clientGroup.length > 0) {
					rateX = clientGroup[0].commissionRateX
					rateY = clientGroup[0].commissionRateY
				}

				amount = netAmount / rateX * rateY
			}

			if (entry.source == cc.id) {
				source = cc.name
				commissionRate = cc.ownCommissionRate
			} else {
				source = "Web"
				commissionRate = cc.webCommissionRate
			}
			commission = (amount / 300) * commissionRate

			netProvision += netAmount
			totalProvision += amount

			commissionerProvisionList.find(x => x.id == entry.instituteId).value += commission
			totalCommissionerProvision += commission

			data.push({
				id: entry.id,
				type: entry.type,
				date: entry.date,
				comment: entry.comment,
				login: entry.login,
				name: entry.name,
				amount: Number(amount),
				netAmount: Number(netAmount),
				source: source,
				commissionRate: commissionRate,
				commission: Number(commission),
				agentLogin: entry.agentLogin,
				clientId: entry.clientId,
				groupName: entry.groupName,
				institute: entry.institute,
				instituteId: entry.instituteId,
				type: entry.type
			})
		}

		const orderedData = await this.getOrderedData(data, params.field)

		for (const item of orderedData.data) {
			item.amount = item.amount.toFixed(4)
			item.netAmount = item.netAmount.toFixed(4)
			item.commission = item.commission.toFixed(4)
		}

		for (const item of commissionerProvisionList) {
			item.value = item.value.toFixed(2)
		}

		let summary = {
			invoiceType: "CC",
			id: ccs[0].id,
			login: loginList,
			businessName: ccs[0].businessName,
			name: ccs[0].title,
			addressFirst: (ccs[0].addressFirst || ''),
			addressSecond: (ccs[0].addressSecond || ''),
			period: selectedPeriod.periodDescription,
			totalAmount: totalProvision.toFixed(2),
			netAmount: netProvision.toFixed(2),
			provision: totalCommissionerProvision.toFixed(2),
			moneyType: "CHF",
			ownCommissionRate: ccs[0].ownCommissionRate,
			webCommissionRate: ccs[0].webCommissionRate,
			commissionerName: ccs[0].name,
			provisionList: commissionerProvisionList,
		}

		return view.render("reports.cc", {
			caption: caption,
			fields: entryFieldList,
			cc: ccs[0],
			data: orderedData.data,
			order: orderedData.order,
			summary: summary,
			setting: setting,
			period: selectedPeriod.period,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async changeCcSource({ request, response, view, session }) {
		try {
			const ccId = request.all().id
			let record = await Customer.find(ccId)
			record.source = record.source == 1 ? 2 : 1

			await record.save()

			session.flash({ info: "Source changed" })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async getSags({ request, response, view, session }) {
		const caption = {
			navSagReport: "active",
			icon: 'fas fa-building fa-fw',
			pageTitle: "SAG Report",
			route: '/sag/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const institutes = (await Institute.all()).toJSON()

		let sags = await Sag.query().where('isActive', 1).orderBy('id', 'desc').fetch()
		sags = sags.toJSON()
		for (const sag of sags) {
			let bfis = await Database.select('bfi.*')
				.from('bfi')
				.where(function () {
					this.andWhere('bfi.sagId', sag.id)
				}).orderBy('bfi.id', 'desc');

			for (const bfi of bfis) {
				const bfiLoginList = await Database.select('bfilogin.*')
					.from('bfilogin')
					.where(function () {
						this.where('bfilogin.bfiId', bfi.id)
						this.whereIn('bfilogin.instituteId', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
					});
				const loginList = [...bfiLoginList.map(x => x.login)]
				bfi.logins = loginList.join(", ")
			}

			sag.bfis = bfis
		}

		const activeInstitutes = institutes.filter(x => x.moneyTransferActive == 1)

		return view.render("reports.sags", {
			caption: caption,
			fields: ccFieldList,
			customerFields: customerFieldList,
			data: sags,
			setting: setting,
			periods: this.periods,
			institutes: activeInstitutes
		})
	}

	async getSag({ request, response, view, session, params }) {
		const caption = {
			navSagReport: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "SAG Report",
			route: '/sag/',
			isPrintable: false,
		}

		const setting = (await BusinessService.GetSettings()).toJSON();

		const institutes = (await Institute.all()).toJSON();

		const selectedPeriod = await this.getPeriodByParams(params);

		const sag = await Sag.findBy('id', params.id);
		const bfis = await Database.select('bfi.*')
			.from('bfi')
			.where(function () {
				this.andWhere('bfi.sagId', sag.id)
			}).orderBy('bfi.id', 'desc');

		for (const bfi of bfis) {
			const bfiLoginList = await Database.select('bfilogin.*')
				.from('bfilogin')
				.where(function () {
					this.where('bfilogin.bfiId', bfi.id)
					this.whereIn('bfilogin.instituteId', (query) => query.from('financeinstitute').select('financeinstitute.id').where('financeinstitute.moneyTransferActive', 1))
				});
			const loginList = [...bfiLoginList.map(x => x.login)]
			bfi.logins = loginList
		}

		sag.bfis = bfis

		const loginList = [...sag.bfis.map(x => x.logins).flat()]

		const customerList = await this.getCustomerList(loginList)

		const entries = await this.getEntriesFromApi(setting, institutes, customerList, selectedPeriod.rangeStart, selectedPeriod.rangeEnd);


		let totalProvision = 0
		let netProvision = 0
		let totalCommissionerProvision = 0
		let data = []

		let commissionerProvisionList = []
		institutes.filter(x => x.moneyTransferActive == 1).forEach(institute => {
			commissionerProvisionList.push({
				id: institute.id,
				title: institute.title,
				value: 0
			})
		})

		for (const entry of entries.data) {
			let commissionRate = 0
			let agent = ""

			for (const bfi of sag.bfis) {
				if (bfi.logins.find(login => login == entry.agentLogin)) {
					commissionRate = sag.commissionRate
					agent = sag.name
				}
			}

			const netAmount = entry.amount
			const amount = (entry.amount / 200) * 300
			const commission = (amount / 300) * commissionRate

			netProvision += netAmount
			totalProvision += amount

			commissionerProvisionList.find(x => x.id == entry.instituteId).value += commission
			totalCommissionerProvision += commission

			data.push({
				id: entry.id,
				type: entry.type,
				date: entry.date,
				comment: entry.comment,
				login: entry.login,
				name: entry.name,
				amount: Number(amount),
				netAmount: Number(netAmount),
				source: agent,
				commissionRate: commissionRate,
				commission: Number(commission),
				agentLogin: entry.agentLogin,
				clientId: entry.clientId,
				groupName: entry.groupName,
				institute: entry.institute,
				instituteId: entry.instituteId,
				type: entry.type
			})
		}

		const orderedData = await this.getOrderedData(data, params.field)

		for (const item of orderedData.data) {
			item.amount = item.amount.toFixed(4)
			item.netAmount = item.netAmount.toFixed(4)
			item.commission = item.commission.toFixed(4)
		}

		for (const item of commissionerProvisionList) {
			item.value = item.value.toFixed(2)
		}

		let summary = {
			invoiceType: "SAG",
			id: sag.id,
			login: loginList,
			businessName: sag.businessName,
			name: sag.title,
			addressFirst: (sag.addressFirst || ''),
			addressSecond: (sag.addressSecond || ''),
			period: selectedPeriod.periodDescription,
			totalAmount: totalProvision.toFixed(2),
			netAmount: netProvision.toFixed(2),
			provision: totalCommissionerProvision.toFixed(2),
			moneyType: "CHF",
			commissionRate: sag.commissionRate,
			commissionerName: sag.name,
			provisionList: commissionerProvisionList,
		}

		return view.render("reports.sag", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData.data,
			order: orderedData.order,
			summary: summary,
			setting: setting,
			period: selectedPeriod.period,
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async previewPdf({ request, response, view }) {
		const caption = {
			navHome: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "Invoice",
			route: '/previewPdf/',
			isPrintable: true,
		}

		const setting = await BusinessService.GetSettings();
		const invoiceSetting = await InvoiceSetting.findBy('id', 1);

		const period = request.all().period
		let sayiRegex = /^\d+$/;
		let periodYear = moment(new Date()).format('YYYY')
		const last4Char = period.slice(-4)
		if (sayiRegex.test(last4Char)) {
			periodYear = last4Char
		}

		let taxRate = 8
		const invoiceTax = await InvoiceTax.query().where('settingId', 1).andWhere('year', Number(periodYear)).fetch();
		if (invoiceTax.rows.length > 0) {
			taxRate = invoiceTax.rows[0].taxRate;
		}

		const invoiceType = request.all().invoiceType
		const cid = request.all().cid

		const bankAccount = await BankAccount.query().where('cid', cid).andWhere('invoiceType', invoiceType).fetch();
		const ibBankInfo = bankAccount.toJSON()[0];

		let provisionList = []
		const institutes = (await Institute.all()).toJSON()
		institutes.filter(institute => institute.moneyTransferActive == 1).forEach(institute => {
			provisionList.push({
				id: institute.id,
				title: institute.companyName,
				value: request.all()[`provision${institute.id}`]
			})
		})

		let summary = {
			invoiceType: invoiceType,
			cid: cid,
			businessName: request.all().businessName,
			name: request.all().name,
			addressFirst: request.all().addressFirst,
			addressSecond: request.all().addressSecond,
			period: request.all().period,
			date: moment().format('DD.MM.YYYY'),
			provision: request.all().provision,
			provisionList: provisionList,
			login: request.all().login || "",
			moneyType: request.all().moneyType,
			cost1: 0,
			cost2: 0,
			taxRate: (taxRate).toFixed(1),
			taxTotal: 0,
		}

		if (ibBankInfo) {
			summary.bankName = ibBankInfo.bankName || ''
			summary.bankName = ibBankInfo.bankName || ''
			summary.bankAddress = ibBankInfo.bankAddress || ''
			summary.accountName = ibBankInfo.accountName || ''
			summary.iban = ibBankInfo.iban || ''
			summary.bicSwift = ibBankInfo.bicSwift || ''
			summary.provisionAccount = ibBankInfo.provisionAccount || ''
		}

		return view.render("reports.invoicePdf", {
			caption: caption,
			summary: summary,
			setting: setting,
			invoiceSetting: invoiceSetting,
		})
	}


	/*FOREX724*/
	async saveForexData({ request, response, session, params }) {
		try {
			const reportType = request.all().reportType
			const period = request.all().period

			for (let record of state.forexData) {
				let item = await ForexReport.findBy('transactionId', record.id)
				const mysqlDate = moment(record.date, 'DD.MM.YYYY HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

				if (item == null) {
					await ForexReport.create({
						reportType: reportType,
						period: period,
						transactionId: record.id,
						type: record.type,
						date: mysqlDate,
						comment: record.comment,
						login: Number(record.login),
						name: record.name,
						amount: Number(record.amount) || 0,
						commission: Number(record.commission) || 0,
						commissionRate: Number(record.commissionRate) || 0,
						isFrozen: 0,
					})
				}
				else {
					item.reportType = reportType
					item.period = period
					item.type = record.type
					item.date = mysqlDate
					item.comment = record.comment
					item.login = Number(record.login)
					item.name = record.name
					item.amount = Number(record.amount) || 0
					item.commission = Number(record.commission) || 0
					item.commissionRate = Number(record.commissionRate) || 0
					item.isFrozen = 0

					await record.save()
				}
			}

			session.flash({ info: "Report Data Saved" })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async getForexIbs({ view, request }) {
		const caption = {
			navForexIbReport: "active",
			navBankReport: "active",
			icon: 'fas fa-users fa-fw',
			pageTitle: "Commissions Report",
			route: '/forexIbs/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		let list = await Database.select('person.*', 'cc.*')
			.from('person')
			.leftJoin('cc', 'cc.personId', 'person.id')
			.where(function () {
				this.andWhere('cc.isActive', 1)
				this.andWhere('cc.type', 'IB')
			}).orderBy('person.id');

		return view.render("reports.forexIbs", {
			caption: caption,
			fields: customerFieldList,
			data: list,
			setting: setting,
			periods: this.periods
		})
	}

	async getForexIb({ request, response, view, session, params }) {
		const caption = {
			navForexIbReport: "active",
			navBankReport: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "Commissions Report",
			route: '/forexIb/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const ib = await Customer.findBy('login', params.login);
		const commissionRate = ib.commissionRate; //200
		const commissionFullRate = ib.commissionFullRate; //300

		const ccQuery = await Commissioner.query().where('personId', ib.id).andWhere('type', 'IB').fetch()
		const cc = ccQuery.toJSON()[0];

		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.andWhere('person.financeinstitute', ib.financeinstitute)
			}).orderBy('person.id', 'desc');

		state.customerLoginList = customers.map(x => Number(x.login))

		let data = []

		let totalProvision = 0

		let status = 0
		let saveAble = false
		let fromApi = true

		const login = params.login

		const periodId = Number(params.periodId)
		const period = this.periods.find(x => x.id == periodId)
		let periodDescription = period.en
		if (periodId == 0) {
			const begin = moment.unix(params.begin / 1000).format('DD.MM.YYYY');
			const end = moment.unix(params.end / 1000).format('DD.MM.YYYY');

			periodDescription = begin + " - " + end
		} else if (periodId >= 7 && periodId <= 31) { //Ocak 2024 gibi olanlar (Aylık Periyotlar)
			saveAble = true

			//DB de bu periyoda ait kayıt var mı?
			const forexRawData = await ForexReport
				.query()
				.where('period', period.title)
				.andWhere('reportType', 'ForexIB')
				.fetch()

			let forexRecords = forexRawData.toJSON()
			if (forexRecords.length > 0) {
				fromApi = false
				status = forexRecords[0].isFrozen == 2 ? 2 : 1

				for (let record of forexRecords) {
					data.push({
						id: record.id,
						period: periodDescription,
						transactionId: record.transactionId,
						type: record.type,
						date: record.date,
						comment: record.comment,
						login: record.login,
						name: record.name,
						amount: record.amount,
						commission: record.commission,
						commissionRate: record.commissionRate,
						isFrozen: record.isFrozen,
					})
				}
			} else {
				fromApi = true
			}
		} else {
			saveAble = false

			const rangeStart = moment.unix(Number(params.begin) / 1000).add(-3, 'hour').format('YYYY-MM-DD HH:mm:ss')
			const rangeEnd = moment.unix(Number(params.end) / 1000).add(20, 'hour').add(59, 'minute').add(59, 'second').format('YYYY-MM-DD HH:mm:ss')

			//DB de bu periyoda ait kayıt var mı?
			const forexRawData = await ForexReport
				.query()
				.whereBetween('date', [rangeStart, rangeEnd])
				.andWhere('reportType', 'ForexIB')
				.fetch()

			let forexRecords = forexRawData.toJSON()
			if (forexRecords.length > 0) {
				fromApi = false
				status = forexRecords[0].isFrozen == 2 ? 2 : 1

				for (let record of forexRecords) {
					data.push({
						id: record.id,
						period: periodDescription,
						transactionId: record.transactionId,
						type: record.type,
						date: record.date,
						comment: record.comment,
						login: record.login,
						name: record.name,
						amount: record.amount,
						commission: record.commission,
						commissionRate: record.commissionRate,
						isFrozen: record.isFrozen,
					})
				}
			} else {
				fromApi = true
			}
		}

		let rawTotal = 0
		let ownTotal = 0
		let otherTotal = 0
		let otherCommission = 0

		if (fromApi) {
			//Trader WEB API
			const managerId = setting.restApiUser;
			const password = setting.restApiPassword;

			const resAuth = await axios.post(`${setting.restApiBaseUrl}/v1/register/register`,
				{
					managerID: managerId,
					password: password
				}
			)

			const ledgerTypes = [4]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

			//const beginDate = new Date(period.beginDate) //new Date(2024, 2, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye
			//const endDate = new Date(period.endDate) //new Date(2024, 3, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye

			//const rangeStart1 = Math.floor(beginDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)
			//const rangeEnd1 = Math.floor(endDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)

			const rangeStart = Math.floor(params.begin) - beginTimeDifference
			const rangeEnd = Math.floor(params.end) + endTimeDifference

			state.rangeStart = rangeStart
			state.rangeEnd = rangeEnd

			const resGetEntries = await axios.post(`${setting.restApiBaseUrl}/v1/ledger/getEntries`,
				{
					auth: {
						managerID: managerId,
						token: resAuth.data.token
					},
					rangeStart: rangeStart,
					rangeEnd: rangeEnd,
					clientIds: [Number(login)],
					ledgerTypes: [...ledgerTypes] //ledgerType
				}
			)

			const entries = resGetEntries.data.ledgerEntry
			for (const item of entries.filter(x => x.additionalType == "8")) {
				rawTotal = rawTotal + Number(item.amount / 100)

				let ledgerName = ""

				const ledgerLogin = item.comment.substring(item.comment.indexOf('comm:') + 5, 200).split('-')[0]

				const customer = customers.find(x => x.login == ledgerLogin)
				if (customer) {
					ledgerName = customer.name + ' ' + customer.surname
				}

				let amount = 0
				amount = Number(item.amount / 100)
				if (customer.agentLogin == login) {//bizim müşteriyse oranlama hesabına gerek yok.
					ownTotal = ownTotal + amount
				} else {
					otherTotal = otherTotal + amount

					let rateX = 0
					let rateY = 0
					const clientGroup = Common.Basic.customerGroupNames.filter(group => group.id == customer.groupName)
					if (clientGroup.length > 0) {
						rateX = clientGroup[0].commissionRateX
						rateY = clientGroup[0].commissionRateY
					}

					amount = amount / rateX * rateY    //  / commissionRate * commissionFullRate
					otherCommission = otherCommission + amount
				}

				data.push({
					id: item.uid,
					type: item.additionalType,
					date: moment.unix(Number(item.generatedTime) / 1000).format('YYYY-MM-DD HH:mm:ss'),
					comment: item.comment,
					login: ledgerLogin,
					name: ledgerName,
					amount: Number(amount),
				})
			}
		}

		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;

			//desc
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		state.forexData = orderedData

		for (const item of orderedData) {
			//veri manipülasyonu
			item.transactionId = Number(item.transactionId)
			item.type = Number(item.type)
			item.amount = Number(item.amount)

			//genel toplamlar
			totalProvision += item.amount

			//gösterim için
			item.date = moment(item.date).format('DD.MM.YYYY HH:mm:ss')
			item.amount = item.amount.toFixed(2)
			item.isFrozen = item.isFrozen == 0 ? 'NO' : 'YES'
		}

		let summary = {
			invoiceType: "ForexIB",
			id: ib.id,
			login: login,
			ccid: cc.id,
			name: cc.title,
			businessName: cc.businessName,
			recipientTitle: cc.recipientTitle,
			recipientName: cc.recipientName,
			addressFirst: (cc.addressFirst || ''),
			addressSecond: (cc.addressSecond || ''),
			addressThird: (cc.addressThird || ''),
			period: periodDescription,
			totalAmount: totalProvision.toFixed(2),
			provision: totalProvision.toFixed(2),
			moneyType: "CHF",
			commissionRate: ib.commissionRate,
			commissionFullRate: ib.commissionFullRate,
			rawTotal: rawTotal.toFixed(2),
			ownTotal: ownTotal.toFixed(2),
			otherTotal: otherTotal.toFixed(2),
			otherCommission: otherCommission.toFixed(2),
		}

		return view.render("reports.forexIb", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData,
			summary: summary,
			setting: setting,
			period: period,
			status: status,
			saveAble: saveAble,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url()
		})
	}

	async getForexContests({ view, request }) {
		const caption = {
			navForexContestReport: "active",
			navBankReport: "active",
			icon: 'fas fa-crown fa-fw',
			pageTitle: "Contest Report",
			route: '/forexContests/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		let list = await Database.select('person.*', 'cc.*')
			.from('person')
			.leftJoin('cc', 'cc.personId', 'person.id')
			.where(function () {
				this.andWhere('cc.type', 'IB')
			}).orderBy('person.id');

		return view.render("reports.forexContests", {
			caption: caption,
			fields: customerFieldList,
			data: list,
			setting: setting,
			periods: this.periods
		})
	}

	async getForexContest({ request, response, view, session, params }) {
		const caption = {
			navForexContestReport: "active",
			navBankReport: "active",
			icon: 'fas fa-crown fa-fw',
			pageTitle: "Contest Report",
			route: '/forexContest/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const ib = await Customer.findBy('login', params.login);
		const commissionRate = ib.commissionRate; //200
		const commissionFullRate = ib.commissionFullRate; //300

		const ccQuery = await Commissioner.query().where('personId', ib.id).andWhere('type', 'IB').fetch()
		const cc = ccQuery.toJSON()[0];

		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.andWhere('person.financeinstitute', ib.financeinstitute)
			}).orderBy('person.id', 'desc');

		const period = this.periods.find(x => x.id == params.periodId)
		let periodDescription = period.title
		if (params.periodId == 0) {
			const begin = moment.unix(params.begin / 1000).format('DD.MM.YYYY');
			const end = moment.unix(params.end / 1000).format('DD.MM.YYYY');

			periodDescription = begin + " - " + end
		}

		//Trader WEB API
		const managerId = setting.restApiUser;
		const password = setting.restApiPassword;

		const resAuth = await axios.post(`${setting.restApiBaseUrl}/v1/register/register`,
			{
				managerID: managerId,
				password: password
			}
		)

		const allCustomersLogins = customers.map(x => Number(x.login))
		const login = params.login
		const ledgerTypes = [4]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

		//const beginDate = new Date(period.beginDate) //new Date(2024, 2, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye
		//const endDate = new Date(period.endDate) //new Date(2024, 3, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye

		//const rangeStart1 = Math.floor(beginDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)
		//const rangeEnd1 = Math.floor(endDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)

		const rangeStart = Math.floor(params.begin) - beginTimeDifference;
		const rangeEnd = Math.floor(params.end) + endTimeDifference;

		const resGetEntries = await axios.post(`${setting.restApiBaseUrl}/v1/ledger/getEntries`,
			{
				auth: {
					managerID: managerId,
					token: resAuth.data.token
				},
				rangeStart: rangeStart,
				rangeEnd: rangeEnd,
				clientIds: [...allCustomersLogins],
				ledgerTypes: [...ledgerTypes] //ledgerType
			}
		)

		let totalProvision = 0
		let data = []

		let contestData = resGetEntries.data.ledgerEntry

		let filteredData = contestData.filter(x => x.comment.includes("Contest:"))

		const entries = filteredData
		for (const item of entries) {
			let ledgerName = ""

			const ledgerLogin = Number(item.clientId)

			const customer = customers.find(x => x.login == ledgerLogin)
			if (customer) {
				ledgerName = customer.name + ' ' + customer.surname
			}

			const amount = Number(item.amount / 100)

			totalProvision += amount

			data.push({
				id: item.uid,
				type: item.additionalType,
				date: moment.unix(Number(item.generatedTime) / 1000).format('DD.MM.YYYY HH:mm:ss'),
				comment: item.comment,
				login: ledgerLogin,
				name: ledgerName,
				amount: Number(amount),
			})
		}

		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;

			//desc
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		for (const item of orderedData) {
			item.amount = item.amount.toFixed(2)
		}

		let summary = {
			invoiceType: "ForexContest",
			id: ib.id,
			login: login,
			businessName: cc.businessName,
			name: cc.title,
			addressFirst: (cc.addressFirst || ''),
			addressSecond: (cc.addressSecond || ''),
			addressThird: (cc.addressThird || ''),
			period: periodDescription,
			totalAmount: totalProvision.toFixed(2),
			provision: totalProvision.toFixed(2),
			moneyType: "CHF",
			commissionRate: ib.commissionRate,
			commissionFullRate: ib.commissionFullRate,
		}

		return view.render("reports.forexContest", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData,
			summary: summary,
			setting: setting,
			period: period,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async getForexBulls({ view, request }) {
		const caption = {
			navForexBullReport: "active",
			navBankReport: "active",
			icon: 'fas fa-star-half-alt fa-fw',
			pageTitle: "Bulls Report",
			route: '/forexBulls/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		let list = await Database.select('person.*', 'cc.*')
			.from('person')
			.leftJoin('cc', 'cc.personId', 'person.id')
			.where(function () {
				this.andWhere('cc.type', 'IB')
			}).orderBy('person.id');

		return view.render("reports.forexBulls", {
			caption: caption,
			fields: customerFieldList,
			data: list,
			setting: setting,
			periods: this.periods
		})
	}

	async getForexBull({ request, response, view, session, params }) {
		const caption = {
			navForexBullReport: "active",
			navBankReport: "active",
			icon: 'fas fa-star-half-alt fa-fw',
			pageTitle: "Bull Report",
			route: '/forexBull/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const ib = await Customer.findBy('login', params.login);
		const commissionRate = ib.commissionRate; //200
		const commissionFullRate = ib.commissionFullRate; //300

		const ccQuery = await Commissioner.query().where('personId', ib.id).andWhere('type', 'IB').fetch()
		const cc = ccQuery.toJSON()[0];

		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.andWhere('person.financeinstitute', ib.financeinstitute)
			}).orderBy('person.id', 'desc');

		const period = this.periods.find(x => x.id == params.periodId)
		let periodDescription = period.title
		if (params.periodId == 0) {
			const begin = moment.unix(params.begin / 1000).format('DD.MM.YYYY');
			const end = moment.unix(params.end / 1000).format('DD.MM.YYYY');

			periodDescription = begin + " - " + end
		}

		//Trader WEB API
		const managerId = setting.restApiUser;
		const password = setting.restApiPassword;

		const resAuth = await axios.post(`${setting.restApiBaseUrl}/v1/register/register`,
			{
				managerID: managerId,
				password: password
			}
		)

		const allCustomersLogins = customers.map(x => Number(x.login))
		const login = params.login
		const ledgerTypes = [4]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

		//const beginDate = new Date(period.beginDate) //new Date(2024, 2, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye
		//const endDate = new Date(period.endDate) //new Date(2024, 3, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye

		//const rangeStart1 = Math.floor(beginDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)
		//const rangeEnd1 = Math.floor(endDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)

		const rangeStart = Math.floor(params.begin) - beginTimeDifference;
		const rangeEnd = Math.floor(params.end) + endTimeDifference;

		const resGetEntries = await axios.post(`${setting.restApiBaseUrl}/v1/ledger/getEntries`,
			{
				auth: {
					managerID: managerId,
					token: resAuth.data.token
				},
				rangeStart: rangeStart,
				rangeEnd: rangeEnd,
				clientIds: [...allCustomersLogins],
				ledgerTypes: [...ledgerTypes] //ledgerType
			}
		)

		let totalProvision = 0
		let data = []

		let contestData = resGetEntries.data.ledgerEntry

		let filteredData = contestData.filter(x => x.comment.includes("Invitation for ID:")) // Schedule.js->invitationCheck methodunda 'Invitation for ID: ' yazılmış

		const entries = filteredData
		for (const item of entries) {
			let ledgerName = ""
			const ledgerLogin = Number(item.clientId)
			const customer = customers.find(x => x.login == ledgerLogin)
			if (customer) {
				ledgerName = customer.name + ' ' + customer.surname
			}

			const amount = Number(item.amount / 100)
			totalProvision += amount

			let inviteName = ""
			const inviteLogin = item.comment.substring(item.comment.indexOf('Invitation for ID:') + 19, 200).replace("'", "")
			const inviteCustomer = customers.find(x => x.login == inviteLogin)
			if (inviteCustomer) {
				inviteName = customer.name + ' ' + customer.surname
			}

			data.push({
				id: item.uid,
				type: item.additionalType,
				date: moment.unix(Number(item.generatedTime) / 1000).format('DD.MM.YYYY HH:mm:ss'),
				comment: item.comment,
				login: ledgerLogin,
				name: ledgerName,
				amount: Number(amount),
				inviteLogin: inviteLogin,
				inviteName: inviteName,
			})
		}

		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;

			//desc
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		for (const item of orderedData) {
			item.amount = item.amount.toFixed(2)
		}

		let summary = {
			invoiceType: "ForexBull",
			id: ib.id,
			login: login,
			businessName: cc.businessName,
			name: cc.title,
			addressFirst: (cc.addressFirst || ''),
			addressSecond: (cc.addressSecond || ''),
			addressThird: (cc.addressThird || ''),
			period: periodDescription,
			totalAmount: totalProvision.toFixed(2),
			provision: totalProvision.toFixed(2),
			moneyType: "CHF",
			commissionRate: ib.commissionRate,
			commissionFullRate: ib.commissionFullRate,

		}

		return view.render("reports.forexBull", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData,
			summary: summary,
			setting: setting,
			period: period,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async getForexQuizes({ view, request }) {
		const caption = {
			navForexQuizReport: "active",
			navBankReport: "active",
			icon: 'fas fa-question-circle fa-fw',
			pageTitle: "Quiz Report",
			route: '/forexQuizes/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		let list = await Database.select('person.*', 'cc.*')
			.from('person')
			.leftJoin('cc', 'cc.personId', 'person.id')
			.where(function () {
				this.andWhere('cc.type', 'IB')
			}).orderBy('person.id');

		return view.render("reports.forexQuizes", {
			caption: caption,
			fields: customerFieldList,
			data: list,
			setting: setting,
			periods: this.periods
		})
	}

	async getForexQuiz({ request, response, view, session, params }) {
		const caption = {
			navForexQuizReport: "active",
			navBankReport: "active",
			icon: 'fas fa-question-circle fa-fw',
			pageTitle: "Quiz Report",
			route: '/forexQuiz/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		const ib = await Customer.findBy('login', params.login);
		const commissionRate = ib.commissionRate; //200
		const commissionFullRate = ib.commissionFullRate; //300

		const ccQuery = await Commissioner.query().where('personId', ib.id).andWhere('type', 'IB').fetch()
		const cc = ccQuery.toJSON()[0];

		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.andWhere('person.financeinstitute', ib.financeinstitute)
			}).orderBy('person.id', 'desc');

		const period = this.periods.find(x => x.id == params.periodId)
		let periodDescription = period.title
		if (params.periodId == 0) {
			const begin = moment.unix(params.begin / 1000).format('DD.MM.YYYY');
			const end = moment.unix(params.end / 1000).format('DD.MM.YYYY');

			periodDescription = begin + " - " + end
		}

		//Trader WEB API
		const managerId = setting.restApiUser;
		const password = setting.restApiPassword;

		const resAuth = await axios.post(`${setting.restApiBaseUrl}/v1/register/register`,
			{
				managerID: managerId,
				password: password
			}
		)

		const allCustomersLogins = customers.map(x => Number(x.login))
		const login = params.login
		const ledgerTypes = [6]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

		//const beginDate = new Date(period.beginDate) //new Date(2024, 2, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye
		//const endDate = new Date(period.endDate) //new Date(2024, 3, 1, 0, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye

		//const rangeStart1 = Math.floor(beginDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)
		//const rangeEnd1 = Math.floor(endDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)

		const rangeStart = Math.floor(params.begin) - beginTimeDifference;
		const rangeEnd = Math.floor(params.end) + endTimeDifference;

		const resGetEntries = await axios.post(`${setting.restApiBaseUrl}/v1/ledger/getEntries`,
			{
				auth: {
					managerID: managerId,
					token: resAuth.data.token
				},
				rangeStart: rangeStart,
				rangeEnd: rangeEnd,
				clientIds: [...allCustomersLogins],
				ledgerTypes: [...ledgerTypes] //ledgerType
			}
		)

		let totalProvision = 0
		let data = []

		let quizData = resGetEntries.data.ledgerEntry

		let filteredData = quizData.filter(x => x.comment.includes("Winner Credit for ID:"))

		const entries = filteredData
		for (const item of entries) {
			let ledgerName = ""

			const ledgerLogin = Number(item.clientId)

			const customer = customers.find(x => x.login == ledgerLogin)
			if (customer) {
				ledgerName = customer.name + ' ' + customer.surname
			}

			const amount = Number(item.amount / 100)

			totalProvision += amount

			data.push({
				id: item.uid,
				type: item.additionalType,
				date: moment.unix(Number(item.generatedTime) / 1000).format('DD.MM.YYYY HH:mm:ss'),
				comment: item.comment,
				login: ledgerLogin,
				name: ledgerName,
				amount: Number(amount),
			})
		}

		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;

			//desc
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		for (const item of orderedData) {
			item.amount = item.amount.toFixed(2)
		}

		let summary = {
			invoiceType: "ForexQuiz",
			id: ib.id,
			login: login,
			businessName: cc.businessName,
			name: cc.title,
			addressFirst: (cc.addressFirst || ''),
			addressSecond: (cc.addressSecond || ''),
			addressThird: (cc.addressThird || ''),
			period: periodDescription,
			totalAmount: totalProvision.toFixed(2),
			provision: totalProvision.toFixed(2),
			moneyType: "CHF",
			commissionRate: ib.commissionRate,
			commissionFullRate: ib.commissionFullRate,

		}

		return view.render("reports.forexQuiz", {
			caption: caption,
			fields: entryFieldList,
			data: orderedData,
			summary: summary,
			setting: setting,
			period: period,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
		})
	}

	async calculateForexInvoice(customerLogins, rangeStart, rangeEnd) {
		const setting = await BusinessService.GetSettings();

		//Trader WEB API
		const managerId = setting.restApiUser;
		const password = setting.restApiPassword;

		const resAuth = await axios.post(`${setting.restApiBaseUrl}/v1/register/register`,
			{
				managerID: managerId,
				password: password
			}
		)

		const ledgerTypes = [4, 6]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8
		const resGetEntries = await axios.post(`${setting.restApiBaseUrl}/v1/ledger/getEntries`,
			{
				auth: {
					managerID: managerId,
					token: resAuth.data.token
				},
				rangeStart: rangeStart,
				rangeEnd: rangeEnd,
				clientIds: [...customerLogins],
				ledgerTypes: [...ledgerTypes] //ledgerType
			}
		)

		const rawData = resGetEntries.data.ledgerEntry

		let totalContest = 0
		const contestData = rawData.filter(x => x.entryType == 4 && x.comment.includes("Contest:"))
		for (const item of contestData) {
			const amount = Number(item.amount / 100)
			totalContest += amount
		}

		let totalBulls = 0
		const bullsData = rawData.filter(x => x.entryType == 4 && x.comment.includes("Invitation for ID:")) // Schedule.js->invitationCheck methodunda 'Invitation for ID: ' yazılmış
		for (const item of bullsData) {
			const amount = Number(item.amount / 100)
			totalBulls += amount
		}

		let totalQuiz = 0
		const quizData = rawData.filter(x => x.entryType == 6 && x.comment.includes("Winner Credit for ID:"))
		for (const item of quizData) {
			const amount = Number(item.amount / 100)
			totalQuiz += amount
		}

		return { contest: totalContest, bulls: totalBulls, quiz: totalQuiz, total: totalContest + totalBulls + totalQuiz }
	}

	async previewForexPdf({ request, response, view, params }) {
		const caption = {
			navHome: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "Forex Invoice",
			route: '/previewForexPdf/',
			isPrintable: true,
		}

		const setting = await BusinessService.GetSettings();

		let taxRate = 8
		const period = request.all().period
		let sayiRegex = /^\d+$/;
		let periodYear = moment(new Date()).format('YYYY')
		const last4Char = period.slice(-4)
		if (sayiRegex.test(last4Char)) {
			periodYear = last4Char
		}

		const invoiceTax = await InvoiceTax.query().where('settingId', 2).andWhere('year', Number(periodYear)).fetch();
		if (invoiceTax) taxRate = invoiceTax.toJSON()[0].taxRate;

		const invoiceType = request.all().invoiceType
		const cid = request.all().cid
		const ccid = request.all().ccid

		const invoiceSettingRaw = await InvoiceSetting.query().where('cid', ccid).andWhere('keyword', "bankHead").fetch();
		const invoiceSetting = invoiceSettingRaw.toJSON()[0];

		const bankAccount = await BankAccount.query().where('cid', cid).andWhere('invoiceType', invoiceType).fetch();
		const ibBankInfo = bankAccount.toJSON()[0];

		const forexInvoice = await this.calculateForexInvoice(state.customerLoginList, state.rangeStart, state.rangeEnd);

		const provision = parseFloat(request.all().provision)
		const contest = parseFloat(forexInvoice.contest)
		const bulls = parseFloat(forexInvoice.bulls)
		const quiz = parseFloat(forexInvoice.quiz)
		const taxTotal = parseFloat(0)

		let summary = {
			invoiceType: invoiceType,
			cid: cid,
			businessName: request.all().businessName,
			name: request.all().name,
			recipientTitle: request.all().recipientTitle,
			recipientName: request.all().recipientName,
			addressFirst: request.all().addressFirst,
			addressSecond: request.all().addressSecond,
			addressThird: request.all().addressThird,
			period: request.all().period,
			date: moment().format('DD.MM.YYYY'),
			moneyType: request.all().moneyType,
			provision: provision.toFixed(2),
			contest: (-contest).toFixed(2),
			bulls: (-bulls).toFixed(2),
			quiz: (-quiz).toFixed(2),
			taxRate: (taxRate).toFixed(1),
			taxTotal: (taxTotal).toFixed(2),
			subTotal: (provision - forexInvoice.total + taxTotal).toFixed(2),
		}

		if (ibBankInfo) {
			summary.bankName = ibBankInfo.bankName || ''
			summary.bankAddress = ibBankInfo.bankAddress || ''
			summary.accountName = ibBankInfo.accountName || ''
			summary.iban = ibBankInfo.iban || ''
			summary.bicSwift = ibBankInfo.bicSwift || ''
			summary.provisionAccount = ibBankInfo.provisionAccount || ''
		}

		return view.render("reports.invoiceForexPdf", {
			caption: caption,
			summary: summary,
			setting: setting,
			invoiceSetting: invoiceSetting,
		})
	}

	async getForexMtcs({ view, request }) {
		const caption = {
			navForexMtcReport: "active",
			icon: 'fas fa-sun fa-fw',
			pageTitle: "Banks",
			route: '/forexMtcs/',
			isPrintable: false,
		}

		const setting = await BusinessService.GetSettings();

		let list = await Database.select('cc.*')
			.from('cc')
			.innerJoin('financeinstitute', 'financeinstitute.id', 'cc.personId')
			.where('cc.type', 'Bank')
			.orderBy('cc.id');

		return view.render("pages.forexMtcs", {
			caption: caption,
			fields: customerFieldList,
			data: list,
			setting: setting,
			periods: this.periods,
		})
	}

	async getForexMtc({ request, response, view, session, params }) {
		const caption = {
			navForexMtcReport: "active",
			icon: 'fas fa-sun fa-fw',
			pageTitle: "Bank-MTC Report",
			route: '/forexMtc/',
			isPrintable: false,

			newAction: 'createForexMtc',
			newData: "New Entry",
			editData: "Edit Data",
			deleteData: "Delete Data",
			deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
			close: "Close",
			new: "New",
			save: "Save",
			add: "Add",
			edit: "Edit",
			delete: "Delete",
			active: "Active",
			passive: "Passive",
			savedMessage: "It was saved successfuly",
			updatedMessage: "It was updated successfuly",
			deletedMessage: "It was deleted successfuly",
			selectFile: "Select File",
		}

		const setting = await BusinessService.GetSettings();

		const instituteId = Number(params.instituteId)
		const institute = (await Institute.findBy('id', instituteId)).toJSON();

		const ccQuery = await Commissioner.query().where('personId', institute.id).andWhere('type', 'Bank').fetch()
		const cc = ccQuery.toJSON()[0];

		const customers = await Database.select('person.*')
			.from('person')
			.where(function () {
				this.andWhere('person.financeinstitute', institute.id)
			}).orderBy('person.id', 'desc');

		state.customerLoginList = customers.map(x => Number(x.login))
		state.customerLoginList.pop(Number(institute.caseLoginId)) //kendi hesabını çıkarıyoruz

		const bankMtcCommissionRate = cc.mtcRate
		const cryptoCommissionRate = cc.cryptoRate
		let invoiceNumber = moment().format('YYYYMMDDHHmmss')

		let data = []

		let status = 0
		let saveAble = false
		let fromApi = true

		let totalProvision = 0
		let totalCommission = 0
		let totalBankDeposit = 0
		let totalMtcDeposit = 0
		let totalCryptoDeposit = 0
		let totalDeposit = 0
		let totalBankWithdraw = 0
		let totalMtcWithdraw = 0
		let totalCryptoWithdraw = 0
		let totalWithdraw = 0

		const periodId = Number(params.periodId)
		const period = this.periods.find(x => x.id == periodId)
		let periodDescription = period.title
		if (periodId == 0) {
			const begin = moment.unix(params.begin / 1000).format('DD.MM.YYYY');
			const end = moment.unix(params.end / 1000).format('DD.MM.YYYY');

			periodDescription = begin + " - " + end
		} else if (periodId >= 7 && periodId <= 31) { //Ocak 2024 gibi olanlar (Aylık Periyotlar)
			saveAble = true

			//DB de bu periyoda ve bankaya ait kayıt var mı?
			const mtcRawData = await MtcReport
				.query()
				.where('period', period.title)
				.andWhere('instituteId', institute.id)
				.fetch()

			if (mtcRawData.rows.length > 0) {
				let mtcRecords = mtcRawData.toJSON()
				fromApi = false
				invoiceNumber = mtcRecords[0].invoiceNumber
				status = mtcRecords[0].isFrozen == 2 ? 2 : 1

				for (let record of mtcRecords) {
					data.push({
						id: record.id,
						invoiceNumber,
						period: periodDescription,
						transactionId: record.transactionId,
						type: record.type,
						date: record.date,
						comment: record.comment,
						login: record.login,
						name: record.name,
						amount: record.amount,
						typeName: undefined,
						gateway: record.gateway,
						commission: record.commission,
						commissionRate: record.commissionRate,
						isFrozen: record.isFrozen,
					})
				}
			} else {
				fromApi = true
			}
		} else {
			saveAble = false

			const rangeStart = moment.unix(Number(params.begin) / 1000).add(-3, 'hour').format('YYYY-MM-DD HH:mm:ss')
			const rangeEnd = moment.unix(Number(params.end) / 1000).add(20, 'hour').add(59, 'minute').add(59, 'second').format('YYYY-MM-DD HH:mm:ss')

			//DB de bu periyoda ait kayıt var mı?
			const mtcRawData = await MtcReport
				.query()
				.whereBetween('date', [rangeStart, rangeEnd])
				.andWhere('instituteId', institute.id)
				.fetch()

			let mtcRecords = mtcRawData.toJSON()
			if (mtcRecords.length > 0) {
				fromApi = false
				invoiceNumber = mtcRecords[0].invoiceNumber
				status = mtcRecords[0].isFrozen == 2 ? 2 : 1

				for (let record of mtcRecords) {
					data.push({
						id: record.id,
						invoiceNumber,
						period: periodDescription,
						transactionId: record.transactionId,
						type: record.type,
						date: record.date,
						comment: record.comment,
						login: record.login,
						name: record.name,
						amount: record.amount,
						typeName: undefined,
						gateway: record.gateway,
						commission: record.commission,
						commissionRate: record.commissionRate,
						isFrozen: record.isFrozen,
					})
				}
			} else {
				fromApi = true
			}
		}

		if (fromApi) {
			//Trader WEB API
			const managerId = institute.restApiUser;
			const password = institute.restApiPassword;

			const resAuth = await axios.post(`${institute.restApiBaseUrl}/v1/register/register`,
				{
					managerID: managerId,
					password: password
				}
			)

			const ledgerTypes = [4, 5]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8

			const rangeStart = Math.floor(params.begin) - beginTimeDifference
			const rangeEnd = Math.floor(params.end) + endTimeDifference

			state.rangeStart = rangeStart
			state.rangeEnd = rangeEnd

			const resGetEntries = await axios.post(`${institute.restApiBaseUrl}/v1/ledger/getEntries`,
				{
					auth: {
						managerID: managerId,
						token: resAuth.data.token
					},
					rangeStart: rangeStart,
					rangeEnd: rangeEnd,
					clientIds: [...state.customerLoginList],
					ledgerTypes: [...ledgerTypes] //ledgerType
				}
			)

			const rawData = resGetEntries.data.ledgerEntry

			const entries = rawData.filter(x =>
				!(x.comment.includes("Contest:"))
				&& !(x.comment.includes("Invitation for ID:"))
				&& !(x.comment.includes("comm:"))
				&& !(x.comment.includes("comment='Commission"))
				&& !(x.comment.includes("comment='Contest Correction"))
				&& !(x.comment.includes("comment='Correction of wrong commission payment"))
			)

			let id = 0
			for (const item of entries) {
				let ledgerName = ""

				const ledgerLogin = Number(item.clientId)
				if (ledgerLogin == institute.withdrawLoginId
					|| ledgerLogin == institute.caseLoginId
					|| ledgerLogin == 44308
					|| ledgerLogin == 48100) {
					ledgerName = "TradeCenter"
					continue
				}

				const customer = customers.find(x => x.login == ledgerLogin)
				if (customer) {
					ledgerName = customer.name + ' ' + customer.surname
				}

				const amount = Number(item.amount / 100)

				let foundGateway = ""
				if (item.comment.includes("Crypto")) {
					foundGateway = "Crypto"
				} else if (item.comment.includes("Withdraw[")
					|| item.comment.includes("Deposit[")
					|| item.comment.includes("Transfer from")
				) {
					foundGateway = "Bank"
				} else if (item.comment.includes("deposit explorer")
					|| item.comment.includes("withdraw explorer")
					|| item.comment.includes("deposit")
					|| item.comment.includes("comment='deposit")
					|| item.comment.includes("comment='withdraw")
					|| item.comment.includes("MTC")
				) {
					foundGateway = "MTC"
				} else {
					foundGateway = "UNKNOWN"
				}

				let commission = 0
				let commissionRate = 0
				if (item.entryType == 4 && (foundGateway == "MTC" || foundGateway == "Bank")) {
					commissionRate = bankMtcCommissionRate
					commission = amount * commissionRate / 100
				} else if (item.entryType == 4 && foundGateway == "Crypto") {
					commissionRate = cryptoCommissionRate
					commission = amount * commissionRate / 100
				}

				id++;
				data.push({
					id: id,
					invoiceNumber,
					instituteId: institute.id,
					period: periodDescription,
					transactionId: Number(item.uid),
					type: Number(item.entryType),
					date: moment.unix(Number(item.generatedTime) / 1000).format('YYYY-MM-DD HH:mm:ss'),
					comment: item.comment,
					login: ledgerLogin,
					name: ledgerName,
					amount: amount,
					typeName: undefined,
					gateway: foundGateway,
					commission: commission,
					commissionRate: commissionRate,
					isFrozen: 0,
				})
			}
		}

		//FILTERING
		const type = Number(params.type, 0)
		const gateway = params.gateway
		if (type != undefined && gateway != undefined) {
			if (type != 0) {
				data = data.filter(x => x.type == type)
			}

			if (gateway != "all") {
				data = data.filter(x => x.gateway == gateway)
			}
		}

		//SORTING
		let orderBy = 'login'
		let orderAsc = 'desc'
		const paramOrder = params.field
		if (!paramOrder) {
			orderBy = 'date'
			orderAsc = 'desc'
		}
		switch (paramOrder) {
			case 'date':
				orderBy = 'date'
				orderAsc = 'asc'
				break;
			case 'login':
				orderBy = 'login'
				orderAsc = 'asc'
				break;
			case 'type':
				orderBy = 'type'
				orderAsc = 'asc'
				break;
			case 'gateway':
				orderBy = 'gateway'
				orderAsc = 'asc'
				break;
			case 'amount':
				orderBy = 'amount'
				orderAsc = 'asc'
				break;
			case 'commission':
				orderBy = 'commission'
				orderAsc = 'asc'
				break;

			//desc
			case 'dated':
				orderBy = 'date'
				orderAsc = 'desc'
				break;
			case 'logind':
				orderBy = 'login'
				orderAsc = 'desc'
				break;
			case 'typed':
				orderBy = 'type'
				orderAsc = 'desc'
				break;
			case 'gatewayd':
				orderBy = 'gateway'
				orderAsc = 'desc'
				break;
			case 'amountd':
				orderBy = 'amount'
				orderAsc = 'desc'
				break;
			case 'commissiond':
				orderBy = 'commission'
				orderAsc = 'desc'
				break;
		}

		const orderedData = data.sort((a, b) => {
			if (orderAsc == 'asc') {
				if (typeof a[orderBy] == 'string') {
					return a[orderBy].localeCompare(b[orderBy])
				}
				return Number(a[orderBy]) - Number(b[orderBy])
			}

			if (typeof a[orderBy] == 'string') {
				return b[orderBy].localeCompare(a[orderBy])
			}
			return Number(b[orderBy]) - Number(a[orderBy])
		})

		state.mtcData = orderedData

		for (const item of orderedData) {
			//veri manipülasyonu
			item.transactionId = Number(item.transactionId)
			item.type = Number(item.type)
			item.amount = Number(item.amount)
			item.commission = Number(item.commission)
			item.commissionRate = Number(item.commissionRate)

			//genel toplamlar
			totalProvision += item.amount
			totalCommission += item.commission

			if (item.type == 4) { //Deposit
				totalDeposit += item.amount
				if (item.gateway == "MTC") {
					totalMtcDeposit += item.amount
				} else if (item.gateway == "Bank") {
					totalBankDeposit += item.amount
				} else if (item.gateway == "Crypto") {
					totalCryptoDeposit += item.amount
				}
			} else if (item.type == 5) { //Withdraw
				totalWithdraw += item.amount
				if (item.gateway == "MTC") {
					totalMtcWithdraw += item.amount
				} else if (item.gateway == "Bank") {
					totalBankWithdraw += item.amount
				} else if (item.gateway == "Crypto") {
					totalCryptoWithdraw += item.amount
				}
			}

			//gösterim için
			item.date = moment(item.date).format('DD.MM.YYYY HH:mm:ss')
			item.amount = item.amount.toFixed(2)
			item.commission = item.commission.toFixed(2)
			item.isFrozen = item.isFrozen == 0 ? 'NO' : 'YES'
			item.typeName = item.type == 4 ? 'DEPOSIT' : 'WITHDRAWAL'
		}

		let summary = {
			date: moment().format('DD.MM.YYYY HH:mm:ss'),
			invoiceType: "Bank",
			invoiceNumber,

			id: cc.id,
			login: instituteId,
			businessName: cc.businessName,
			name: cc.title,
			addressFirst: (cc.addressFirst || ''),
			addressSecond: (cc.addressSecond || ''),
			addressThird: (cc.addressThird || ''),
			period: periodDescription,
			moneyType: "CHF",

			bankMtcCommissionRate,
			cryptoCommissionRate,
			provision: totalProvision.toFixed(2),
			commission: totalCommission.toFixed(2),
			amount: (totalProvision - totalCommission - (totalCryptoDeposit + totalCryptoWithdraw)).toFixed(2),
			deposit: totalDeposit.toFixed(2),
			withdraw: totalWithdraw.toFixed(2),
			bankDeposit: totalBankDeposit.toFixed(2),
			bankWithdraw: totalBankWithdraw.toFixed(2),
			mtcDeposit: totalMtcDeposit.toFixed(2),
			mtcWithdraw: totalMtcWithdraw.toFixed(2),
			cryptoDeposit: totalCryptoDeposit.toFixed(2),
			cryptoWithdraw: totalCryptoWithdraw.toFixed(2),
		}

		const invoiceData = await Invoice.findBy('invoiceNumber', invoiceNumber);
		if (invoiceData) {
			invoiceData.date = moment(invoiceData.date).format('DD.MM.YYYY')
		}

		let transactionGateways = [{ id: 'all', title: 'ALL' }, ...Common.Basic.transactionGateways] //.filter(x => x.id != 'UNKNOWN')
		let transactionTypes = [{ id: '0', title: 'ALL' }, ...Common.Basic.transactionTypes]

		return view.render("pages.forexMtc", {
			caption: caption,
			fields: mtcFieldList,
			data: orderedData,
			summary: summary,
			setting: setting,
			period: period,
			status: status,
			saveAble: saveAble,
			order: { field: paramOrder, asc: orderAsc == 'asc' ? 'd' : '' },
			url: request.url().indexOf('/order/') > 0 ? request.url().substring(0, request.url().indexOf('/order/')) : request.url(),
			invoiceFieldList,
			invoiceData,
			transactionGateways,
			transactionTypes,
			params,
		})
	}

	async previewForexMtcPdf({ request, response, view }) {
		const caption = {
			navHome: "active",
			icon: 'fas fa-money-bill-wave fa-fw',
			pageTitle: "Forex Invoice",
			route: '/previewForexMtcPdf/',
			isPrintable: true,
		}

		const setting = await BusinessService.GetSettings();

		const status = request.all().status
		const invoiceType = request.all().invoiceType
		const invoiceNumber = request.all().invoiceNumber
		const cid = Number(request.all().cid)

		const invoiceSetting = await InvoiceSetting.findBy('cid', cid);
		const bankAccount = await BankAccount.query().where('cid', cid).andWhere('invoiceType', invoiceType).fetch();
		const ibBankInfo = bankAccount.toJSON()[0];

		const provision = parseFloat(request.all().provision)
		const commission = parseFloat(request.all().commission)
		const bankMtcCommissionRate = request.all().bankMtcCommissionRate
		const cryptoCommissionRate = request.all().cryptoCommissionRate

		const deposit = parseFloat(request.all().deposit)
		const withdraw = parseFloat(request.all().withdraw)
		const bankDeposit = parseFloat(request.all().bankDeposit)
		const bankWithdraw = parseFloat(request.all().bankWithdraw)
		const mtcDeposit = parseFloat(request.all().mtcDeposit)
		const mtcWithdraw = parseFloat(request.all().mtcWithdraw)
		const cryptoDeposit = parseFloat(request.all().cryptoDeposit)
		const cryptoWithdraw = parseFloat(request.all().cryptoWithdraw)

		const requestedCredit = parseFloat(request.all().requestedCredit) || 0
		const previousCredit = parseFloat(request.all().previousCredit) || 0
		const invoiceDate = request.all().invoiceDate != 'undefined' ? request.all().invoiceDate : moment().format('DD.MM.YYYY')

		const totalBankMtc = (bankDeposit + bankWithdraw) + (mtcDeposit + mtcWithdraw)
		const totalCrypto = (cryptoDeposit + cryptoWithdraw)
		const commissionBankMtc = (bankDeposit + mtcDeposit) * bankMtcCommissionRate / 100
		const commissionCrypto = cryptoDeposit * cryptoCommissionRate / 100

		let summary = {
			invoiceType: invoiceType,
			invoiceNumber,

			cid: cid,
			businessName: request.all().businessName,
			name: request.all().name,
			addressFirst: request.all().addressFirst,
			addressSecond: request.all().addressSecond,
			addressThird: request.all().addressThird,
			period: request.all().period,
			moneyType: request.all().moneyType,
			provision: provision.toFixed(2),
			commission: commission.toFixed(2),

			bankMtcCommissionRate,
			cryptoCommissionRate,
			amount: (provision - commission - (cryptoDeposit + cryptoWithdraw)).toFixed(2),
			deposit: deposit.toFixed(2),
			withdraw: withdraw.toFixed(2),
			bankDeposit: bankDeposit.toFixed(2),
			bankWithdraw: bankWithdraw.toFixed(2),
			mtcDeposit: mtcDeposit.toFixed(2),
			mtcWithdraw: mtcWithdraw.toFixed(2),
			cryptoDeposit: cryptoDeposit.toFixed(2),
			cryptoWithdraw: cryptoWithdraw.toFixed(2),

			requestedCredit: requestedCredit.toFixed(2),
			previousCredit: previousCredit.toFixed(2),
			date: invoiceDate,
		}

		if (status == 1) {
			//eğer invoice satırı varsa update et yoksa create et
			const item = await Invoice.findBy('invoiceNumber', invoiceNumber);
			let invoiceData = summary
			invoiceData.date = moment(invoiceDate, 'DD.MM.YYYY').format('YYYY-MM-DD')
			if (item != null) {
				item.merge(invoiceData);
				await item.save();
			} else {
				await Invoice.create(invoiceData);
			}
		}

		summary.totalBankMtc = totalBankMtc.toFixed(2);
		summary.totalCrypto = totalCrypto.toFixed(2);
		summary.commissionBankMtc = commissionBankMtc.toFixed(2);
		summary.commissionCrypto = commissionCrypto.toFixed(2);

		if (ibBankInfo) {
			summary.bankName = ibBankInfo.bankName || ''
			summary.bankAddress = ibBankInfo.bankAddress || ''
			summary.accountName = ibBankInfo.accountName || ''
			summary.beneficiaryAddress = ibBankInfo.beneficiaryAddress || ''
			summary.iban = ibBankInfo.iban || ''
			summary.bicSwift = ibBankInfo.bicSwift || ''
			summary.provisionAccount = ibBankInfo.provisionAccount || ''
		}

		summary.date = moment(summary.date, 'YYYY-MM-DD').format('DD.MM.YYYY')
		return view.render("pages.invoiceForexMtcPdf", {
			caption: caption,
			summary: summary,
			setting: setting,
			invoiceSetting: invoiceSetting,
			data: state.mtcData,
		})
	}

	async saveForexMtc({ request, response, session, params }) {
		try {
			const invoiceNumber = Number(request.all().invoiceNumber)
			const period = request.all().period
			const status = request.all().status

			for (let record of state.mtcData) {
				let item = await MtcReport.findBy('transactionId', record.transactionId)
				const mysqlDate = moment(record.date, 'DD.MM.YYYY HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

				if (item == null) {
					await MtcReport.create({
						instituteId: Number(record.instituteId),
						period: period,
						invoiceNumber: invoiceNumber,
						transactionId: record.transactionId,
						type: record.type,
						date: mysqlDate,
						comment: record.comment,
						login: Number(record.login),
						name: record.name,
						gateway: record.gateway,
						amount: Number(record.amount) || 0,
						commission: Number(record.commission) || 0,
						commissionRate: Number(record.commissionRate) || 0,
						isFrozen: record.isFrozen == "YES" ? 1 : 0,
					})
				}
				else {
					item.instituteId = Number(record.instituteId)
					item.period = period
					item.invoiceNumber = invoiceNumber
					item.type = record.type
					item.date = mysqlDate
					item.comment = record.comment
					item.login = Number(record.login)
					item.name = record.name
					item.gateway = record.gateway
					item.amount = Number(record.amount) || 0
					item.commission = Number(record.commission) || 0
					item.commissionRate = Number(record.commissionRate) || 0
					item.isFrozen = record.isFrozen == "YES" ? 1 : 0

					await record.save()
				}
			}

			session.flash({ info: "Report Saved" })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async freezeForexMtc({ request, response, session, params }) {
		try {
			const invoiceNumber = Number(request.all().invoiceNumber)
			const period = request.all().period

			await MtcReport.query()
				.where('period', period)
				.andWhere('invoiceNumber', invoiceNumber)
				.update({
					isFrozen: 2
				});

			session.flash({ info: "Report Frozen" })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async createForexMtc({ request, response, session }) {
		try {
			await MtcReport.create({
				invoiceNumber: Number(request.all().invoiceNumber),
				period: request.all().period,
				transactionId: Number(moment().format('YYYYMMDDHHmmss')),

				date: request.all().date,
				login: parseInt(request.all().login),
				name: request.all().name,
				gateway: request.all().gateway,
				type: parseInt(request.all().type),
				amount: parseFloat(request.all().amount),
				commission: parseFloat(request.all().commission),
				commissionRate: parseFloat(request.all().commissionRate),
				comment: request.all().comment,
			})

			session.flash({ info: "It was saved successfuly" })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async updateForexMtc({ request, response, session, params }) {
		try {
			const record = await MtcReport.find(params.id)

			record.date = request.all().date
			record.login = parseInt(request.all().login)
			record.name = request.all().name
			record.gateway = request.all().gateway
			record.type = parseInt(request.all().type)
			record.amount = parseFloat(request.all().amount)
			record.commission = parseFloat(request.all().commission)
			record.commissionRate = parseFloat(request.all().commissionRate)
			record.comment = request.all().comment

			await record.save()

			session.flash({ info: "It was updated successfuly" })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async deleteForexMtc({ response, session, params }) {
		try {
			const record = await MtcReport.find(params.id);
			await record.delete();

			session.flash({ info: "It was deleted successfuly" })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async updateInvoice({ request, response, session, params }) {
		try {
			const record = await Invoice.findBy("invoiceNumber", params.id)

			record.date = request.all().date
			record.requestedCredit = parseFloat(request.all().requestedCredit)
			record.previousCredit = parseFloat(request.all().previousCredit)

			await record.save()

			session.flash({ info: "It was updated successfuly" })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = ReportController
