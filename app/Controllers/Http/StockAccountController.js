'use strict'

const Database = use("Database")
const BusinessService = use("App/Helpers/BusinessService");
const Record = use("App/Models/StockAccount")

const axios = require("axios")

const accountTypes = [
	{ id: 1, title: 'Stock Account' },
	{ id: 2, title: 'Cashbox Account' },
	{ id: 3, title: 'Withdrawal Account' },
]

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'accountName', c: 'Account Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'accountType', c: 'Account Type', i: 'fas fa-tag fa-fw', t: 'select', v: accountTypes, e: true },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true },
	{ n: 'personId', c: 'Connected ID', i: 'fas fa-users fa-fw', t: 'text', v: '', e: true },
]

const caption = {
	navSetting: "active",
	pageTitle: "Own Accounts",
	icon: 'fas fa-user fa-fw',
	route: '/stocks/accounts/',
	newAction: 'createAccount',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
}

class StockAccountController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();
		const list = await Record.query().orderBy('id', 'desc').fetch()

		return view.render("pages.stockAccount", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting,
		})
	}

	async update({ request, response, session, params }) {
		try {
			let record = await Record.find(params.id)
			record.accountName = request.all().accountName
			record.accountType = parseInt(request.all().accountType)
			record.login = parseInt(request.all().login)
			record.personId = parseInt(request.all().personId)

			await record.save()
			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

}

module.exports = StockAccountController
