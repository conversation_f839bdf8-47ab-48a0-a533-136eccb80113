'use strict'
const BusinessService = use('App/Helpers/BusinessService');

const Bfi = use("App/Models/Bfi")
const Record = use("App/Models/Terminal")
const SystemLog = use("App/Models/SystemLog")
const TerminalNote = use("App/Models/TerminalNote")
const TerminalKey = use("App/Models/TerminalKey")
const Database = use('Database')

const devicePorts = [
	{ id: "COM1", title: "COM1" },
	{ id: "COM2", title: "COM2" },
	{ id: "COM3", title: "COM3" },
	{ id: "COM4", title: "COM4" },
	{ id: "COM5", title: "COM5" },
	{ id: "COM6", title: "COM6" },
	{ id: "COM7", title: "COM7" },
	{ id: "COM8", title: "COM8" },
]

const langs = [
	{ id: "en", title: "en" },
	{ id: "de", title: "de" },
	{ id: "fr", title: "fr" },
	{ id: "it", title: "it" },
]

const status = [
	{ id: 0, title: "<i class='fas fa-power-off'></i> Off" },
	{ id: 1, title: "<i class='fas fa-wifi'></i> On" },
	{ id: 2, title: "<i class='fas fa-tools'></i> Out of Service" },
]

const hasPrinter = [
	{ id: 0, title: "No Printer" },
	{ id: 1, title: "Printer Active" },
]

const commands = [
	{ id: "check", title: "Check Status" },
	{ id: "offservice", title: "Out of Service" },
	{ id: "onservice", title: "On Service" },
	{ id: "close", title: "Close App" },
	{ id: "restart", title: "Restart App" },
	{ id: "update", title: "Update App Now" },
	{ id: "version", title: "Get App Version" },
	{ id: "resetDevice", title: "Reset Money Device" },
	{ id: "payoutInfo", title: "Get Payout Money Amount" },
	{ id: "payoutEmpty", title: "Empty Payout to Cashbox" },
	{ id: "cashboxReset", title: "Reset Cashbox Amount" },
	{ id: "versionUpdate", title: "Get Update.exe Version" },
	{ id: "updateExe", title: "Upgrade Update.exe Now" },
	{ id: "shutdown", title: "Shutdown Terminal" },
	{ id: "restartpc", title: "Restart Terminal" },
]

const targetTerminal = { n: 'targetTerminal', c: 'Target Terminal', i: 'fas fa-desktop fa-fw', t: 'select', v: '', e: true };

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-tag fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'description', c: 'Description', i: 'fas fa-certificate fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'devicePort', c: 'Port', i: 'fas fa-plug fa-fw', t: 'select', v: devicePorts, e: true, ne: true },
	{ n: 'showUrl', c: 'Show Url', i: 'fas fa-qrcode fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'lang', c: 'Lang', i: 'fas fa-flag fa-fw', t: 'select', v: langs, e: true, ne: true },
	{ n: 'answer', c: 'Answer', i: 'fas fa-asterisk fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'payoutTotalMoney', c: 'Payout', i: 'fas fa-money-bill-alt fa-fw', t: 'number', v: 0, e: false, ne: false },
	{ n: 'payoutMoneyDetail', c: 'Payout Detail', i: 'fas fa-money-bill-alt fa-fw', t: 'text', v: '', e: false, ne: false },
	//{ n: 'cashboxTotalMoney', c: 'Cashbox', i: 'fas fa-money-bill-wave-alt fa-fw', t: 'number', v: 0, e: false, ne: true },
	{ n: 'isPrint', c: 'Has Printer', i: 'fas fa-print fa-fw', t: 'select', v: hasPrinter, e: true, ne: true },
	{ n: 'bId', c: 'BFI', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true, ne: true },
	{ n: 'depositLimit', c: 'General Deposit Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Deposit Limit' },
	{ n: 'withdrawalLimit', c: 'General Withdrawal Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Withdrawal Limit' },
	{ n: 'status', c: 'Status', i: 'fas fa-satellite-dish fa-fw', t: 'select', v: status, e: false, ne: true },
	{ n: 'command', c: 'Command', i: 'fas fa-terminal fa-fw', t: 'select', v: commands, e: true, ne: false },
]

const caption = {
	navTerminal: "active",
	pageTitle: "Terminals",
	icon: 'fas fa-desktop fa-fw',
	route: '/terminals/',
	newAction: 'createTerminal',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	migrateData: "Migrate Data",
	migrateWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure migrate this terminal?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	migrate: "Migrate Terminal",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	migratedMessage: "It was migrated successfuly",
	send: "Send",
	commandData: "Send Command All Terminals",
	commandAction: 'updateAllTerminals',
}

class TerminalController {
	setting;

	constructor() {
		BusinessService.GetSettings()
			.then((s) => {
				this.setting = s;
			})
			.catch((err) => {
				console.error(err);
			});
	}

	async index({ view }) {

		const list = await Record.query().orderBy('id').fetch()
		const terminalList = await Record.query().fetch()
		const terminals = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		targetTerminal.v = terminals;

		const bfiList = await Bfi.query().fetch()
		fieldList[9].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})

		return view.render("pages.terminal", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			targetTerminal: targetTerminal,
			setting: this.setting,
		})
	}

	async query({ view }) {

		const list = await Record.query().orderBy('id').fetch()
		const terminalList = await Record.query().fetch()
		const terminals = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		targetTerminal.v = terminals;

		const bfiList = await Bfi.query().fetch()
		fieldList[9].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})

		//her açılışta status check
		let set = { doCommand: "check", status: 0, answer: "" }
		await Database
			.table('terminal')
			.whereIn('status', [1, 2])
			.update(set)

		return view.render("pages.terminal", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			targetTerminal: targetTerminal,
			setting: this.setting,
		})
	}

	async create({ request, response, session }) {
		try {
			await Record.create({
				terminalCode: request.input("terminalCode"),
				description: request.input("description") || '',
				//showUrl: request.input("showUrl") || '',
				devicePort: request.input("devicePort"),
				startupLang: request.input("lang"),
				//doCommand: request.input("command"),
				isPrint: request.input("isPrint"),
				bId: request.input("bId"),
				status: request.input("status"),
				active: request.input("active"),
				depositLimit: request.input("depositLimit"),
				withdrawalLimit: request.input("withdrawalLimit"),
			})
			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params, auth }) {
		try {
			const record = await Record.find(params.id)
			record.terminalCode = request.all().terminalCode
			record.description = request.all().description || ""
			//record.showUrl = request.all().showUrl || ""
			record.devicePort = request.all().devicePort
			record.startupLang = request.all().lang
			if (request.all().command == "check") {
				record.status = 0
				record.answer = ""
				record.doCommand = record.doCommand
			} else {
				record.doCommand = request.all().command
			}
			record.isPrint = request.all().isPrint
			record.bId = request.all().bId
			record.active = request.all().active
			record.depositLimit = parseInt(request.all().depositLimit)
			record.withdrawalLimit = parseInt(request.all().withdrawalLimit)

			await record.save()
			await SystemLog.create({
				terminalCode: request.all().terminalCode,
				log: `${auth.user.email} sent terminal Command >_ [${request.all().command}]`,
			})

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params, auth }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();

			await SystemLog.create({
				terminalCode: record.terminalCode,
				log: `${auth.user.email} deleted terminal !!!`,
			})

			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async updateAll({ request, response, session, params }) {
		try {
			let set = { doCommand: request.all().command }
			if (request.all().command == "check") {
				set = { doCommand: request.all().command, status: 0, answer: "" }
			}
			await Database
				.table('terminal')
				.whereIn('status', [1, 2])
				.update(set)

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async migrate({ request, response, session, params }) {
		try {
			const targetId = request.all().targetTerminal
			const targetTerminal = await Record.find(targetId);
			const terminal = await Record.find(params.id);
			let set = { terminalCode: targetTerminal.terminalCode }

			await Database.table('person')
				.whereIn('terminalCode', [terminal.terminalCode])
				.update(set);

			await Database.table('transfer')
				.whereIn('terminalCode', [terminal.terminalCode])
				.update(set);

			await SystemLog.create({
				terminalCode: terminal.terminalCode,
				log: `${auth.user.email} migrated terminal to ${targetTerminal.terminalCode} !!!`,
			})

			session.flash({ info: caption.migratedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

}
module.exports = TerminalController
