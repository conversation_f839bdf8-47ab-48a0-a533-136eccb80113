'use strict'

const BusinessService = use("App/Helpers/BusinessService");

const Database = use("Database")
const Record = use("App/Models/Transfer")
const Institutes = use("App/Models/Institute")
const Bfi = use("App/Models/Bfi")
const Terminals = use("App/Models/Terminal")

const moment = require("moment");
moment.defaultFormat = "DD.MM.YYYY HH:mm";
let dateTimeNow = moment().format('YYYY-MM-DD HH:mm:ss')
let dateToday = moment().format('YYYY-MM-DD')

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'name', c: 'Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'surname', c: 'Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'financeInstitute', c: 'Institute', i: 'fas fa-money-check-alt fa-fw', t: 'select', v: [], e: true, ne: true },
	{ n: 'amount', c: 'Amount', i: 'fas fa-money-bill-alt fa-fw', t: 'number', v: 0, e: true, ne: true },
	{ n: 'currencyType', c: 'Currency', i: 'fas fa-tag fa-fw', t: 'text', v: 'CHF', e: false, ne: true },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'bId', c: 'BFI', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true, ne: true },
	{ n: 'created_date', c: 'Date', i: 'fas fa-tag fa-fw', t: 'dateTime', v: dateTimeNow, e: true, ne: false },
]

const queryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true },
	{ n: 'financeInstitute', c: 'Institute', i: 'fas fa-money-check-alt fa-fw', t: 'select', v: [], e: true },
	{ n: 'amount', c: 'Amount', i: 'fas fa-money-bill-alt fa-fw', t: 'number', v: 0, e: true },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true },
	{ n: 'bId', c: 'BFI', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true },
	{ n: 'beginDate', c: 'Begin', i: 'fas fa-calendar fa-fw', t: 'date', v: dateToday, e: true },
	{ n: 'endDate', c: 'End', i: 'fas fa-calendar fa-fw', t: 'date', v: dateToday, e: true },
]

const caption = {
	navStocks: "active",
	pageTitle: "Stock Money",
	icon: 'fas fa-hand-holding-usd fa-fw',
	route: '/stocks/',
	newAction: 'createStocks',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	query: 'Query',
	queryRoute: '/stocks',
	queryData: 'Data Query',
	queryAction: 'queryTransfer',
	close: "Close",
	find: "Find",
	detailData: 'Details',
	detailInfo: 'Processed By: ',
}

const currentQuery = {
	tradeId: null,
	amount: null,
	end: null,
	begin: null,
	terminalCode: null,
	financeInstitute: null,
	bId: null,
}

const summary = {
	totalAmount: 0,
	moneyType: "CHF"
}

class StocksController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();

		let today = new Date()
		today.setDate(today.getDate() + 1)
		let end = moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 365)
		let begin = moment(today).format('YYYY-MM-DD')

		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = null
		currentQuery.amount = null
		currentQuery.terminalCode = null
		currentQuery.financeInstitute = null
		currentQuery.bId = null

		let list = await Record.query()
			.join('person', (query) => {
				query
					.on((subquery) => {
						subquery
							.on('person.login', '=', 'transfer.login')
					})
			})
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 1).select('stocks.login'))
			.andWhereBetween('transfer.created_date', [begin, end])
			.orderBy('transfer.created_date', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();

		list = list.toJSON()
		summary.totalAmount = 0
		list.forEach(element => {
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[6].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[3].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfi.query().fetch()
		fieldList[7].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.stocks", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list,
			setting: setting,
			summary: summary,
		})
	}

	async query({ view, request }) {
		const setting = await BusinessService.GetSettings();

		let today = new Date()
		let tradeId = request.all().login
		let amount = request.all().amount
		today.setDate(today.getDate() + 1)
		let end = request.all().endDate || moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 8)
		let begin = request.all().beginDate || moment(today).format('YYYY-MM-DD')
		let terminalCode = request.all().terminalCode
		let financeInstitute = request.all().financeInstitute
		let bId = request.all().bId

		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = tradeId
		currentQuery.amount = amount
		currentQuery.terminalCode = terminalCode
		currentQuery.financeInstitute = financeInstitute
		currentQuery.bId = bId

		let list = await Database //Database.schema.raw('SELECT * FROM posts')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.from('transfer')
			.leftJoin('terminal', 'transfer.terminalCode', 'terminal.terminalCode')
			.leftJoin('person', 'transfer.login', 'person.login')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 1).select('stocks.login'))
			.andWhere(function () {
				this.whereBetween('transfer.created_date', [begin, end])
				if (tradeId) {
					this.andWhere('transfer.login', tradeId)
				}
				if (amount) {
					this.andWhere('transfer.amount', '=', amount)
				}
				if (terminalCode && terminalCode != 0) {
					this.andWhere('terminal.id', terminalCode)
				}
				if (financeInstitute && financeInstitute != 0) {
					this.andWhere('transfer.financeInstitute', financeInstitute)
				}
				if (bId > 0) {
					this.andWhere('transfer.bId', bId)
				}
			})
			.orderBy('transfer.created_date', 'desc')

		summary.totalAmount = 0
		list.forEach(element => {
			element.created_date = moment(element.created_date).format()
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[6].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[3].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfi.query().fetch()
		fieldList[7].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		//queryFieldList[5].v = new Date()
		//queryFieldList[6].v = new Date()

		return view.render("pages.stocks", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list,
			setting: setting,
			summary: summary
		})
	}

	async queryByLogin({ view, params }) {
		const setting = await BusinessService.GetSettings();

		currentQuery.begin = moment(new Date("2020-01-01")).format('YYYY-MM-DD')
		currentQuery.end = moment(new Date()).format('YYYY-MM-DD')
		currentQuery.tradeId = params.login
		currentQuery.amount = null
		currentQuery.terminalCode = null
		currentQuery.financeInstitute = null
		currentQuery.bId = null

		let list = await Record.query()
			.join('person', (query) => {
				query
					.on((subquery) => {
						subquery
							.on('person.login', '=', 'transfer.login')
					})
			})
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where('transfer.login', '=', params.login)
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();

		list = list.toJSON()
		summary.totalAmount = 0
		list.forEach(element => {
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		const terminalList = await Terminals.query().fetch()
		fieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		queryFieldList[3].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfi.query().fetch()
		fieldList[7].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v = bfiList.toJSON().map(b => {
			let id = b.id;
			let title = b.businessName;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.stocks", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list,
			setting: setting,
			summary: summary,
		})
	}

	async create({ request, response, session, auth }) {
		try {
			await Record.create({
				login: parseInt(request.all().login),
				financeInstitute: parseInt(request.all().financeInstitute),
				terminalCode: request.all().terminalCode,
				amount: parseInt(request.all().amount),
				currencyType: request.all().currencyType,
				detail: auth.user.email,
				bId: parseInt(request.all().bId),
			})

			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params, auth }) {
		try {
			let record = await Record.find(params.id)
			record.financeInstitute = parseInt(request.all().financeInstitute)
			record.terminalCode = request.all().terminalCode
			record.amount = parseInt(request.all().amount)
			record.detail = auth.user.email
			record.bId = parseInt(request.all().bId)
			record.login = request.all().login
			record.created_date = request.all().created_date

			await record.save()

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

}

module.exports = StocksController
