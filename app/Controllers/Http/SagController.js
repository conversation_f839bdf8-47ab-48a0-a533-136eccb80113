'use strict'

const Database = use("Database")
const BusinessService = use("App/Helpers/BusinessService");
const Record = use("App/Models/Sag")

const isActive = [
	{ id: 0, title: "Passive" },
	{ id: 1, title: "Active" },
]

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'businessName', c: 'Business Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'isActive', c: 'Status', i: 'fas fa-check fa-fw', t: 'select', v: isActive, e: true, ne: true },
	{ n: 'commissionRate', c: 'Commission Rate on 300', i: 'fas fa-percentage', t: 'number', v: 0, e: true, ne: true, l: '300 /' },
]

const caption = {
	navSetting: "active",
	pageTitle: "SAG",
	icon: 'fas fa-warehouse fa-fw',
	route: '/sag/',
	editData: "Edit Data",
	close: "Close",
	new: "New",
	save: "Save",
	edit: "Edit",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
}

class SagController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();
		const list = await Record.query().orderBy('id', 'desc').fetch()

		return view.render("pages.sag", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting,
		})
	}

	async update({ request, response, session, params }) {
		try {
			let record = await Record.find(params.id)
			record.businessName = request.all().businessName
			record.isActive = request.all().isActive
			record.commissionRate = request.all().commissionRate

			await record.save()
			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

}

module.exports = SagController
