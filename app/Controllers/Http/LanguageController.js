'use strict'

String.prototype.validForDb = function (text) {
    let result = this
    return result.replace(/(\r\n|\n)/gm, "<br />").replace(/(\')/gm, "`").replace(/(\")/gm, "¨").replace(/(\r|\t)/gm, " ")
};

const BusinessService = use("App/Helpers/BusinessService");
const Record = use("App/Models/Language")

const fieldList = [//name,caption,icon,type,value,enabled,row
    { n: 'key', c: 'Key', i: 'fas fa-hashtag fa-fw', t: 'text', v: '', e: true },
    { n: 'de', c: 'Deutsch', i: 'fas fa-flag-checkered fa-fw', t: 'textarea', v: '', e: true, r: 2 },
    { n: 'fr', c: 'Français', i: 'fas fa-flag-checkered fa-fw', t: 'textarea', v: '', e: true, r: 2 },
    { n: 'it', c: 'Italiano', i: 'fas fa-flag-checkered fa-fw', t: 'textarea', v: '', e: true, r: 2 },
    { n: 'en', c: 'English', i: 'fas fa-flag-checkered fa-fw', t: 'textarea', v: '', e: true, r: 2 },
]

const caption = {
    navLanguage: "active",
    pageTitle: "Languages",
    icon: 'fas fa-flag fa-fw',
    route: '/languages/',
    newAction: 'createLanguage',
    newData: "New Entry",
    editData: "Edit Data",
    deleteData: "Delete Data",
    deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
    close: "Close",
    new: "New",
    save: "Save",
    add: "Add",
    edit: "Edit",
    delete: "Delete",
    active: "Active",
    passive: "Passive",
    savedMessage: "It was saved successfuly",
    updatedMessage: "It was updated successfuly",
    deletedMessage: "It was deleted successfuly",
}

class LanguageController {

    async index({ view }) {
        const setting = await BusinessService.GetSettings();
        const list = await Record.query().orderBy('id').fetch()
        return view.render("pages.language", {
            caption: caption,
            fields: fieldList,
            data: list.toJSON(),
            setting: setting,
        })
    }

    async create({ request, response, session }) {
        try {
            await Record.create({
                key: request.input("key").validForDb(),
                en: request.input("en").validForDb(),
                de: request.input("de").validForDb(),
                fr: request.input("fr").validForDb(),
                it: request.input("it").validForDb(),
            })
            session.flash({ info: caption.savedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }

    async update({ request, response, session, params }) {
        try {
            const record = await Record.find(params.id)
            record.key = request.all().key
            record.en = request.all().en.validForDb()
            record.de = request.all().de.validForDb()
            record.fr = request.all().fr.validForDb()
            record.it = request.all().it.validForDb()
            await record.save()
            session.flash({ info: caption.updatedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }

    async delete({ response, session, params }) {
        try {
            const record = await Record.find(params.id);
            await record.delete();
            session.flash({ info: caption.deletedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }

}
module.exports = LanguageController

