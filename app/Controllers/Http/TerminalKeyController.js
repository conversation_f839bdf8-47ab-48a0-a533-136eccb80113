'use strict'

const BusinessService = use("App/Helpers/BusinessService");

const Database = use("Database")
const Record = use("App/Models/TerminalKey")
const Terminals = use("App/Models/Terminal")

const moment = require("moment");
moment.defaultFormat = "DD.MM.YYYY HH:mm";

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'terminalId', c: 'Terminal ID', i: 'fas fa-desktop fa-fw', t: 'number', v: 0, e: false, ne: false },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'keyName', c: 'Key', i: 'fas fa-key fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'ownerName', c: 'Owner', i: 'fas fa-user fa-fw', t: 'text', v: '', e: true, ne: true },
]

const caption = {
	navTerminal: "active",
	pageTitle: "Terminal Keys",
	icon: 'fas fa-key fa-fw',
	route: '/terminalKeys/',
	newAction: 'createTerminalKey',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	close: "Close",
}

class TerminalKeyController {

	async queryByTerminalId({ view, params }) {
		const setting = await BusinessService.GetSettings();

		let list = await Record.query().join('terminal', (query) => {
			query
				.on((subquery) => {
					subquery
						.on('terminal.id', '=', 'terminalKey.terminalId')
				})
		})
			.where('terminalKey.terminalId', '=', params.terminalId)
			.orderBy('terminalKey.id', 'desc')
			.select('terminalKey.*')
			.select('terminal.terminalCode')
			.fetch();

		list = list.toJSON()

		return view.render("pages.terminalKey", {
			caption: caption,
			fields: fieldList,
			data: list,
			setting: setting,
			terminalId: params.terminalId
		})
	}

	async create({ request, response, session, params }) {
		try {
			await Record.create({
				terminalId: parseInt(params.terminalId),
				keyName: request.all().keyName,
				ownerName: request.all().ownerName,
			})

			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params }) {
		try {
			let record = await Record.find(params.id)
			record.keyName = request.all().keyName
			record.ownerName = request.all().ownerName

			await record.save()
			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = TerminalKeyController
