'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const Database = use("Database")
const Record = use("App/Models/SystemLog")

const moment = require("moment")
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: false },
	{ n: 'log', c: 'Log', i: 'fas fa-history fa-fw', t: 'text', v: '', e: false },
	{ n: 'created_date', c: 'Date', i: 'fas fa-tag fa-fw', t: 'text', v: '', e: false },
]

let date = moment().format('YYYY-MM-DD')
const queryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true },
	{ n: 'log', c: 'Log', i: 'fas fa-history fa-fw', t: 'text', v: '', e: true },
	{ n: 'beginDate', c: 'Begin', i: 'fas fa-calendar fa-fw', t: 'date', v: date, e: true },
	{ n: 'endDate', c: 'End', i: 'fas fa-calendar fa-fw', t: 'date', v: date, e: true },
]

const caption = {
	navLog: "active",
	pageTitle: "Logs",
	icon: 'fas fa-history fa-fw',
	route: '/logs/',
	close: "Close",
	query: 'Query',
	queryRoute: '/logs',
	queryData: 'Data Query',
	queryAction: 'queryLog',
	close: "Close",
	find: "Find",
	detailData: 'Details',
	detailInfo: 'Log Detail: ',
}

const currentQuery = {
	terminalCode: null,
	log: null,
	end: null,
	begin: null,
}

class SystemLogController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();

		let today = new Date()
		today.setDate(today.getDate() + 1)
		let end = moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 30)
		let begin = moment(today).format('YYYY-MM-DD')

		currentQuery.begin = begin
		currentQuery.end = end

		let list = await Record.query().orderBy('id', 'desc').limit(2000).fetch();

		return view.render("pages.systemlog", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list.toJSON(),
			setting: setting,
		})
	}

	async query({ view, request }) {
		const setting = await BusinessService.GetSettings();

		let today = new Date()
		let log = request.all().log
		today.setDate(today.getDate() + 1)
		let end = request.all().endDate || moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 8)
		let begin = request.all().beginDate || moment(today).format('YYYY-MM-DD')
		let terminalCode = request.all().terminalCode

		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.log = log
		currentQuery.terminalCode = terminalCode

		let list = await Record.query()
			.where(function () {
				this.whereBetween('created_date', [begin, end])
				if (log) {
					this.andWhere('log', 'like', '%' + log + '%')
				}
				if (terminalCode) {
					this.andWhere('terminalCode', terminalCode)
				}
			})
			.orderBy('id', 'desc')
			.fetch();

		return view.render("pages.systemlog", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list.toJSON(),
			setting: setting,
		})
	}

	async create({ request, response, session }) {
		try {
			await Record.create({
				terminalCode: request.all().terminalCode,
				log: request.all().log,
			})
			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

}

module.exports = SystemLogController
