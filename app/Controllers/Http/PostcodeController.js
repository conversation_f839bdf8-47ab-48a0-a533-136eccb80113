'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const Postcode = use("App/Models/Postcode")

const caption = {
	navPostcode: "active",
	pageTitle: "User Informations",
	icon: 'fas fa-user fa-fw',
}

class PostcodeController {
	/*
	const posts = await use(`App/Models/Post`)
		.query()
		.select(['id', 'user_id', 'post_title'])
		.with("user", builder => {
			return builder
				.select(["id", "username"])
		})
		.orderBy('user.username', 'desc')
		.limit(3)
		.fetch()
	*/

	async index({ view, request, params }) {
		const setting = await BusinessService.GetSettings();
		//const list = await Postcode.pick(100) //bu static method(satatic methodlarda fetch kullanılmıyor) primarykey id istiyor bu yüzden model kısmına primarykey code olsun diye atadık.
		const list = await Postcode.query().paginate(1) //sayfalama örneği
		return view.render("pages.postcode", {
			title: "Post Codes",
			caption: caption,
			page: list.toJSON(),
			setting: setting,
		})
	}

	async page({ view, request, params }) {
		let page = params.page
		if (isNaN(page)) {
			page = 1
		}
		const list = await Postcode.query().paginate(page) //sayfalama örneği
		return view.render("pages.postcode", {
			title: "Post Codes",
			caption: caption,
			page: list.toJSON(),
		})
	}

}

module.exports = PostcodeController
