'use strict'

const BusinessService = use('App/Helpers/BusinessService');
const Record = use("App/Models/Button")

const Helpers = use("Helpers")

const fieldList = [//name,caption,icon,type,value,enabled
    { n: 'caption', c: 'Caption', i: 'fas fa-tag', t: 'text', v: '', e: true },
    { n: 'menuOrder', c: 'Order', i: 'fas fa-sort', t: 'number', v: '', e: true },
    { n: 'url', c: 'Button Link', i: 'fas fa-link', t: 'text', v: '', e: true },
    { n: 'image', c: 'De', i: 'fas fa-image', t: 'file', v: '', e: true },
    { n: 'imageFr', c: 'Fr', i: 'fas fa-image', t: 'file', v: '', e: true },
    { n: 'imageIt', c: 'It', i: 'fas fa-image', t: 'file', v: '', e: true },
    { n: 'imageEn', c: 'En', i: 'fas fa-image', t: 'file', v: '', e: true },
]

const caption = {
    navButton: "active",
    pageTitle: "Menu Buttons",
    icon: 'fas fa-grip-horizontal fa-fw',
    route: '/buttons/',
    newAction: 'createButton',
    newData: "New Entry",
    editData: "Edit Data",
    deleteData: "Delete Data",
    deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
    close: "Close",
    new: "New",
    save: "Save",
    add: "Add",
    edit: "Edit",
    delete: "Delete",
    active: "Active",
    passive: "Passive",
    savedMessage: "It was saved successfuly",
    updatedMessage: "It was updated successfuly",
    deletedMessage: "It was deleted successfuly",
    selectFile: "Select File",
}

class ButtonController {

    async index({ view }) {
        const setting = await BusinessService.GetSettings();

        const list = await Record.query().orderBy('id').fetch()
        return view.render("pages.button", {
            caption: caption,
            fields: fieldList,
            data: list.toJSON(),
            setting: setting,
        })
    }

    async create({ request, response, session }) {
        try {
            await Record.create({
                caption: request.all().caption,
                menuOrder: request.all().menuOrder,
                url: request.all().url,
                image: request.all().image,
                active: request.all().active,
            })
            const image = request.file("imageFile")
            await image.move(Helpers.publicPath('MenuImages'), { overwrite: true })//resmi upload etme
            session.flash({ info: caption.savedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }

    async update({ request, response, session, params }) {
        try {
            const record = await Record.find(params.id)
            record.caption = request.all().caption
            record.menuOrder = request.all().menuOrder
            record.url = request.all().url
            record.image = request.all().image
            record.active = request.all().active
            await record.save()

            const image = request.file("imageFile")
            const imageFr = request.file("imageFrFile")
            const imageIt = request.file("imageItFile")
            const imageEn = request.file("imageEnFile")
            if (image != null) { await image.move(Helpers.publicPath('MenuImages/de'), { overwrite: true }) }
            if (imageFr != null) { await imageFr.move(Helpers.publicPath('MenuImages/fr'), { overwrite: true }) }
            if (imageIt != null) { await imageIt.move(Helpers.publicPath('MenuImages/it'), { overwrite: true }) }
            if (imageEn != null) { await imageEn.move(Helpers.publicPath('MenuImages/en'), { overwrite: true }) }

            session.flash({ info: caption.updatedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }

    async delete({ response, session, params }) {
        try {
            const record = await Record.find(params.id);
            await record.delete();
            session.flash({ info: caption.deletedMessage })
            return response.route("back")
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("home")
        }
    }
}

module.exports = ButtonController
