'use strict'

const BusinessService = use('App/Helpers/BusinessService');

const Database = use("Database")
const Bfi = use("App/Models/Bfi")
const BfiLogin = use("App/Models/BfiLogin")
const BfiLink = use("App/Models/BfiLink")
const Institute = use("App/Models/Institute")
const Terminal = use("App/Models/Terminal")

const { query } = require("@adonisjs/lucid/src/Lucid/Model");
const moment = require("moment")
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const caption = {
	navHome: "active",
	icon: 'fas fa-home fa-fw',
	version: "8.0 Dockerized",
	lastUpdate: "06.03.2025",
}

class HomeController {

	async index({ request, response, view }) {
		const setting = await BusinessService.GetSettings();

		if (setting.appType != 'panel' && setting.appType != 'report') {
			return response.status(403).send('Invalid App Type')
			//throw new HttpException('Invalid request origin', 403);
		}

		if (setting.appType == 'panel') {
			return view.render("pages.home", {
				pageTitle: "Trade Center Management",
				title: "Management Panel",
				subtitle: "This Web Application manage everything about Trade Center Kiosk system securely.",
				data: "Now login, then you can watch all terminals.",
				buttonCaption: "Show Terminals",
				caption: caption,
				setting: setting,
			})
		} else if (setting.appType == 'report') {
			if (setting.company == 'tradecenter24') {
				return view.render("reports.home", {
					pageTitle: "Trade Center Reports",
					title: "Report",
					subtitle: "This Web Application give Report from Trade Center system securely.",
					data: "Now login, then you can select reports.",
					buttonCaption: "Select Reports",
					caption: caption,
					setting: setting,
				})
			} else if (setting.company == 'forex724') {
				return view.render("reports.home", {
					pageTitle: "Forex724 Reports",
					title: "Report",
					subtitle: "This Web Application give Report from Forex724 system securely.",
					data: "Now login, then you can select reports.",
					buttonCaption: "Select Reports",
					caption: caption,
					setting: setting,
				})
			}
		} else {
			return response.route("/")
		}
	}

	async qrcodes({ request, response, session, params, view }) {
		const setting = await BusinessService.GetSettings();

		const bfiId = parseInt(params.id)
		const rawBfi = await Bfi.find(bfiId)
		let bfi = rawBfi.toJSON()
		let instituteList = []

		const rawInstitutes = await Institute.query().orderBy('order').fetch()
		const allInstitutes = rawInstitutes.toJSON()

		const linkInstitutes = allInstitutes.filter(x => x.accountCreateActive)
		for (const institute of linkInstitutes) {
			const rawBfiLink = await BfiLink.query().where(_ => {
				_.where('bfiId', bfiId)
				_.where('instituteId', institute.id)
			}).fetch();
			const bfiLink = rawBfiLink.toJSON()

			const link = encodeURIComponent(bfiLink[0].link)

			institute.bfiLink = link

			instituteList.push(institute)
		}

		bfi.institutes = instituteList

		return view.render("pages.qrcodes", {
			setting: setting,
			bfi: bfi,
		})
	}

	async bfiMap({ request, response, session, params, view }) {
		const setting = await BusinessService.GetSettings();

		const rawBfis = await Bfi.query().where(query => {
			query.where('isActive', 1)
			query.where('type', 1)
			//query.where('lat', '!=', 0)
			//query.where('long', '!=', 0)
		}).fetch()
		let bfis = rawBfis.toJSON()

		return view.render("pages.bfiMap", {
			setting: setting,
			bfis: bfis,
		})
	}

}

module.exports = HomeController
