'use strict'

const BusinessService = use('App/Helpers/BusinessService');

const Database = use("Database")
const Record = use("App/Models/Bfi")
const BfiLogin = use("App/Models/BfiLogin")
const BfiLink = use("App/Models/BfiLink")
const Institute = use("App/Models/Institute")
const Terminal = use("App/Models/Terminal")
const Sag = use("App/Models/Sag")

const moment = require("moment")
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const classification = [
	{ id: 1, title: 'Little' },
	{ id: 2, title: 'Medium' },
	{ id: 3, title: 'Strong' },
]

const level = [
	{ id: 1, title: 'Bad' },
	{ id: 2, title: 'Medium' },
	{ id: 3, title: 'Good' },
]

const access = [
	{ id: 1, title: 'Stairs' },
	{ id: 2, title: 'Lift' },
	{ id: 3, title: 'Easy' },
]

const yesNo = [
	{ id: 0, title: "No" },
	{ id: 1, title: "Yes" },
]

const isActive = [
	{ id: 0, title: "Passive" },
	{ id: 1, title: "Active" },
]

const category = [
	{ id: 1, title: "Restaurant" },
	{ id: 2, title: "Shop" },
	{ id: 3, title: "Hairdresser" },
	{ id: 4, title: "Bar" },
	{ id: 5, title: "Kitchen" },
]

const type = [
	{ id: 1, title: "Active" },
	{ id: 2, title: "Prospect" },
	{ id: 3, title: "Passive" },
]

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'businessName', c: 'Business Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Business Name' },
	{ n: 'companyName', c: 'Company Name', i: 'fas fa-building fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Company Name' },
	{ n: 'street', c: 'Company Street', i: 'fas fa-map-marked-alt fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Company Street' },
	{ n: 'zip', c: 'Company Zip', i: 'fas fa-map-marker fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Company Zip' },
	{ n: 'city', c: 'City', i: 'fas fa-map-marker fa-fw', t: 'text', v: '', e: true, ne: true, l: 'City' },
	{ n: 'contactName', c: 'Contact', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Contact' },
	{ n: 'contactPhone', c: 'Contact Phone', i: 'fas fa-phone fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Contact Phone' },
	{ n: 'contactEmail', c: 'Contact Email', i: 'fas fa-at fa-fw', t: 'email', v: '', e: true, ne: true, l: 'Contact Email' },
	//{ n: 'speedUpload', c: 'Speed Upload', i: 'fa fa-upload fa-fw', t: 'number', v: 0, e: true, ne: true },
	{ n: 'speedDownload', c: 'Speed Download/Upload', i: 'fa fa-download fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Speed Download/Upload' },
	{ n: 'distanceRouter', c: 'Distance Router', i: 'fas fa-road fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Distance Router' },
	{ n: 'socket', c: 'Socket(m)', i: 'fas fa-plug fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Socket(m)' },
	{ n: 'router', c: 'WLAN Name', i: 'fas fa-sitemap fa-fw', t: 'text', v: '', e: true, ne: true, l: 'WLAN Name' },
	{ n: 'providerName', c: 'WLAN Password', i: 'fas fa-server fa-fw', t: 'text', v: '', e: true, ne: true, l: 'WLAN Password' },
	{ n: 'location', c: 'Customer Potential', i: 'fas fa-location-arrow fa-fw', t: 'select', v: level, e: true, ne: true, l: 'Customer Potential' },
	{ n: 'seats', c: 'Number of Seats', i: 'fa fa-bed fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Number of Seats' },
	{ n: 'customerFrequency', c: 'Customer Frequency (daily)', i: 'fa fa-male fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Customer Frequency (daily)' },
	{ n: 'category', c: 'Category', i: 'fas fa-beer fa-fw', t: 'select', v: category, e: true, ne: true, l: 'Category' },
	{ n: 'access', c: 'Delivery Access', i: 'fas fa-blind fa-fw', t: 'select', v: access, e: true, ne: true, l: 'Delivery Access' },
	{ n: 'transport', c: 'Transport (parking)', i: 'fas fa-car fa-fw', t: 'select', v: yesNo, e: true, ne: true, l: 'Transport (parking)' },
	{ n: 'salesCounter', c: 'Sales Counter', i: 'fas fa-list-ol fa-fw', t: 'select', v: yesNo, e: true, ne: true, l: 'Sales Counter' },
	{ n: 'insuranceName', c: 'Insurance Name', i: 'fas fa-life-ring fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Insurance Name' },
	{ n: 'insuranceAmount', c: 'Amount of Inventory Insurance', i: 'fas fa-tag fa-fw', t: 'number', v: 0, e: true, ne: true, l: 'Amount of Inventory Insurance' },
	{ n: 'premisesClass', c: 'Classification of the premises', i: 'fas fa-certificate fa-fw', t: 'select', v: classification, e: true, ne: true, l: 'Classification of the premises' },
	//{ n: 'showUrl', c: 'Show Url', i: 'fas fa-qrcode fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Show Url' },
	//{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true, ne: true, l: 'Trader ID' },
	{ n: 'sagId', c: 'SAG', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true, ne: true, l: 'SAG' },
	{ n: 'isActive', c: 'Status', i: 'fas fa-check fa-fw', t: 'select', v: isActive, e: true, ne: true, l: 'Status' },
	{ n: 'type', c: 'Type', i: 'fas fa-exchange-alt fa-fw', t: 'select', v: type, e: true, ne: true, l: 'Type' },

	{ n: 'speedtest1', c: 'SpeedTest1', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'speedtest2', c: 'SpeedTest2', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'speedtest3', c: 'SpeedTest3', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'localInterior', c: 'Interior', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'localExterior', c: 'Exterior', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'commercialRegister', c: 'Commercial Register', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'idPass1', c: 'ID/Pass 1', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },
	{ n: 'idPass2', c: 'ID/Pass 2', i: 'fas fa-image  fa-fw', t: 'imageFile', v: '', e: true },

	{ n: 'lat', c: 'Latitude', i: 'fas fa-map-marker fa-fw', t: 'double', v: 0.0, e: true, ne: true, l: 'Latitude' },
	{ n: 'long', c: 'Longitude', i: 'fas fa-map-marker fa-fw', t: 'double', v: 0.0, e: true, ne: true, l: 'Longitude' },
]

const levelAll = [{ id: 0, title: "ALL" }, ...level]

const categoryAll = [{ id: 0, title: "ALL" }, ...category]

const accessAll = [{ id: 0, title: "ALL" }, ...access]

const typeAll = [{ id: 0, title: "ALL" }, ...type]


const queryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'location', c: 'Customer  Potential', i: 'fas fa-location-arrow fa-fw', t: 'select', v: levelAll, e: true },
	{ n: 'category', c: 'Category', i: 'fas fa-beer fa-fw', t: 'select', v: categoryAll, e: true },
	{ n: 'access', c: 'Delivery Access', i: 'fas fa-blind fa-fw', t: 'select', v: accessAll, e: true },
	{ n: 'type', c: 'Type', i: 'fas fa-exchange-alt fa-fw', t: 'select', v: typeAll, e: true },
]

const appUrl = process.env.APP_URL
const imgPath = `${appUrl}bfi/`

const caption = {
	navBfi: "active",
	pageTitle: "BFI",
	icon: 'fas fa-building fa-fw',
	route: '/bfi/',
	newAction: 'createBfi',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	query: 'Query',
	queryRoute: '/bfi',
	queryData: 'Data Query',
	queryAction: 'queryTransfer',
	find: "Find",
	imgPath: imgPath,
}

const currentQuery = {
	location: null,
	category: null,
	access: null,
	type: null,
}

class BfiController {

	setting;

	constructor() {
		BusinessService.GetSettings()
			.then((s) => {
				this.setting = s;
			})
			.catch((err) => {
				console.error(err);
			});
	}

	async query({ view, request, auth }) {
		const locationField = request.all().location
		const categoryField = request.all().category
		const accessField = request.all().access
		const typeField = request.all().type || 2
		const isActiveField = request.all().isActive

		currentQuery.location = locationField
		currentQuery.category = categoryField
		currentQuery.access = accessField
		currentQuery.type = typeField
		currentQuery.isActive = isActiveField

		const list = await Record.query().where(
			function () {
				if (locationField && locationField > 0) {
					this.where('location', request.all().location)
				}
				if (categoryField && categoryField > 0) {
					this.where('category', request.all().category)
				}
				if (accessField && accessField > 0) {
					this.where('access', request.all().access)
				}
				if (typeField && typeField > 0) {
					this.where('type', request.all().type)
				}
			}
		).orderBy('id', 'desc').fetch()

		const sagList = await Sag.query().fetch()
		fieldList.find(x => x.n == 'sagId').v = sagList.toJSON().map(s => {
			let id = s.id
			let title = s.businessName
			return { id, title }
		})

		//kullanıcı prospect olan tab ise type disabled olacak
		if (auth.user.role == 3 || auth.user.role == 2) {
			fieldList.find(x => x.n == 'type').e = true
		} else {
			fieldList.find(x => x.n == 'type').e = false
		}

		const rawInstitutes = await Institute.query().orderBy('order').fetch()
		const allInstitutes = rawInstitutes.toJSON()

		const loginInstitutes = allInstitutes.filter(x => x.moneyTransferActive)
		const linkInstitutes = allInstitutes.filter(x => x.accountCreateActive)

		let loginFieldList = []
		allInstitutes.filter(x => x.moneyTransferActive).forEach(i => {
			loginFieldList.push(
				{ n: `login${i.id}`, c: `${i.title} Trader ID`, i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true, ne: true, l: `${i.title} Trader ID` }
			)
		})

		let linkFieldList = []
		allInstitutes.filter(x => x.accountCreateActive).forEach(i => {
			linkFieldList.push(
				{ n: `link${i.id}`, c: `${i.title} Link`, i: 'fas fa-qrcode fa-fw', t: 'text', v: '', e: true, ne: true, l: `${i.title} Link` },
			)
		})

		const bfiLogins = await BfiLogin.query().join('financeinstitute', (query) => {
			query
				.on((subquery) => {
					subquery
						.on('financeinstitute.id', '=', 'bfilogin.instituteId')
				})
		})
			.select('bfilogin.*')
			.select('financeinstitute.title')
			.fetch();

		const bfiLoginList = bfiLogins.toJSON().map(l => {
			let id = l.id
			let bfiId = l.bfiId
			let instituteId = l.instituteId
			let login = l.login
			let title = l.title
			return { id, bfiId, instituteId, title, login }
		})

		const bfiLinks = await BfiLink.query().join('financeinstitute', (query) => {
			query
				.on((subquery) => {
					subquery
						.on('financeinstitute.id', '=', 'bfilink.instituteId')
				})
		})
			.select('bfilink.*')
			.select('financeinstitute.title')
			.fetch();

		const bfiLinkList = bfiLinks.toJSON().map(l => {
			let id = l.id
			let bfiId = l.bfiId
			let instituteId = l.instituteId
			let link = decodeURIComponent(l.link)
			let title = l.title
			return { id, bfiId, instituteId, title, link }
		})

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		let dataList = list.toJSON()
		dataList.forEach(element => {
			element.loginList = bfiLoginList.filter(x => x.bfiId == element.id)
			element.linkList = bfiLinkList.filter(x => x.bfiId == element.id)
		});

		return view.render("pages.bfi", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: dataList,
			setting: this.setting,
			bfiCategories: category,
			loginFields: loginFieldList,
			linkFields: linkFieldList,
			loginInstitutes: loginInstitutes,
			linkInstitutes: linkInstitutes,
		})
	}

	async update({ request, response, session, params }) {
		try {
			const bfiId = parseInt(params.id)
			let record = await Record.find(bfiId)

			record.businessName = request.all().businessName
			record.companyName = request.all().companyName
			record.street = request.all().street
			record.zip = request.all().zip
			record.city = request.all().city
			record.contactName = request.all().contactName
			record.contactPhone = request.all().contactPhone
			record.contactEmail = request.all().contactEmail
			//record.speedUpload = parseInt(request.all().speedUpload)
			record.speedDownload = parseInt(request.all().speedDownload)
			record.distanceRouter = parseInt(request.all().distanceRouter)
			record.socket = parseInt(request.all().socket)
			record.router = request.all().router
			record.providerName = request.all().providerName
			record.location = request.all().location
			record.seats = parseInt(request.all().seats)
			record.customerFrequency = parseInt(request.all().customerFrequency)
			record.category = request.all().category
			record.access = request.all().access
			record.transport = request.all().transport
			record.salesCounter = request.all().salesCounter
			record.insuranceName = request.all().insuranceName
			record.insuranceAmount = parseInt(request.all().insuranceAmount)
			record.premisesClass = request.all().premisesClass
			record.isActive = request.all().isActive
			record.showUrl = request.all().showUrl || ""
			//record.login = request.all().login || 0
			record.sagId = parseInt(request.all().sagId)

			record.lat = request.all().lat
			record.long = request.all().long

			if (request.all().type) {
				record.type = parseInt(request.all().type)
			}

			await record.save()

			//bağlı olan terminallerdeki verileri güncelle
			const rawTerminals = await Terminal.query().where('bId', bfiId).orderBy('id', 'desc').fetch()
			const terminals = rawTerminals.toJSON()
			for (const item of terminals) {
				const terminal = await Terminal.find(item.id)
				terminal.showUrl = request.all().showUrl || ""
				await terminal.save()
			}

			const rawInstitutes = await Institute.query().orderBy('order').fetch()
			const allInstitutes = rawInstitutes.toJSON()

			//bağlı olan bfiLogin tablosunu güncelleme (varsa update yoksa create)
			const loginInstitutes = allInstitutes.filter(x => x.moneyTransferActive)
			for (const institute of loginInstitutes) {
				const bfiLogin = await BfiLogin.query().where(_ => {
					_.where('bfiId', bfiId)
					_.where('instituteId', institute.id)
				}).fetch();

				const newValue = request.input(`login${institute.id}`) || 0
				if (bfiLogin.rows.length > 0) {
					for (const row of bfiLogin.rows) {
						row.login = newValue
						await row.save()
					}
				} else {
					await BfiLogin.create({
						bfiId: bfiId,
						instituteId: institute.id,
						login: newValue,
					})
				}
			}

			//bağlı olan bfiLink tablosunu güncelleme (varsa update yoksa create)
			const linkInstitutes = allInstitutes.filter(x => x.accountCreateActive)
			for (const institute of linkInstitutes) {
				const bfiLink = await BfiLink.query().where(_ => {
					_.where('bfiId', bfiId)
					_.where('instituteId', institute.id)
				}).fetch();

				const newValue = (request.input(`link${institute.id}`)).replace('&amp;', '&').replace('amp;', '') || ""
				if (bfiLink.rows.length > 0) {
					for (const row of bfiLink.rows) {
						row.link = newValue
						await row.save()
					}
				} else {
					await BfiLink.create({
						bfiId: bfiId,
						instituteId: institute.id,
						link: newValue,
					})
				}
			}

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async create({ request, response, session }) {
		try {
			await Record.create({
				businessName: request.input("businessName"),
				companyName: request.input("companyName") || '',
				street: request.input("street"),
				zip: request.input("zip"),
				city: request.input("city"),
				contactName: request.input("contactName"),
				contactPhone: request.input("contactPhone"),
				contactEmail: request.input("contactEmail"),
				//speedUpload: request.input("speedUpload"),
				speedDownload: request.input("speedDownload"),
				distanceRouter: request.input("distanceRouter"),
				socket: request.input("socket"),
				router: request.input("router"),
				providerName: request.input("providerName"),
				location: request.input("location"),
				seats: request.input("seats"),
				customerFrequency: request.input("customerFrequency"),
				category: request.input("category"),
				access: request.input("access"),
				transport: request.input("transport"),
				salesCounter: request.input("salesCounter"),
				insuranceName: request.input("insuranceName"),
				insuranceAmount: request.input("insuranceAmount"),
				premisesClass: request.input("premisesClass"),
				showUrl: request.input("showUrl") || '',
				//login: request.all().login || 0,
				sagId: request.all().sagId,
				type: 2
			})

			const rawInstitutes = await Institute.query().orderBy('order').fetch()
			const allInstitutes = rawInstitutes.toJSON()

			//bağlı bfiLogin tablosuna aktif institute kadar kayıt atılacak
			const loginInstitutes = allInstitutes.filter(x => x.moneyTransferActive)
			for (const institute of loginInstitutes) {
				await BfiLogin.create({
					bfiId: Record.id,
					instituteId: institute.id,
					login: request.input(`login${institute.id}`) || 0
				})
			}

			//bağlı bfiLink tablosuna aktif institute kadar kayıt atılacak
			const linkInstitutes = allInstitutes.filter(x => x.accountCreateActive)
			for (const institute of linkInstitutes) {
				await BfiLink.create({
					bfiId: Record.id,
					instituteId: institute.id,
					link: request.input(`link${institute.id}`) || ""
				})
			}

			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async fileUpload({ view, params, request, response, session }) {
		const bfiId = request.all().id
		const fieldName = request.all().field

		const checkingBfi = await Record.find(bfiId);

		const requestFile = request.file(`fileUploadFile`)

		try {
			if (checkingBfi && requestFile) {

				// Dosyanın kaydedilmesi ${Date.now()}
				const uploadPath = `public/bfi/${bfiId}`

				const uploadFileName = `${requestFile.clientName}`;
				requestFile.clientName = uploadFileName
				await requestFile.move(uploadPath, { overwrite: true });

				//resim isimlerini BFI'a ye yaz
				checkingBfi[fieldName] = `${bfiId}/${uploadFileName}`
				await checkingBfi.save()

				session.flash({ info: caption.updatedMessage })
				return response.route("back");
			} else {
				return response.status(404).send('Not found')
			}
		} catch (error) {
			return response.status(500).send(error)
		}
	}
}

module.exports = BfiController
