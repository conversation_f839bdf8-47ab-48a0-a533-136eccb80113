'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const Common = use("App/Helpers/Common");
const Record = use("App/Models/User")

const fieldList = [
	{ n: 'username', c: 'Username', i: 'fas fa-user', t: 'text', v: '', e: true },
	{ n: 'email', c: 'Email', i: 'fas fa-at', t: 'email', v: '', e: true },
	{ n: 'role', c: 'Role', i: 'fas fa-suitcase', t: 'select', v: Common.Basic.userRoles, e: true },
	{ n: 'password', c: 'Password', i: 'fas fa-key', t: 'password', v: '', e: true },
]

const caption = {
	navUser: "active",
	pageTitle: "Users",
	icon: 'fas fa-money-check-alt fa-fw',
	route: '/users/',
	newAction: 'createUser',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	selectRole: "Select Role",
}

class UserController {

	async index({ view, auth }) {
		const setting = await BusinessService.GetSettings();

		if (auth.user.role != 3) {
			//roles.remove({ id: 3, title: "Admin" })
		}

		let list = await Record.query().orderBy('id').fetch()
		if (auth.user.role == 2) {
			list = await Record.query().whereIn('role', [1, 2, 4]).orderBy('id').fetch()
		}

		return view.render("pages.user", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting,
			roles: Common.Basic.userRoles
		})
	}

	async create({ request, response, session, auth }) {
		try {
			await Record.create({
				username: request.input("username"),
				email: request.input("email"),
				role: request.input("role"),
				password: request.input("password"),
			})
			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params, auth }) {
		try {
			const record = await Record.find(params.id)
			record.username = request.all().title
			record.email = request.all().email
			record.role = request.all().role
			record.password = request.all().password
			await record.save()

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params, auth }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = UserController
