'use strict'

const BusinessService = use("App/Helpers/BusinessService");

const Record = use("App/Models/Setting")

const modes = [
	{ id: 1, title: 'Test Mode' },
	{ id: 2, title: 'Real Mode' },
]

const isKycActive = [
	{ id: 0, title: "KYC Passive" },
	{ id: 1, title: "KYC Active" },
]

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'sms_url', c: 'Sms Url', i: 'fas fa-link fa-fw', t: 'url', v: '', e: true },
	{ n: 'sms_user', c: 'Sms User', i: 'fas fa-user fa-fw', t: 'text', v: '', e: true },
	{ n: 'sms_pass', c: 'Sms Password', i: 'fas fa-key fa-fw', t: 'text', v: '', e: true },
	{ n: 'sms_originator', c: 'Sms Originator', i: 'fas fa-tag fa-fw', t: 'text', v: '', e: true },
	{ n: 'sms_originator_alternate', c: 'Sms Originator Alternate', i: 'fas fa-tag fa-fw', t: 'text', v: '', e: true },
	{ n: 'faq_url', c: 'FAQ Page Url', i: 'fab fa-safari fa-fw', t: 'url', v: '', e: true },
	{ n: 'ftp_host', c: 'Ftp Host', i: 'fas fa-server fa-fw', t: 'text', v: '', e: true },
	{ n: 'ftp_port', c: 'Ftp Port', i: 'fab fa-hubspot fa-fw', t: 'number', v: '', e: true },
	{ n: 'ftp_user', c: 'Ftp User', i: 'fas fa-user-shield fa-fw', t: 'text', v: '', e: true },
	{ n: 'ftp_pass', c: 'Ftp Password', i: 'fas fa-key fa-fw', t: 'text', v: '', e: true },
	{ n: 'ftp_working_dir', c: 'Ftp Working Directory', i: 'fas fa-folder fa-fw', t: 'text', v: '', e: true },
	{ n: 'company_title', c: 'Company Title', i: 'fas fa-building fa-fw', t: 'text', v: '', e: true },
	{ n: 'company_phone', c: 'Company Phone', i: 'fas fa-phone fa-fw', t: 'text', v: '', e: true },
	{ n: 'company_address', c: 'Company Address', i: 'fas fa-map fa-fw', t: 'text', v: '', e: true },
	{ n: 'company_mail', c: 'Company Email', i: 'fas fa-envelope fa-fw', t: 'email', v: '', e: true },

	{ n: 'money_wait_time', c: 'Money Waiting Time(min)', i: 'fas fa-stopwatch fa-fw', t: 'number', v: '', e: true, l: 'Money Wait' },
	{ n: 'general_wait_time', c: 'General Waiting Time(min)', i: 'fas fa-history fa-fw', t: 'number', v: '', e: true, l: 'General Wait' },
	//{ n: 'payoutMinAmount', c: 'Max Payout Amount', i: 'fas fa-money-bill fa-fw', t: 'number', v: '0', e: true, l: 'Max Payout Amount' },
	//{ n: 'payoutMinChannel1', c: 'Maximum quantity of CHF 10 banknotes', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Max 10 CHF' },
	//{ n: 'payoutMinChannel2', c: 'Maximum quantity of CHF 20 banknotes', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Max 20 CHF' },
	//{ n: 'payoutMinChannel3', c: 'Maximum quantity of CHF 50 banknotes', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Max 50 CHF' },
	//{ n: 'payoutMinChannel4', c: 'Maximum quantity of CHF 100 banknotes', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Max 100 CHF' },
	//{ n: 'payoutMinChannel5', c: 'Maximum quantity of CHF 200 banknotes', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Max 200 CHF' },

	{ n: 'screenSaver_wait_time', c: 'ScreenSaver Waiting Time(min)', i: 'fas fa-stopwatch fa-fw', t: 'number', v: '', e: true, l: 'ScreenSaver Minutes' },

	{ n: 'terminalDepositLimit', c: 'Terminal Deposit Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Terminal Deposit Limit' },
	{ n: 'terminalWithdrawalLimit', c: 'Terminal Withdrawal Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Terminal Withdrawal Limit' },
	{ n: 'customerDepositLimit', c: 'Customer Deposit Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Customer Deposit Limit' },
	{ n: 'customerWithdrawalLimit', c: 'Customer Withdrawal Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Customer Withdrawal Limit' },

	{ n: 'invitationMoney', c: 'Bulls per Account', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Bulls per Account' },
	{ n: 'invitationMoneyFive', c: 'Bulls for 5 Accounts', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Bulls for 5 Accounts' },
	{ n: 'invitationMoneyFifty', c: 'Bulls for 50 Accounts', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Bulls for 50 Accounts' },
	{ n: 'test_mode', c: 'Working Mode', i: 'fas fa-vial fa-fw', t: 'select', v: modes, e: true, l: 'System Mode' },
	{ n: 'isKycActive', c: 'KYC Status', i: 'fas fa-check', t: 'select', v: isKycActive, e: true, l: 'KYC Status' },
	{ n: 'version', c: 'TradeCenter.exe Version', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true, l: 'App Version' },
	{ n: 'updateVersion', c: 'Update.exe Version', i: 'fas fa-file-download fa-fw', t: 'text', v: '', e: true, l: 'Update Version' },
]

const caption = {
	navSetting: "active",
	pageTitle: "Settings",
	route: '/settings/',
	header: 'Change Settings',
	close: "Close",
	save: "Save",
	updatedMessage: "It was updated successfuly",
}

class SettingController {

	async index({ view }) {

		const setting = await BusinessService.GetSettings()

		const list = await Record.query().fetch()

		return view.render("pages.setting", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting
		})
	}

	async update({ request, response, session, params, auth }) {
		if (auth.user.role == 3) {
			try {
				const record = await Record.find(params.id)
				record.sms_url = request.all().sms_url
				record.sms_user = request.all().sms_user
				record.sms_pass = request.all().sms_pass
				record.sms_originator = request.all().sms_originator
				//record.sms_originator_alternate = request.all().sms_originator_alternate
				record.faq_url = request.all().faq_url
				record.money_wait_time = request.all().money_wait_time
				record.general_wait_time = request.all().general_wait_time
				record.ftp_host = request.all().ftp_host
				record.ftp_port = request.all().ftp_port
				record.ftp_user = request.all().ftp_user
				record.ftp_pass = request.all().ftp_pass
				record.ftp_working_dir = request.all().ftp_working_dir
				record.company_title = request.all().company_title
				record.company_phone = request.all().company_phone
				record.company_address = request.all().company_address
				record.company_mail = request.all().company_mail
				//record.management_pass = request.all().management_pass
				record.test_mode = request.all().test_mode
				record.version = request.all().version
				record.updateVersion = request.all().updateVersion

				//record.payoutMinAmount = request.all().payoutMinAmount
				//record.payoutMinChannel1 = request.all().payoutMinChannel1
				//record.payoutMinChannel2 = request.all().payoutMinChannel2
				//record.payoutMinChannel3 = request.all().payoutMinChannel3
				//record.payoutMinChannel4 = request.all().payoutMinChannel4
				//record.payoutMinChannel5 = request.all().payoutMinChannel5

				record.screenSaver_wait_time = parseInt(request.all().screenSaver_wait_time)
				record.terminalDepositLimit = parseInt(request.all().terminalDepositLimit)
				record.terminalWithdrawalLimit = parseInt(request.all().terminalWithdrawalLimit)
				record.customerDepositLimit = parseInt(request.all().customerDepositLimit)
				record.customerWithdrawalLimit = parseInt(request.all().customerWithdrawalLimit)

				record.invitationMoney = request.all().invitationMoney
				record.invitationMoneyFive = request.all().invitationMoneyFive
				record.invitationMoneyFifty = request.all().invitationMoneyFifty
				record.isKycActive = request.all().isKycActive

				await record.save()

				session.flash({ info: caption.updatedMessage })
				return response.route("back")
			} catch (error) {
				session.flash({ error: error.message })
				return response.route("home")
			}
		} else {
			return response.route("home")
		}

	}

}

module.exports = SettingController
