'use strict'

const Common = use("App/Helpers/Common");

const Setting = use("App/Models/Setting");
const auth = require("@adonisjs/auth")

const User = use("App/Models/User")

const fieldList = [
    { n: 'username', c: 'Username', i: 'fas fa-user', t: 'text', v: '', e: true },
    { n: 'email', c: 'Email', i: 'fas fa-at', t: 'email', v: '', e: true },
    { n: 'role', c: 'Role', i: 'fas fa-suitcase', t: 'select', v: Common.Basic.userRoles, e: true },
    { n: 'password', c: 'Password', i: 'fas fa-key', t: 'password', v: '', e: true },
]

const caption = {
    navUser: "active",
    pageTitle: "Register",
    title: "Create New User",
    username: "Enter Username",
    email: "Enter Email",
    password: "Enter Password",
    passwordR: "Confirm Password",
    role: "Select Role",
    add: "Add",
}

class RegisterController {

    async index({ view, params }) {
        const setting = await Setting.find(1);

        return view.render("pages.register", {
            caption: caption,
            fields: fieldList,
            data: null,
            setting: setting,
            roles: Common.Basic.userRoles
        })
    }

    async create({ request, response, session, view, params, auth }) {

        if (auth.user.role == 3) {
            try {
                const user = await User.create({
                    username: request.input("username"),
                    email: request.input("email"),
                    password: request.input("pass"),
                    role: parseInt(request.all().role),
                })

                session.flash({ info: "New User is created." })
                return response.route("getUsers")
            } catch (error) {
                session.flash({ error: error.message })
                return response.route("home")
            }
        }
        else {
            return response.route("home")
        }
    }

}

module.exports = RegisterController
