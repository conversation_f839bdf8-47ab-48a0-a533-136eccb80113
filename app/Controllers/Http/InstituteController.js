'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const Record = use("App/Models/Institute")

const Helpers = use("Helpers")

const modes = [
	{ id: 0, title: 'Normal Register' },
	{ id: 1, title: 'Online Register' },
]

const fieldList = [
	{ n: 'title', c: 'Title', i: 'fas fa-tag', t: 'text', v: '', e: true },
	{ n: 'icon', c: 'Image', i: 'fas fa-image', t: 'file', v: '', e: true },
	{ n: 'account_opening_site', c: 'Site', i: 'fas fa-globe-europe', t: 'text', v: '', e: true },
	{ n: 'account_opening_duration', c: 'O.Duration', i: 'fas fa-clock', t: 'text', v: '', e: true },
	{ n: 'max_leverage_factor', c: 'Leverage', i: 'fas fa-percent', t: 'text', v: '', e: true },
	{ n: 'markup', c: 'Markup', i: 'fas fa-money-bill', t: 'text', v: '', e: true },
	{ n: 'payment_transactions', c: 'Payment Transaction', i: 'fas fa-credit-card', t: 'text', v: '', e: true },
	{ n: 'correspondence_language', c: 'Lang', i: 'fas fa-flag', t: 'text', v: '', e: true },
	{ n: 'competition_participation', c: 'CP', i: 'fas fa-award', t: 'text', v: '', e: true },
	{ n: 'commercial_products', c: 'Products', i: 'fas fa-shopping-cart', t: 'text', v: '', e: true },
	{ n: 'email', c: 'Email', i: 'fas fa-at', t: 'email', v: '', e: true },
	{ n: 'page_link', c: 'Page Link', i: 'fas fa-file-code', t: 'url', v: '', e: true },
	{ n: 'signup_finish_link', c: 'Signup Link', i: 'fas fa-link', t: 'url', v: '', e: true },
	{ n: 'server_host', c: 'Server Host', i: 'fas fa-server', t: 'text', v: '', e: true },
	{ n: 'server_port', c: 'Server Port', i: 'fab fa-hubspot', t: 'number', v: '', e: true },
	{ n: 'server_user', c: 'Server User', i: 'fas fa-user-shield', t: 'text', v: '', e: true },
	{ n: 'server_pass', c: 'Server Password', i: 'fas fa-key', t: 'text', v: '', e: true },
	{ n: 'server_db', c: 'Server DB', i: 'fas fa-database', t: 'text', v: '', e: true },
	{ n: 'server_table', c: 'Server Table', i: 'fas fa-dice-d6', t: 'text', v: '', e: true },
	{ n: 'loginField', c: 'Login Field', i: 'fas fa-sign-in-alt', t: 'text', v: '', e: true },
	{ n: 'emailField', c: 'Mail Field', i: 'fas fa-at', t: 'text', v: '', e: true },
	{ n: 'fillFormTemplate', c: 'Form Template', i: 'fab fa-wpforms', t: 'text', v: '', e: true },
	{ n: 'onlineRegister', c: 'Online Register', i: 'fas fa-id-card fa-fw', t: 'select', v: modes, e: true },
	{ n: 'order', c: 'Order', i: 'fas fa-sort-numeric-up', t: 'number', v: '', e: true },
	{ n: 'kycMail', c: 'KYC Email', i: 'fas fa-at', t: 'email', v: '', e: true },
]

const caption = {
	navInstitute: "active",
	pageTitle: "Finance Institutes",
	icon: 'fas fa-university fa-fw',
	route: '/institutes/',
	newAction: 'createInstitute',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	selectFile: "Select File",
}

class InstituteController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();
		const list = await Record.query().orderBy('id').fetch()
		return view.render("pages.institute", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting,
		})
	}

	async create({ request, response, session }) {
		try {
			await Record.create({
				title: request.input("title"),
				icon: request.input("icon"),
				account_opening_site: request.input("account_opening_site"),
				account_opening_duration: request.input("account_opening_duration"),
				max_leverage_factor: request.input("max_leverage_factor"),
				markup: request.input("markup"),
				payment_transactions: request.input("payment_transactions"),
				correspondence_language: request.input("correspondence_language"),
				competition_participation: request.input("competition_participation"),
				commercial_products: request.input("commercial_products"),
				email: request.input("email"),
				link: request.input("page_link"),
				signup_finish_link: request.input("signup_finish_link"),
				server_host: request.input("server_host"),
				server_port: request.input("server_port"),
				server_user: request.input("server_user"),
				server_pass: request.input("server_pass"),
				server_db: request.input("server_db"),
				server_table: request.input("server_table"),
				loginField: request.input("loginField"),
				emailField: request.input("emailField"),
				fillFormTemplate: request.input("fillFormTemplate"),
				active: request.input("active"),
				onlineRegister: request.input("onlineRegister"),
				kycMail: request.input("kycMail"),
			})
			const image = request.file("iconFile")
			if (image != null) { await image.move(Helpers.publicPath('FinancialLogos'), { overwrite: true }) }//resmi upload etme
			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params }) {
		try {
			const record = await Record.find(params.id)
			record.title = request.all().title
			record.icon = request.all().icon
			record.account_opening_site = request.all().account_opening_site
			record.account_opening_duration = request.all().account_opening_duration
			record.max_leverage_factor = request.all().max_leverage_factor
			record.markup = request.all().markup
			record.payment_transactions = request.all().payment_transactions
			record.correspondence_language = request.all().correspondence_language
			record.competition_participation = request.all().competition_participation
			record.commercial_products = request.all().commercial_products
			record.email = request.all().email
			record.link = request.all().page_link
			record.signup_finish_link = request.all().signup_finish_link
			record.server_host = request.all().server_host
			record.server_port = request.all().server_port
			record.server_user = request.all().server_user
			record.server_pass = request.all().server_pass
			record.server_db = request.all().server_db
			record.server_table = request.all().server_table
			record.loginField = request.all().loginField
			record.emailField = request.all().emailField
			record.fillFormTemplate = request.all().fillFormTemplate
			record.active = request.all().active
			record.onlineRegister = request.all().onlineRegister
			record.order = request.all().order
			record.kycMail = request.all().kycMail

			await record.save()
			const image = request.file("iconFile")
			if (image != null) { await image.move(Helpers.publicPath('FinancialLogos'), { overwrite: true }) }//resmi upload etme
			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = InstituteController
