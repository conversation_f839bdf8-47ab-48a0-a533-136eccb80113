'use strict'

const Common = use('App/Helpers/Common');

const BusinessService = use("App/Helpers/BusinessService");
const TransactionService = use("App/Helpers/TransactionService");

const Database = use("Database")
const Record = use("App/Models/Transfer")
const Institutes = use("App/Models/Institute")
const Bfis = use("App/Models/Bfi")
const Terminals = use("App/Models/Terminal")
const SystemLog = use("App/Models/SystemLog")
const Customer = use("App/Models/Customer")

const moment = require("moment");
moment.defaultFormat = "DD.MM.YYYY HH:mm";
let dateTimeNow = moment().format('YYYY-MM-DD HH:mm:ss')
let dateToday = moment().format('YYYY-MM-DD')

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: false, ne: true },
	{ n: 'name', c: 'Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'surname', c: 'Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false, ne: false },
	{ n: 'financeInstitute', c: 'Institute', i: 'fas fa-money-check-alt fa-fw', t: 'select', v: [], e: true, ne: true },
	{ n: 'amount', c: 'Amount', i: 'fas fa-money-bill-alt fa-fw', t: 'number', v: 0, e: true, ne: true },
	{ n: 'currencyType', c: 'Currency', i: 'fas fa-tag fa-fw', t: 'text', v: '', e: false, ne: true },
	{ n: 'bId', c: 'BFI', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true, ne: true },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'created_date', c: 'Date', i: 'fas fa-tag fa-fw', t: 'dateTime', v: dateTimeNow, e: true, ne: false },
	{ n: 'commentType', c: 'Comment Type', i: 'fas fa-comment fa-fw', t: 'select', v: Common.Basic.commentTypes, e: true, ne: true },
	{ n: 'comment', c: 'Comment', i: 'fas fa-comment fa-fw', t: 'text', v: '', e: true, ne: true },
	{ n: 'bankApi', c: 'Bank API', i: 'fas fa-university fa-fw', t: 'select', v: Common.Basic.yesNo, e: false, ne: true },
]

const queryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true },
	{ n: 'financeInstitute', c: 'Institute', i: 'fas fa-money-check-alt fa-fw', t: 'select', v: [], e: true },
	{ n: 'amount', c: 'Amount', i: 'fas fa-money-bill-alt fa-fw', t: 'number', v: 0, e: true },
	{ n: 'bId', c: 'BFI', i: 'fas fa-building fa-fw', t: 'select', v: [], e: true },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true },
	{ n: 'beginDate', c: 'Begin', i: 'fas fa-calendar fa-fw', t: 'date', v: dateToday, e: true },
	{ n: 'endDate', c: 'End', i: 'fas fa-calendar fa-fw', t: 'date', v: dateToday, e: true },
	{ n: 'commentType', c: 'Comment Type', i: 'fas fa-comment fa-fw', t: 'select', v: Common.Basic.commentTypes, e: true },
]
queryFieldList[7].v.splice(0, 0, { id: 0, title: "ALL" })

const caption = {
	navTransfer: "active",
	pageTitle: "Transactions",
	icon: 'fas fa-hand-holding-usd fa-fw',
	route: '/transfers/',
	newAction: 'createTransfer',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	query: 'Query',
	queryRoute: '/transfers',
	queryData: 'Data Query',
	queryAction: 'queryTransfer',
	close: "Close",
	find: "Find",
	detailData: 'Details',
	detailInfo: 'Money: ',
}

const currentQuery = {
	tradeId: null,
	amount: null,
	end: null,
	begin: null,
	terminalCode: null,
	financeInstitute: null,
	bId: null,
	commentType: null,
}

const summary = {
	totalAmount: 0,
	stockAmount: 0,
	cashboxAmount: 0,
	moneyType: "CHF",
}

class TransferController {

	async index({ view, session, params }) {
		const setting = await BusinessService.GetSettings();

		const logId = params.logId
		let deposit = undefined
		if (logId) {
			const systemLog = await SystemLog.find(parseInt(logId));
			const smsLog = await SystemLog.find(parseInt(logId) + 1); //@692744 got the SMS Code: 3216 (to deposit 20 CHF)
			const depositLogin = smsLog.log.split(' ')[0].replace('@', '')
			const depositAmount = smsLog.log.split(' ')[8]
			const depositPerson = await Customer.findBy('login', parseInt(depositLogin));
			const depositTerminal = await Terminals.findBy('terminalCode', systemLog.terminalCode);

			deposit = {
				login: parseInt(depositLogin),
				amount: parseInt(depositAmount),
				terminalCode: systemLog.terminalCode,
				financeInstitute: depositPerson.financeinstitute,
				bId: depositTerminal.bId,
				commentType: 1,
				comment: 'Manuel Deposit from ' + systemLog.terminalCode + ' LOG: ' + logId,
			}
		}

		let today = new Date()
		today.setDate(today.getDate() + 1)
		let end = moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 30)
		let begin = moment(today).format('YYYY-MM-DD')

		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = null
		currentQuery.amount = null
		currentQuery.terminalCode = null
		currentQuery.financeInstitute = null
		currentQuery.bId = null
		currentQuery.commentType = null

		let transferData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.whereNotIn('person.login', (query) => query.from('stocks').select('stocks.login'))
			.andWhereBetween('transfer.created_date', [begin, end])
			.orderBy('transfer.created_date', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();

		transferData = transferData.toJSON()
		summary.totalAmount = 0
		transferData.forEach(element => {
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: transferData,
			setting: setting,
			summary: summary,
			terminal: undefined,
			deposit: deposit,
		})
	}

	async query({ view, request }) {
		const setting = await BusinessService.GetSettings();

		let today = new Date()
		let tradeId = request.all().login
		let amount = request.all().amount
		today.setDate(today.getDate() + 1)
		let end = request.all().endDate || moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 8)
		let begin = request.all().beginDate || moment(today).format('YYYY-MM-DD')
		let terminalCode = request.all().terminalCode
		let financeInstitute = request.all().financeInstitute
		let bId = request.all().bId
		let commentType = request.all().commentType

		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = tradeId
		currentQuery.amount = amount
		currentQuery.terminalCode = terminalCode
		currentQuery.financeInstitute = financeInstitute
		currentQuery.bId = bId
		currentQuery.commentType = commentType

		let transferData = await Database //Database.schema.raw('SELECT * FROM posts')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.from('transfer')
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.whereNotIn('person.login', (query) => query.from('stocks').select('stocks.login'))
			.andWhere(function () {
				this.whereBetween('transfer.created_date', [begin, end])
				if (tradeId) {
					this.andWhere('transfer.login', tradeId)
				}
				if (amount) {
					this.andWhere('transfer.amount', '=', amount)
				}
				if (terminalCode) {
					this.andWhere('transfer.terminalCode', terminalCode)
				}
				if (financeInstitute && financeInstitute != 0) {
					this.andWhere('transfer.financeInstitute', financeInstitute)
				}
				if (bId > 0) {
					this.andWhere('transfer.bId', bId)
				}
				if (commentType && commentType != 0) {
					this.andWhere('transfer.commentType', commentType)
				}
			})
			.orderBy('transfer.created_date', 'desc')

		summary.totalAmount = 0
		transferData.forEach(element => {
			element.created_date = moment(element.created_date).format()
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		//queryFieldList[5].v = new Date()
		//queryFieldList[6].v = new Date()

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: transferData,
			setting: setting,
			summary: summary,
			terminal: undefined,
		})
	}

	async queryByLogin({ view, params }) {
		const setting = await BusinessService.GetSettings();

		currentQuery.begin = moment(new Date("2020-01-01")).format('YYYY-MM-DD')
		currentQuery.end = moment(new Date()).format('YYYY-MM-DD')
		currentQuery.tradeId = params.login
		currentQuery.amount = null
		currentQuery.terminalCode = null
		currentQuery.financeInstitute = null
		currentQuery.bId = null
		currentQuery.commentType = null

		let transferData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where('transfer.login', '=', params.login)
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();

		transferData = transferData.toJSON()
		summary.totalAmount = 0
		transferData.forEach(element => {
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: transferData,
			stockData: [],
			setting: setting,
			summary: summary,
			terminal: undefined,
		})
	}

	async queryByTerminal({ view, params }) {
		const setting = await BusinessService.GetSettings();

		currentQuery.begin = moment(new Date("2020-01-01")).format('YYYY-MM-DD')
		currentQuery.end = moment(new Date()).format('YYYY-MM-DD')
		currentQuery.tradeId = null
		currentQuery.amount = null
		currentQuery.terminalCode = params.terminalCode
		currentQuery.financeInstitute = null
		currentQuery.bId = null
		currentQuery.commentType = null

		const terminalQuery = await Terminals.query().where('terminalCode', params.terminalCode).fetch();
		let terminal = terminalQuery.toJSON()[0];

		let transferData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereNotIn('person.login', (query) => query.from('stocks').select('stocks.login'))
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		transferData = transferData.toJSON()

		summary.totalAmount = 0
		transferData.forEach(element => {
			summary.totalAmount += element.amount
		});

		let stockData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 1).select('stocks.login'))
				//this.where('transfer.amount', '>', 0)
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		stockData = stockData.toJSON()

		summary.stockAmount = 0
		stockData.forEach(element => {
			summary.stockAmount += element.amount
		});

		let cashboxData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 3).select('stocks.login'))
				//this.where('transfer.amount', '<', 0)
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		cashboxData = cashboxData.toJSON()

		summary.cashboxAmount = 0
		cashboxData.forEach(element => {
			summary.cashboxAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			setting: setting,
			data: transferData,
			stockData: stockData,
			cashboxData: cashboxData,
			summary: summary,
			terminal: terminal,
		})
	}

	async queryByBfi({ view, params }) {
		const setting = await BusinessService.GetSettings();

		currentQuery.begin = moment(new Date("2020-01-01")).format('YYYY-MM-DD')
		currentQuery.end = moment(new Date()).format('YYYY-MM-DD')
		currentQuery.tradeId = null
		currentQuery.amount = null
		currentQuery.terminalCode = null
		currentQuery.financeInstitute = null
		currentQuery.bId = params.bId
		currentQuery.commentType = null

		let transferData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where('transfer.bId', '=', params.bId)
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();

		transferData = transferData.toJSON()

		summary.totalAmount = 0
		transferData.forEach(element => {
			summary.totalAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: transferData,
			setting: setting,
			stockData: [],
			summary: summary,
			terminal: undefined,
		})
	}

	//duzenlenecek 
	async queryBySag({ view, params }) {
		const setting = await BusinessService.GetSettings();

		currentQuery.begin = moment(new Date("2020-01-01")).format('YYYY-MM-DD')
		currentQuery.end = moment(new Date()).format('YYYY-MM-DD')
		currentQuery.tradeId = null
		currentQuery.amount = null
		currentQuery.terminalCode = params.terminalCode
		currentQuery.financeInstitute = null
		currentQuery.bId = null
		currentQuery.commentType = null

		const terminalQuery = await Terminals.query().where('terminalCode', params.terminalCode).fetch();
		let terminal = terminalQuery.toJSON()[0];

		let transferData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereNotIn('person.login', (query) => query.from('stocks').select('stocks.login'))
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		transferData = transferData.toJSON()

		summary.totalAmount = 0
		transferData.forEach(element => {
			summary.totalAmount += element.amount
		});

		let stockData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 1).select('stocks.login'))
				//this.where('transfer.amount', '>', 0)
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		stockData = stockData.toJSON()

		summary.stockAmount = 0
		stockData.forEach(element => {
			summary.stockAmount += element.amount
		});

		let cashboxData = await Record.query()
			.leftJoin('person', 'person.login', 'transfer.login')
			.leftJoin('terminal', 'terminal.terminalCode', 'transfer.terminalCode')
			.leftJoin('bfi', 'bfi.Id', 'transfer.bId')
			.where(function () {
				this.where('transfer.terminalCode', '=', params.terminalCode)
				this.whereIn('person.login', (query) => query.from('stocks').where('stocks.accountType', 3).select('stocks.login'))
				//this.where('transfer.amount', '<', 0)
			})
			.orderBy('transfer.id', 'desc')
			.select('transfer.*')
			.select('person.name', 'person.surname')
			.select('bfi.businessName')
			.fetch();
		cashboxData = cashboxData.toJSON()

		summary.cashboxAmount = 0
		cashboxData.forEach(element => {
			summary.cashboxAmount += element.amount
		});

		const instituteList = await Institutes.query().fetch()
		fieldList[3].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		fieldList[7].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[4].v.splice(0, 0, { id: 0, title: "ALL" })

		const bfiList = await Bfis.query().fetch()
		fieldList[6].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v = bfiList.toJSON().map(bfi => {
			let id = bfi.id;
			let title = bfi.businessName;
			return { id, title }
		})
		queryFieldList[3].v.splice(0, 0, { id: 0, title: "ALL" })

		return view.render("pages.transfer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: transferData,
			setting: setting,
			stockData: stockData,
			cashboxData: cashboxData,
			summary: summary,
			terminal: terminal,
		})
	}

	async create({ request, response, session, auth }) {
		try {
			let transactionId = undefined

			if (request.all().bId == 0 || request.all().bId == null) {
				session.flash({ error: "Please select a BFI" })
				return response.route("back")
			}

			if (request.all().amount == 0) {
				session.flash({ error: "Amount can not be zero" })
				return response.route("back")
			}

			if (request.all().bankApi == 1) {
				const institute = await Institutes.find(request.all().financeInstitute)
				const transactionService = new TransactionService(institute);

				if (request.all().amount < 0) {
					const result = await transactionService.setCreditIn(request.all().login, Math.abs(request.all().amount), "Transfer from " + request.all().terminalCode)
					if (result == undefined) {
						session.flash({ error: "API Credit In is not successful" })
						return response.route("back")
					} else {
						transactionId = result
					}
				}

				if (request.all().amount > 0) {
					const result = await transactionService.setDeposit(request.all().login, request.all().amount, "Transfer from " + request.all().terminalCode)
					if (result == undefined) {
						session.flash({ error: "API Deposit is not successful" })
						return response.route("back")
					} else {
						transactionId = result
					}
				}
			}

			await Record.create({
				login: parseInt(request.all().login),
				financeInstitute: parseInt(request.all().financeInstitute),
				terminalCode: request.all().terminalCode,
				amount: parseInt(request.all().amount),
				currencyType: request.all().currencyType,
				detail: auth.user.email,
				bId: parseInt(request.all().bId),
				commentType: parseInt(request.all().commentType),
				comment: request.all().comment,
				bankApiCode: transactionId,
			})

			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params }) {
		try {
			let record = await Record.find(params.id)
			record.financeInstitute = parseInt(request.all().financeInstitute)
			record.terminalCode = request.all().terminalCode
			record.amount = parseInt(request.all().amount)
			record.bId = parseInt(request.all().bId)
			record.created_date = request.all().created_date
			record.commentType = parseInt(request.all().commentType)
			record.comment = request.all().comment

			await record.save()

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = TransferController
