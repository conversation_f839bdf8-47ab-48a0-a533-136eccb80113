'use strict'

const Common = use('App/Helpers/Common');
const SMS = use('App/Helpers/Sms');
const BusinessService = use("App/Helpers/BusinessService");

const Database = use("Database")
const Customer = use("App/Models/Customer")
const FinanceInstitute = use("App/Models/Institute")
const CustomerAnswer = use("App/Models/CustomerAnswer")

const { validate } = use("Validator")
const Mail = use('Mail')

const fs = require("fs");
const os = require("os");
const path = require("path");
const axios = require("axios");
const sharp = require("sharp");
const querystring = require("querystring");

const moment = require("moment");
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const jspdf = require('jspdf');


const caption = {
    pageTitle: "Trade Center Mobile - KYC",
    icon: 'fas fa-user fa-fw',
    close: "X",
    detailData: 'Details',
    detailInfo: 'Log Detail: ',
}

const appUrl = process.env.APP_URL
const mailTo = process.env.MAIL_TO

const kycOnlyMobile = process.env.KYC_ONLY_MOBILE

const tradeCenterAPI = process.env.KYC_API_URL //"http://tradecenterch.switzerlandnorth.cloudapp.azure.com:6009"

async function resizeImage(source, target) {
    try {
        await sharp(source)
            .withMetadata() // Meta verileri koru
            .rotate() // EXIF yönlendirme bilgilerine göre otomatik döndürme
            .resize(1080, 1920, {
                fit: 'inside', // Resmin boyutlarını koruyarak sığdır
                withoutEnlargement: true, // Resim büyütmeyi engelle
                //autoRotate: true // EXIF yönlendirme bilgilerine göre otomatik döndürme
            })
            .toFile(target);
    } catch (error) {
        console.error('Resim yeniden boyutlandırma hatası:', error);
    }
}

class MobileController {

    async kycWelcome({ view, params, request, response, session }) {
        const checkingCustomer = await Customer.findBy('login', params.id);

        //Mobile Kontrol
        if (kycOnlyMobile == 'true') {
            const userAgent = request.request.headers["user-agent"];
            if (!(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent))) {
                return view.render("mobile.kycOnlyMobile", {
                    caption: caption,
                    data: checkingCustomer,
                })
            }
        }

        try {
            if (checkingCustomer) {
                //test
                //this.sendMailToBank(checkingCustomer)

                return view.render("mobile.kycWelcome", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycInstruction({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycInstruction", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    //@Test
    async kycCamera({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycCamera", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    //@Obsolete
    async kycTakePhoto({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycPhoto", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    //@Obsolete
    async kycUploadPhoto({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const frontId = request.file('frontIdFile')
        const backId = request.file('backIdFile')
        const selfie = request.file('selfieFile')

        // Dosya boyutu kontrolü
        if (frontId.size > 2000000) {
            //return response.badRequest('Dosya boyutu 2 MBdan fazla olamaz.');
        }
        // Dosya tipi kontrolü
        if (!['image/jpeg', 'image/png', 'image/jpg', 'image'].includes(frontId.type)) {
            //return response.badRequest('Yalnızca JPEG ve PNG dosyaları yükleyebilirsiniz.');
        }

        try {
            if (checkingCustomer && frontId && backId && selfie) {
                // return view.render("mobile.kycUploadOk", {
                //     caption: caption,
                //     data: checkingCustomer,
                // })

                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`

                const frontIdFileName = `f_${frontId.clientName}`;
                frontId.clientName = frontIdFileName
                await frontId.move(uploadPath, { overwrite: true });

                const backIdFileName = `b_${backId.clientName}`;
                backId.clientName = backIdFileName
                await backId.move(uploadPath, { overwrite: true });

                const selfieFileName = `s_${selfie.clientName}`;
                selfie.clientName = selfieFileName
                await selfie.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.idCardFront = frontIdFileName
                checkingCustomer.idCardBack = backIdFileName
                checkingCustomer.idCardFoto = selfieFileName
                await checkingCustomer.save()

                //TradeCenter API ye bilgiler gönderilecek
                const appUrl = process.env.APP_URL

                const dateOfBirth = new Date(checkingCustomer.dateofbirth);
                const formattedDate = `${dateOfBirth.getDate().toString().padStart(2, '0')}.${(dateOfBirth.getMonth() + 1).toString().padStart(2, '0')}.${dateOfBirth.getFullYear()}`;

                const resCardValidate = await axios.post(`${tradeCenterAPI}/IdentityCardValidate`,
                    {
                        fullName: checkingCustomer.name + " " + checkingCustomer.surname,
                        birthDate: formattedDate,
                        nationality: checkingCustomer.nationality,
                        gender: checkingCustomer.gender,
                        documentFrontUrl: `${appUrl}images/uploads/${personId}/${frontIdFileName}`,
                        documentBackUrl: `${appUrl}images/uploads/${personId}/${backIdFileName}`,
                    }
                )
                /*Respose
                {
                    "isValid": true,
                    "frontIsValid": true,
                    "backIsValid": true,
                    "front": {
                        "fullName": true,
                        "birthDate": true,
                        "nationality": true,
                        "gender": true
                    },
                    "back": {
                        "fullName": true,
                        "birthDate": true,
                        "nationality": true,
                        "gender": true
                    }
                }*/
                if (resCardValidate.data) {
                    const resCard = resCardValidate.data

                    //veriyi Customer a yaz
                    if (resCard.isValid) {
                        checkingCustomer.kycIdentification = 1
                    }
                    checkingCustomer.kycIdentificationDescription = JSON.stringify(resCard);
                    await checkingCustomer.save()

                    const resFaceComparing = await axios.post(`${tradeCenterAPI}/FaceComparing`,
                        {
                            sourceImageUrl: `${appUrl}images/uploads/${personId}/${frontIdFileName}`,
                            targetImageUrl: `${appUrl}images/uploads/${personId}/${selfieFileName}`
                        }
                    )

                    if (resFaceComparing.data) {
                        const resFace = resFaceComparing.data

                        //similarity değerini Customer'a yaz
                        checkingCustomer.kycIdentificationRate = resFace.similarity
                        await checkingCustomer.save()

                        //herşey başarılı ise başarılı sayfasını göster
                        return view.render("mobile.kycUploadOk", {
                            caption: caption,
                            data: checkingCustomer,
                        })
                    }
                }

                return view.render("mobile.kycPhoto", {
                    caption: caption,
                    data: checkingCustomer,
                })

            } else if (checkingCustomer) {
                return view.render("mobile.kycPhoto", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycPhotoFront({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycPhotoFront", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycPhotoBack({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const frontId = request.file('frontIdFile')

        try {
            if (checkingCustomer && frontId) {

                //KYC Doğrulama Başladı Mail Gönder
                const subject = "Customer has started KYC Process"
                const body = `<html><body>
                <p><b>Customer:</b> ${checkingCustomer.name} ${checkingCustomer.surname}</p>
                <p><b>Login:</b> ${checkingCustomer.login}</p>
                <p><b>Date:</b> ${new Date()}</p>
                <p><b>Link:</b> <a href="${appUrl}customer/${personId}">Show Customer Detail on Panel</a></p>
                </body></html>`

                await Mail.raw(body, (message) => {
                    message
                        .to(mailTo)//"<EMAIL>"
                        .from("<EMAIL>")
                        .subject(subject)
                })
                /*
                await Mail.send('emails.welcome', {}, (message) => {
                    message.from('<EMAIL>')
                    message.to('<EMAIL>')
                })
                */

                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`

                const frontIdFileName = `f_${frontId.clientName}`;
                frontId.clientName = frontIdFileName
                await frontId.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.idCardFront = frontIdFileName
                await checkingCustomer.save()

                return view.render("mobile.kycPhotoBack", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else if (checkingCustomer) {
                return view.render("mobile.kycPhotoFront", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycPhotoSelfie({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const backId = request.file('backIdFile')

        try {
            if (checkingCustomer && backId) {
                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`

                const backIdFileName = `b_${backId.clientName}`;
                backId.clientName = backIdFileName
                await backId.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.idCardBack = backIdFileName
                await checkingCustomer.save()

                return view.render("mobile.kycPhotoSelfie", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else if (checkingCustomer) {
                return view.render("mobile.kycPhotoBack", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycCheck({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const selfie = request.file('selfieFile')

        try {
            if (checkingCustomer && selfie) {
                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`

                const selfieFileName = `s_${selfie.clientName}`;
                selfie.clientName = selfieFileName
                await selfie.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.idCardFoto = selfieFileName
                await checkingCustomer.save()

                //create resized folder
                fs.mkdirSync(`public/images/uploads/${personId}/resized`, { recursive: true });

                //resize images
                const idCardFront = `public/images/uploads/${personId}/${checkingCustomer.idCardFront}`
                const idCardBack = `public/images/uploads/${personId}/${checkingCustomer.idCardBack}`
                const idCardSelfie = `public/images/uploads/${personId}/${checkingCustomer.idCardFoto}`

                const apiIdCardFront = `public/images/uploads/${personId}/resized/${checkingCustomer.idCardFront}`
                const apiIdCardBack = `public/images/uploads/${personId}/resized/${checkingCustomer.idCardBack}`
                const apiIdCardSelfie = `public/images/uploads/${personId}/resized/${checkingCustomer.idCardFoto}`

                await resizeImage(idCardFront, apiIdCardFront)
                await resizeImage(idCardBack, apiIdCardBack)
                await resizeImage(idCardSelfie, apiIdCardSelfie)

                // await sharp(idCardFront).resize(1080, 1920).toFile(apiIdCardFront)
                // await sharp(idCardBack).resize(1080, 1920).toFile(apiIdCardBack)
                // await sharp(idCardSelfie).resize(1920, 1080).toFile(apiIdCardSelfie)
                // await sharp(idCardSelfie).resize(1080, 1920).toFile(idCardSelfie)

                //TradeCenter API ye bilgiler gönderilecek
                const dateOfBirth = new Date(checkingCustomer.dateofbirth);
                const formattedDate = `${dateOfBirth.getDate().toString().padStart(2, '0')}.${(dateOfBirth.getMonth() + 1).toString().padStart(2, '0')}.${dateOfBirth.getFullYear()}`;

                const resCardValidate = await axios.post(`${tradeCenterAPI}/IdentityCardValidate`,
                    {
                        fullName: checkingCustomer.name + " " + checkingCustomer.surname,
                        birthDate: formattedDate,
                        nationality: checkingCustomer.nationality,
                        gender: checkingCustomer.gender,
                        documentFrontUrl: `${appUrl}images/uploads/${personId}/resized/${checkingCustomer.idCardFront}`,
                        documentBackUrl: `${appUrl}images/uploads/${personId}/resized/${checkingCustomer.idCardBack}`,
                    }
                )

                if (resCardValidate.data) {
                    const resCard = resCardValidate.data

                    //veriyi Customer a yaz
                    if (resCard.isValid) {
                        checkingCustomer.kycIdentification = 1
                        checkingCustomer.kycIdentificationDate = new Date()
                    }
                    checkingCustomer.kycIdentificationDescription = JSON.stringify(resCard);
                    await checkingCustomer.save()

                    const resFaceComparing = await axios.post(`${tradeCenterAPI}/FaceComparing`,
                        {
                            sourceImageUrl: `${appUrl}images/uploads/${personId}/resized/${checkingCustomer.idCardFront}`,
                            targetImageUrl: `${appUrl}images/uploads/${personId}/resized/${checkingCustomer.idCardFoto}`
                        }
                    )

                    if (resFaceComparing.data) {
                        const resFace = resFaceComparing.data

                        //similarity değerini Customer'a yaz
                        checkingCustomer.kycIdentificationRate = resFace.similarity
                        await checkingCustomer.save()

                        //herşey başarılı ise başarılı sayfasını göster
                        return view.render("mobile.kycSelfieID", {
                            caption: caption,
                            data: checkingCustomer,
                        })
                    }
                }

                return view.render("mobile.kycPhotoSelfie", {
                    caption: caption,
                    data: checkingCustomer,
                })

            } else if (checkingCustomer) {
                return view.render("mobile.kycPhotoSelfie", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            if (error.code == "ERR_BAD_RESPONSE") {
                return view.render("mobile.kycPhotoFront", {
                    caption: caption,
                    data: checkingCustomer,
                    error: 'Ihre Bilder genügen nicht den Kriterien. Bitte fotografieren Sie Ihren Ausweis erneut.'
                })
            } else if (error.code == "ERR_BAD_REQUEST") {
                return view.render("mobile.kycError", {
                    caption: caption,
                    data: checkingCustomer,
                    error: 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.'
                })
            } else {
                return response.status(500).send(error.message)
            }
        }
    }

    async kycSelfieID({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const selfieId = request.file('selfieIdFile')

        try {
            if (checkingCustomer && selfieId) {
                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`

                const selfieIdFileName = `si_${selfieId.clientName}`;
                selfieId.clientName = selfieIdFileName
                await selfieId.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.idCardSelfie = selfieIdFileName
                await checkingCustomer.save()

                return view.render("mobile.kycDirtyMoney", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else if (checkingCustomer) {
                return view.render("mobile.kycSelfieID", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycDirtyMoney({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycDirtyMoney", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycQuestion({ view, params, request, response, session }) {
        let checkingCustomer = undefined
        if (params.login) {
            const login = Number(params.login)
            checkingCustomer = await Customer.findBy('login', login);
        } else {
            const personId = request.all().id
            checkingCustomer = await Customer.find(personId);
        }

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycQuestion", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycFinish({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                let record = await CustomerAnswer.findBy('personId', personId);

                const answer1 = request.all().answer1;
                const answer2 = request.all().answer2;
                const answer3 = request.all().answer3;
                const answer4 = request.all().answer4;
                const answer5 = request.all().answer5;
                const answer6 = request.all().answer6;
                const answer7 = request.all().answer7;
                const answer8 = request.all().answer8;
                const answer9 = request.all().answer9;
                const answer10 = request.all().answer10;
                const answer11 = request.all().answer11;
                const answer12 = request.all().answer12;
                const answer13 = request.all().answer13;
                const answer14 = request.all().answer14;

                if (record == null) {
                    await CustomerAnswer.create({
                        personId: personId,
                        answer1, answer2, answer3, answer4, answer5,
                        answer6, answer7, answer8, answer9, answer10,
                        answer11, answer12, answer13, answer14,
                    })
                    record = await CustomerAnswer.findBy('personId', personId);
                } else {
                    record.answer1 = answer1
                    record.answer2 = answer2
                    record.answer3 = answer3
                    record.answer4 = answer4
                    record.answer5 = answer5
                    record.answer6 = answer6
                    record.answer7 = answer7
                    record.answer8 = answer8
                    record.answer9 = answer9
                    record.answer10 = answer10
                    record.answer11 = answer11
                    record.answer12 = answer12
                    record.answer13 = answer13
                    record.answer14 = answer14

                    await record.save()
                }

                /*
                2b, 2c, ⁠3b, 3c, 3d, 3e, ⁠4d, ⁠5b, 5c, 5d, 5e, ⁠10a, ⁠13e, 13f, ⁠14b
                sorularda bu seçeneklerden biri seçildiyse benificial NO kalacak (kycChatbot=0)
                ve <EMAIL>'a mail gönderilecek
                */
                checkingCustomer.kycChatbot = 1
                checkingCustomer.kycChatbotDate = new Date()
                if ((answer2 != "unter_2500") ||
                    (answer3 != "weniger_10000") ||
                    (answer4 == "andere_quellen") ||
                    (answer5 != "selbst") ||
                    (answer10 == "ja") ||
                    (answer13 == "300000_1000000" || answer13 == "uber_1000000") ||
                    (answer14 == "nein")
                ) {
                    checkingCustomer.kycChatbot = 0
                }

                await checkingCustomer.save()

                //KYC Doğrulama bitti Mail Gönder
                let info = checkingCustomer.kycIdentificationDescription != null ? JSON.parse(checkingCustomer.kycIdentificationDescription) : {}
                info.id = checkingCustomer.id
                info.kycIdentificationRate = checkingCustomer.kycIdentificationRate

                const subject = "Customer has completed KYC Process"
                const mailData = {
                    customer: checkingCustomer.toJSON(),
                    apiResult: info,
                    questions: Common.KYC.kycQuestions,
                    answers: Common.KYC.answerTranslate(record.toJSON()),
                    rawAnswers: record.toJSON(),
                    appUrl: appUrl,
                    personId: personId,
                    today: new Date(),
                }

                await Mail.send('emails.kycComplete', mailData, (message) => {
                    message
                        .to(mailTo)//"<EMAIL>"
                        .from("<EMAIL>")
                        .subject(subject)
                })

                //eğer kycAddressValidation NO ise sms gönder ve müşteri hizmetlerine mail at
                if (checkingCustomer.kycAddressValidation == 0) {
                    const setting = await BusinessService.GetSettings();

                    const smsMessage = `Sehr geehrte(r) Kunde(in), Ihre bei der Kontoeröffnung angegebenen Adressdaten entsprechen nicht unserem Adress-Check. Bitte nutzen Sie den untenstehenden Link, um eine Kopie einer aktuellen Rechnung (z. B. Strom, Telefon, Internet) zu senden. ${appUrl}kyc/bill/${checkingCustomer.login} Vielen Dank fur Ihr Verständnis. Mit freundlichen Grussen, Tele Jet AG`

                    SMS.sendSMS(setting.toJSON(), checkingCustomer.phone, 'Alternate', smsMessage);
                }

                return view.render("mobile.kycSignature", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return view.render("mobile.kycError", { data: checkingCustomer, error: error.message })
            //return response.status(500).send(error)
        }
    }

    async kycSignature({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycSignatureOk", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycSignatureOk({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const signatureBase64 = request.all().signatureFile

        try {
            if (checkingCustomer && signatureBase64) {
                // Dosyanın kaydedilmesi ${Date.now()}
                const uploadPath = `public/images/uploads/${personId}`
                const signatureFileName = 'sig_sign.png'
                const fullFilePath = path.join(uploadPath, signatureFileName);
                const imageBuffer = Buffer.from(signatureBase64.split(',')[1], 'base64');

                fs.writeFileSync(fullFilePath, imageBuffer, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.signature = signatureFileName

                //kyc BenefficialOwner 1 olmalı bizdeki değer kycChatbot=1
                checkingCustomer.kycChatbot = 1
                await checkingCustomer.save()

                //kontrat oluştur
                this.createContractPDF(checkingCustomer)

                //eski hali burda bitiriyorduk.
                // return view.render("mobile.kycFinish", {
                //     caption: caption,
                //     data: checkingCustomer,
                // })

                //utilitybill yükleme kısmına devam et
                return view.render("mobile.kycUtilityBill", {
                    caption: caption,
                    data: checkingCustomer,
                })

            } else if (checkingCustomer) {
                return view.render("mobile.kycSignatureOk", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    //@Obsolete
    async kycUtilityBill({ view, params, request, response, session }) {
        const checkingCustomer = await Customer.findBy('login', params.id);

        //Mobile Kontrol
        if (kycOnlyMobile == 'true') {
            const userAgent = request.request.headers["user-agent"];
            if (!(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent))) {
                return view.render("mobile.kycOnlyMobile", {
                    caption: caption,
                    data: checkingCustomer,
                })
            }
        }

        try {
            if (checkingCustomer) {
                return view.render("mobile.kycUtilityBill", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async kycUtilityBillUpload({ view, params, request, response, session }) {
        const personId = request.all().id
        const checkingCustomer = await Customer.find(personId);

        const utilityBill = request.file('utilityBillFile')

        try {
            if (checkingCustomer && utilityBill) {
                const uploadPath = `public/images/uploads/${personId}`

                const utilityBillFileName = `ub_${utilityBill.clientName}`;
                utilityBill.clientName = utilityBillFileName
                await utilityBill.move(uploadPath, { overwrite: true });

                //resim isimlerini Customer'a ye yaz
                checkingCustomer.utilityBill = utilityBillFileName
                await checkingCustomer.save()

                //KYC için Fatura gönderildi Büroya Mail Gönder
                const subject = "Customer has sent Utility Bill for KYC Process"
                const body = `<html><body>
                <p><b>Customer:</b> ${checkingCustomer.name} ${checkingCustomer.surname}</p>
                <p><b>Login:</b> ${checkingCustomer.login}</p>
                <p><b>Date:</b> ${new Date()}</p>
                <p><b>Link:</b> <a href="${appUrl}customer/${personId}">Show Customer Detail on Panel</a></p>
                <p><b>File:</b> ${utilityBillFileName}</p>
                </body></html>`

                await Mail.raw(body, (message) => {
                    message
                        .to(mailTo)//"<EMAIL>"
                        .from("<EMAIL>")
                        .subject(subject)
                })

                //KYC için Fatura gönderildi Bankaya(FinanceInstitute) Mail Gönder
                this.sendMailToBank(checkingCustomer)

                return view.render("mobile.kycUtilityBillFinish", {
                    caption: caption,
                    data: checkingCustomer,
                })
            } else {
                return response.status(404).send('Not found')
            }
        } catch (error) {
            return response.status(500).send(error)
        }
    }


    async createContractPDF(customer) {
        const uploadPath = `public/images/uploads/${customer.id}`
        const pdfName = 'contract.pdf'
        const signName = 'sig_sign.png'
        const dateString = moment(new Date()).format("YYMMDDHHmmss")
        const contractDate = moment(new Date()).format("DD.MM.YYYY")

        const imagesPath = `public/kyc/images`

        const doc = new jspdf.jsPDF();

        const xLeft = 20
        const xRight = 190
        const yTop = 20
        const yBottom = 287
        const xCenter = xLeft + (xRight - xLeft) / 2
        const lineHeight = 4
        let currentLine = 0

        //doc.line(20, 25, 190, 25); // Alt çizgi ekle
        //const fonts = doc.getFontList();

        //logo ekle
        const logoTelejet = fs.readFileSync(`${imagesPath}/Logo-TeleJet.png`)
        doc.addImage(logoTelejet, 'PNG', xLeft, currentLine * lineHeight + yTop, 24, 12)

        doc.setFontSize(10)
        doc.setFont('helvetica', 'bold')
        doc.text(`GwG-File Nr. ${dateString}`, xCenter, currentLine * lineHeight + yTop, { align: 'center' })

        doc.setFontSize(9)
        doc.setFont('helvetica', 'bold')
        doc.text('Tele Jet Com AG', xRight, currentLine * lineHeight + yTop, { align: 'right' })
        doc.setFont('helvetica', 'normal')
        doc.setTextColor(128, 128, 128)
        currentLine += 1
        doc.text('Schaffhauserstrasse 315', xRight, currentLine * lineHeight + yTop, { align: 'right' })
        currentLine += 1
        doc.text('8050 Zürich', xRight, currentLine * lineHeight + yTop, { align: 'right' })

        //boşluk
        currentLine += 2

        currentLine += 1
        doc.setFontSize(10)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(0, 0, 0)
        doc.text('Feststellung des wirtschaftlich Berechtigten und finanzielle Angaben', xLeft, currentLine * lineHeight + yTop)

        //sorular ve cevapları
        let rawAnswers = await CustomerAnswer.findBy('personId', customer.id);
        let customerAnswers = Common.KYC.answerTranslate(rawAnswers.toJSON())
        const questions = Common.KYC.kycQuestions
        for (let i = 0; i < 14; i++) {
            //boşluk
            currentLine += 1

            currentLine += 1
            doc.setFont('helvetica', 'bolditalic')
            doc.text(questions[`question${i + 1}`], xLeft, currentLine * lineHeight + yTop, { wordWrap: true, maxWidth: 170 })

            if (i == 2) currentLine += 1
            if (i == 9) currentLine += 2
            currentLine += 1
            doc.setFont('helvetica', 'normal')
            doc.text(customerAnswers[`answer${i + 1}`], xLeft, currentLine * lineHeight + yTop)
        }

        //boşluk
        currentLine += 1

        //onaylıyorum ifadesi
        currentLine += 1
        doc.setFont('helvetica', 'normal')
        doc.text('Ich bestätige hiermit, dass meine Angaben korrekt sind.', xLeft, currentLine * lineHeight + yTop)

        //boşluk
        currentLine += 1

        //isim tarih
        currentLine += 1
        doc.text(`${contractDate}, ${customer.name} ${customer.surname}`, xLeft, currentLine * lineHeight + yTop)

        // imza
        const sign = fs.readFileSync(`${uploadPath}/${signName}`);
        doc.addImage(sign, 'PNG', xLeft, currentLine * lineHeight + yTop + 1, 64, 16);

        //alt açıklamalar
        currentLine += 6
        doc.setFontSize(9)
        doc.setTextColor(128, 128, 128)
        doc.text('Die Tele Jet Com AG ist Mitglied des VQF Verein zur Qualitätssicherung von', xLeft, currentLine * lineHeight + yTop)
        //alt logo
        const logoVqf = fs.readFileSync(`${imagesPath}/Logo-VQF.png`)
        doc.addImage(logoVqf, 'PNG', xRight - xLeft, currentLine * lineHeight + yTop, 18, 8);
        currentLine += 1
        doc.text('Finanzdienstleistungen.', xLeft, currentLine * lineHeight + yTop)
        doc.setFont('helvetica', 'bold')
        doc.text('VQF Mitglied Nr. 100775', xLeft + 35, currentLine * lineHeight + yTop)
        // pdf kaydetme
        doc.save(`${uploadPath}/${pdfName}`);
    }

    async sendMailToBank(customer) {
        const institute = await FinanceInstitute.findBy('id', customer.financeinstitute)

        let bankMailTo = "<EMAIL>"
        bankMailTo = institute ? institute.kycMail : "<EMAIL>"

        const subject = "KYC Verification Documents"
        const body = `<html><body>
        <p>Dear Sir or Madam<br/><br/>
        As agreed, we are sending you the verification documents of the customer listed below for your review and records:<br/><br/>
        <b>Login Number:</b> ${customer.login}<br/>
        <b>Name/Surname:</b> ${customer.name} ${customer.surname}<br/>
        <b>Email:</b> ${customer.email}<br/>
        <b>Mobile Number:</b> ${customer.phone}<br/><br/>
        Please note that the information provided is confidential and may only be used for the intended purpose.<br/><br/>
        Kind regards<br/>
        Tele Jet Com AG<br/>
        KYC Team</p>
        <img src="cid:telejetLogo" height="80px" />
        </body></html>`

        const uploadPath = `public/images/uploads/${customer.id}`
        const imagePath = `public/images`

        await Mail.raw(body, (message) => {
            message
                .to(bankMailTo)
                .from("<EMAIL>")
                .subject(subject)
                .embed(`${imagePath}/invoiceTeleJet_logo.png`, 'telejetLogo')
                .attach(`${uploadPath}/${customer.idCardFront}`)
                .attach(`${uploadPath}/${customer.idCardBack}`)
                .attach(`${uploadPath}/${customer.utilityBill}`)
            //.attach(Helpers.tmpPath('guides/getting-started.pdf'))
        })
    }

    async destroy({ response }) {
        return response.route("kycWelcome")
    }
}

module.exports = MobileController
