'use strict'



class PostController {

	async index({ view }) {
		//return "controller buras<PERSON>"

		const viewData = {
			veri: "Controllerdan gelen view",
		}

		const postList = [
			{ baslik : "liste 1"},
			{ baslik : "liste 2"},
			{ baslik : "liste 3"},
		]

		const settingList = await Setting.all()

		return view.render("home", { viewData, postList, settingList })
	}


}

module.exports = PostController
