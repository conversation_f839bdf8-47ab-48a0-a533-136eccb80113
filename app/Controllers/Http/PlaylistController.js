'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const Record = use("App/Models/Playlist")

const Helpers = use("Helpers")

const isActive = [
	{ id: 0, title: "Passive" },
	{ id: 1, title: "Active" },
]

const fieldList = [
	{ n: 'url', c: 'Url', i: 'fas fa-file', t: 'text', v: '', e: true, ne: true },
	{ n: 'isActive', c: 'Is Active', i: 'fas fa-check fa-fw', t: 'select', v: isActive, e: true, ne: true },
]

const caption = {
	navPlaylist: "active",
	pageTitle: "Playlist",
	icon: 'fas fa-money-check-alt fa-fw',
	route: '/playlist/',
	newAction: 'createPlaylist',
	newData: "New Entry",
	editData: "Edit Data",
	deleteData: "Delete Data",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
}

class PlaylistController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();
		const list = await Record.query().orderBy('id').fetch()
		return view.render("pages.playlist", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting
		})
	}

	async create({ request, response, session, auth }) {
		try {
			await Record.create({
				url: request.input("url"),
			})
			session.flash({ info: caption.savedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async update({ request, response, session, params, auth }) {
		try {
			const record = await Record.find(params.id)
			record.url = request.all().url
			record.isActive = request.all().isActive
			await record.save()

			session.flash({ info: caption.updatedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params, auth }) {
		try {
			const record = await Record.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}
}

module.exports = PlaylistController
