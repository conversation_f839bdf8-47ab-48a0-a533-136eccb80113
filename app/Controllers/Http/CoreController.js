'use strict'

const { validate } = use("Validator")

const SMS = use('App/Helpers/Sms')

const Database = use("Database")
const Setting = use("App/Models/Setting");
const Customer = use("App/Models/Customer")
const WinnerPhone = use("App/Models/WinnerPhone")

const axios = require("axios")

const caption = {
    navLogin: "active",
    pageTitle: "Trade Center",
    icon: 'fas fa-user fa-fw',
    creditUpdate: ' CHF wurden auf Ihr Konto geladen',
}

class CoreController {

    async invitationIndex({ view, params }) {
        return view.render("pages.invitation", {
            title: "",
            yourIdCaption: "Einladende Login-ID",
            friendIdCaption: "Eingeladene Login-ID",
            buttonCaption: "Senden",
            caption: caption,
            data: null,
        })
    }

    async invitationCheck({ request, response, session }) {
        const validation = await validate(request.all(), {
            yourId: "required",
            friendId: "required"
        })

        if (validation.fails()) {
            session.withErrors(validation.messages()).flashAll()
            return response.redirect("back")
        }

        try {
            const setting = await Setting.find(1);
            let money = setting.invitationMoney
            let duration = setting.invitationDuration

            let today = new Date()
            today.setDate(today.getDate() - duration)
            let begin = (today).toLocaleDateString('tr-TR')
            today.setDate(today.getDate() + duration)
            let end = (today).toLocaleDateString('tr-TR')

            let yourId = request.input("yourId")
            let friendId = request.input("friendId")

            let you = await Database.select('person.*')
                .from('person')
                .where(function () {
                    this.andWhere('person.login', yourId)
                }).first()

            let friend = await Database.select('person.*')
                .from('person')
                .where(function () {
                    this.andWhere('person.login', friendId)
                    if (duration > 0) {
                        this.whereBetween('person.created_date', [begin, end])
                    }
                }).whereNull('person.invitorId').first()

            if ((you && you.id) && (friend && friend.id)) {
                let record = await Customer.find(friend.id)
                record.invitorId = you.id
                await record.save()

                session.flash({ info: money + caption.creditUpdate })
                return response.route("back")
            } else {
                session.flash({ error: "Ihre eingegebenen Informationen stimmen nicht mit dem System überein!" })
                return response.route("back")
            }
        } catch (error) {
            session.flash({ error: error.message })
            return response.route("back")
        }
    }

    async destroy({ response }) {
        return response.route("invitation")
    }

    async addWinnerPhoneSendSms({ request, response, session }) {
        const validation = await validate(request.all(), {
            phone: "required",
            credit: "required"
        })

        if (validation.fails()) {
            session.withErrors(validation.messages()).flashAll()
            return response.status(400).send('Bad request')
        }

        try {
            const phone = request.input("phone")
            const credit = parseInt(request.input("credit"))

            if (phone.length > 7 && credit > 0) {
                await WinnerPhone.create(
                    {
                        phone: phone,
                        credit: credit
                    }
                );

                const setting = await Setting.find(1);

                const link = 'https://client.forex-724.com/register/ib-client/24a47f97-aa5d-4942-ae62-cec573eef0e5?demo=false&branchUuid=622284c0-ec79-4e1a-97ac-ac0cd1f310c1'
                const message = `Registrieren Sie jetzt ein Konto, um Ihre Prämie zu erhalten. Klicken Sie auf diesen Link, um ein Konto zu eröffnen! ${link}`

                SMS.sendSMS(setting.toJSON(), phone, 'Alternate', message);

                return response.status(200).send('OK')
            } else {
                return response.status(400).send('Bad request')
            }

        } catch (error) {
            return response.status(500).send(error)
        }
    }

    async addWinnerPhoneSendSmsJson({ request, response, session }) {

        try {

            let url = request.request.headers.origin
            if (url != 'https://quiz.tradecenter24.com') {
                response.status(403).send('Forbidden')
            }

            const bestFormat = request.accepts(['json'])

            if (bestFormat === 'json') {
                const body = request.post()

                const client = body.client

                const phone = client.split(':')[0]

                //20, 30, 50, 75, 100
                const prize20 = "xB3c6"
                const prize30 = "5fhTb"
                const prize50 = "25AgT"
                const prize75 = "L5h6p"
                const prize100 = "uRh89"

                let credit = 0
                const creditCode = parseInt(client.split(':')[1])
                switch (creditCode) {
                    case prize20: credit = 20
                        break;
                    case prize30: credit = 30
                        break;
                    case prize50: credit = 50
                        break;
                    case prize75: credit = 75
                        break;
                    case prize100: credit = 100
                        break;
                    default: credit = 0
                        break;
                }

                //geçici kod
                credit = parseInt(client.split(':')[1])

                if (phone.length > 7 && credit > 0) {
                    await WinnerPhone.create(
                        {
                            phone: phone,
                            credit: credit
                        }
                    );

                    const setting = await Setting.find(1);

                    const link = 'https://client.forex-724.com/register/ib-client/24a47f97-aa5d-4942-ae62-cec573eef0e5?demo=false&branchUuid=622284c0-ec79-4e1a-97ac-ac0cd1f310c1'
                    const message = `Registrieren Sie jetzt ein Konto, um Ihre Prämie zu erhalten. Klicken Sie auf diesen Link, um ein Konto zu eröffnen! ${link}`

                    SMS.sendSMS(setting.toJSON(), phone, 'Alternate', message);

                    return response.json({ success: true })
                } else {
                    return response.status(400).send('Phone must be longer than 7 characters and profit must be greater than 0')
                }
            } else {
                return response.status(400).send('Bad request')
            }

        } catch (error) {
            return response.status(500).send(error)
        }
    }
}

module.exports = CoreController
