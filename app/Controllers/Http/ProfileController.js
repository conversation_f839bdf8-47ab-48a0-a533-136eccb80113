'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const User = use("App/Models/User")

const caption = {
    navLogin: "active",
    pageTitle: "User Informations",
    icon: 'fas fa-user fa-fw',
}

class ProfileController {

    async index({ view }) {
        const setting = await BusinessService.GetSettings();

        if (setting.appType == 'panel') {
            return view.render("pages.profile", {
                title: "User Informations",
                passwordCaption: "Enter your new Password",
                passwordRCaption: "Confirm your new Password",
                buttonCaption: "Save Changes",
                emailCaption: "Your Mail",
                caption: caption,
                data: null,
                setting: setting,
            })
        }
        else if (setting.appType == 'report') {
            return view.render("reports.profile", {
                title: "User Informations",
                passwordCaption: "Enter your new Password",
                passwordRCaption: "Confirm your new Password",
                buttonCaption: "Save Changes",
                emailCaption: "Your Mail",
                caption: caption,
                data: null,
                setting: setting,
            })
        }
    }

    async update({ auth, session, request, response }) {
        const user = await User.findOrFail(auth.user.id)

        user.password = request.input('pass')
        await user.save()

        session.flash({ alert: 'User was updated successfully!' })
        return response.route('getProfile')
    }

}

module.exports = ProfileController
