'use strict'

const Common = use('App/Helpers/Common');

const SMS = use('App/Helpers/Sms');
const KYC = use('App/Helpers/Kyc');
const BusinessService = use("App/Helpers/BusinessService");

const Database = use("Database")
const Customer = use("App/Models/Customer")
const CustomerAnswer = use("App/Models/CustomerAnswer")
const Institute = use("App/Models/Institute")
const Terminals = use("App/Models/Terminal")

const axios = require("axios")
const querystring = require('querystring');
const fs = require('fs').promises;

const moment = require("moment")
const { off } = require("process");
const { error } = require("console");
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const kycRiskChecks = [
	{ id: 0, title: "KYC Risk Check: NO" },
	{ id: 1, title: "KYC Risk Check: YES" },
]

const kycAddressValidations = [
	{ id: 0, title: "KYC Address Valid: NO" },
	{ id: 1, title: "KYC Address Valid: YES" },
]

const kycIdentifications = [
	{ id: 0, title: "KYC Identification: NO" },
	{ id: 1, title: "KYC Identification: YES" },
]

const kycChatbot = [
	{ id: 0, title: "KYC Benef. Owners: NO" },
	{ id: 1, title: "KYC Benef. Owners: YES" },
]

const fieldList = [//name,caption,icon,type,value,enabled
	{ n: 'surname', c: 'Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'name', c: 'Name', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'gender', c: 'M/F', i: 'fas fa-venus-mars fa-fw', t: 'select', v: Common.Basic.genders, e: true },
	{ n: 'email', c: 'Email', i: 'fas fa-at fa-fw', t: 'email', v: '', e: true },
	{ n: 'phone', c: 'Phone', i: 'fas fa-phone fa-fw', t: 'text', v: '', e: true },
	{ n: 'avenue', c: 'Street', i: 'fas fa-map-marker fa-fw', t: 'text', v: '', e: true },
	{ n: 'address', c: 'Address', i: 'fas fa-map-marked-alt fa-fw', t: 'text', v: '', e: false },
	{ n: 'financeinstitute', c: 'Institute', i: 'fas fa-university fa-fw', t: 'select', v: [], e: true },
	{ n: 'leverage', c: 'Leverage', i: 'fas fa-percent fa-fw', t: 'text', v: '1:1', e: false, l: 'Leverage' },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: false, l: 'Trader ID' },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: false },
	{ n: 'nickname', c: 'Nickname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: false },

	{ n: 'balance', c: 'Balance', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Balance' },
	{ n: 'equity', c: 'Equity', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Equity' },
	{ n: 'deposit', c: 'Total Deposit', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Total Deposit' },
	{ n: 'withdrawalCapacity', c: 'Withdrawal Capacity', i: 'fas fa-hand-holding-usd', t: 'number', v: 0, e: false, l: 'Withdrawal Capacity' },

	{ n: 'kycRiskCheck', c: 'KYC Risk Check', i: 'fas fa-check', t: 'labelSelect', v: kycRiskChecks, e: true, l: 'kycRiskCheck' },
	{ n: 'kycAddressValidation', c: 'KYC Address Validation', i: 'fas fa-check', t: 'labelSelect', v: kycAddressValidations, e: true, l: 'kycAddressValidation' },
	{ n: 'utilityBillFoto', c: 'Utility Bill Photo', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },
	{ n: 'kycIdentification', c: 'KYC Identification', i: 'fas fa-check', t: 'labelSelect', v: kycIdentifications, e: true, l: 'kycIdentification' },
	{ n: 'kycChatbot', c: 'KYC Beneficial Owners', i: 'fas fa-check', t: 'labelSelect', v: kycChatbot, e: true, l: 'kycChatbot' },
	{ n: 'contract', c: 'Signed WB Form PDF', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },
	{ n: 'created_date', c: 'Date', i: 'fas fa-calendar-alt fa-fw', t: 'text', v: '', e: false, l: 'Created' },
	{ n: 'depositLimit', c: 'Individual Deposit Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Individual Deposit Limit' },
	{ n: 'withdrawalLimit', c: 'Individual Withdrawal Limit', i: 'fas fa-sun fa-fw', t: 'number', v: '0', e: true, l: 'Individual Withdrawal Limit' },
]

/*{
	"isValid":true,
	"frontIsValid":true,
	"backIsValid":true,
	"front":{"fullName":true,"birthDate":true,"nationality":true,"gender":true},
	"back":{"fullName":true,"birthDate":true,"nationality":true,"gender":false}
}*/
const infoFieldList = [//name,caption,type,value,enabled
	{ n: 'kycIdentificationRate', c: 'Identification Rate', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'kycIdCardFront', c: 'ID Front', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },
	{ n: 'kycIdCardBack', c: 'ID Back', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },
	{ n: 'kycIdCardFoto', c: 'Face Photo', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },
	{ n: 'kycIdCardSelfie', c: 'Selfie Photo', i: 'fas fa-image  fa-fw', t: 'image', v: '', e: true },

	{ n: 'kycApiIsValid', c: 'ID Valid', t: 'switch', v: false, e: true },
	{ n: 'kycApiFrontIsValid', c: 'ID Front Valid', t: 'switch', v: false, e: true },
	{ n: 'kycApiBackIsValid', c: 'ID Back Valid', t: 'switch', v: false, e: true },
	{ n: 'kycApiFrontFullName', c: 'Front FullName', t: 'switch', v: false, e: true },
	{ n: 'kycApiFrontBirthDate', c: 'Front BirthDate', t: 'switch', v: false, e: true },
	{ n: 'kycApiFrontNationality', c: 'Front Nationality', t: 'switch', v: false, e: true },
	{ n: 'kycApiFrontGender', c: 'Front Gender', t: 'switch', v: false, e: true },
	{ n: 'kycApiBackFullName', c: 'Back FullName', t: 'switch', v: false, e: true },
	{ n: 'kycApiBackBirthDate', c: 'Back BirthDate', t: 'switch', v: false, e: true },
	{ n: 'kycApiBackNationality', c: 'Back Nationality', t: 'switch', v: false, e: true },
	{ n: 'kycApiBackGender', c: 'Back Gender', t: 'switch', v: false, e: true },
]

const questionFieldList = [//name,caption,icon,type,value,enabled
	{
		n: 'answer1', c: Common.KYC.kycQuestions.question1,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers1, e: true
	},
	{
		n: 'answer2', c: Common.KYC.kycQuestions.question2,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers2, e: true
	},
	{
		n: 'answer3', c: Common.KYC.kycQuestions.question3,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers3, e: true
	},
	{
		n: 'answer4', c: Common.KYC.kycQuestions.question4,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers4, e: true
	},
	{
		n: 'answer5', c: Common.KYC.kycQuestions.question5,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers5, e: true
	},
	{
		n: 'answer6', c: Common.KYC.kycQuestions.question6,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers6, e: true
	},
	{
		n: 'answer7', c: Common.KYC.kycQuestions.question7,
		i: 'fas fa-check fa-fw', t: 'textInfo', v: '', e: true
	},
	{
		n: 'answer8', c: Common.KYC.kycQuestions.question8,
		i: 'fas fa-check', t: 'textInfo', v: '', e: true
	},
	{
		n: 'answer9', c: Common.KYC.kycQuestions.question9,
		i: 'fas fa-check', t: 'textInfo', v: '', e: true
	},
	{
		n: 'answer10', c: Common.KYC.kycQuestions.question10,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers10, e: true
	},
	{
		n: 'answer11', c: Common.KYC.kycQuestions.question11,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers11, e: true
	},
	{
		n: 'answer12', c: Common.KYC.kycQuestions.question12,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers12, e: true
	},
	{
		n: 'answer13', c: Common.KYC.kycQuestions.question13,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers12, e: true
	},
	{
		n: 'answer14', c: Common.KYC.kycQuestions.question14,
		i: 'fas fa-check', t: 'selectInfo', v: Common.KYC.kycAnswers10, e: true
	},
]

let date = moment().format('YYYY-MM-DD')
const queryFieldList = [//name,caption,icon,type,value,enabled
	{ n: 'name', c: 'Name/Surname', i: 'fas fa-id-card fa-fw', t: 'text', v: '', e: true },
	{ n: 'login', c: 'Trader ID', i: 'fas fa-fingerprint fa-fw', t: 'text', v: '', e: true },
	{ n: 'financeInstitute', c: 'Institute', i: 'fas fa-university fa-fw', t: 'select', v: [], e: true },
	{ n: 'terminalCode', c: 'Terminal', i: 'fas fa-desktop fa-fw', t: 'text', v: '', e: true },
	{ n: 'beginDate', c: 'Begin', i: 'fas fa-calendar-alt fa-fw', t: 'date', v: date, e: true },
	{ n: 'endDate', c: 'End', i: 'fas fa-calendar-alt fa-fw', t: 'date', v: date, e: true },
	{ n: 'kycForce', c: 'Kyc Required', t: 'switch', v: false, e: true },
	{ n: 'kycProcessed', c: 'Kyc Processed', t: 'switch', v: false, e: true },
]

const appUrl = process.env.APP_URL
const imgPath = `${appUrl}images/uploads/`

const caption = {
	navCustomer: "active",
	pageTitle: "Accounts",
	icon: 'fas fa-user fa-fw',
	route: '/customers/',
	newAction: 'createCustomer',
	newData: "New Entry",
	editData: "Customer Details",
	deleteData: "Delete Data",
	sendSmsData: "Send SMS",
	sendMultiSmsData: "Send Multi SMS",
	infoTitle: "KYC Api Info",
	answerTitle: "KYC Answers",
	deleteWarning: "<strong>Warning!</strong> This can't be rollback.<br>Are you sure delete this data?",
	sendSmsWarning: "<strong>Warning!</strong> Sms Message will send.<br>Are you sure send SMS to this customer?",
	sendMultiSmsWarning: "<strong>Warning!</strong> Sms Message will send.<br>Are you sure send this SMS to ALL customer?",
	close: "Close",
	new: "New",
	save: "Save",
	add: "Add",
	edit: "Edit",
	delete: "Delete",
	send: "Send",
	active: "Active",
	passive: "Passive",
	savedMessage: "It was saved successfuly",
	updatedMessage: "It was updated successfuly",
	deletedMessage: "It was deleted successfuly",
	smsSentMessage: "It was sent successfuly",
	infoUpdatedMessage: "Saved",
	kycRiskCheckMessage: "KYC Risk Check was sent successfuly",
	query: 'Query',
	queryRoute: '/customers',
	queryData: 'Data Query',
	queryAction: 'queryTransfer',
	find: "Find",
	syncRoute: '/customers',
	sendSmsRoute: '/customer/sendSms',
	sendCustomSmsRoute: '/customer/sendCustomSms',
	sendMultiSmsRoute: '/customer/sendMultiSms',
	syncMessage: "It was synced successfuly",
	transferRoute: '/transfers/',
	imgPath: imgPath,
}

const currentQuery = {
	name: null,
	tradeId: null,
	end: null,
	begin: null,
	terminalCode: null,
	financeInstitute: null,
	kycForce: 'off',
	kycProcessed: 'off',
}

class CustomerController {

	async index({ view }) {
		const setting = await BusinessService.GetSettings();
		const listAnswer = await CustomerAnswer.query().orderBy('id', 'desc').fetch()

		const list = await Customer.query().orderBy('id', 'desc').fetch()
		//Database.raw('DATE_FORMAT(created_date, "%Y-%m-%d")as date')

		const instituteList = await Institute.query().fetch()
		fieldList[7].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[1].v.splice(0, 0, { id: 0, title: "ALL" })

		const terminalList = await Terminals.query().fetch()
		queryFieldList[2].v = terminalList.toJSON().map(terminal => {
			let id = terminal.id;
			let title = terminal.terminalCode;
			return { id, title }
		})
		queryFieldList[2].v.splice(0, 0, { id: 0, title: "ALL" })

		let listInfo = []
		for (const item of list) {
			let info = item.kycIdentificationDescription != null ? JSON.parse(item.kycIdentificationDescription) : {}
			info.id = item.id
			info.kycIdentificationRate = item.kycIdentificationRate
			info.kycIdCardFront = item.idCardFront
			info.kycIdCardBack = item.idCardBack
			info.kycIdCardFoto = item.idCardFoto
			info.kycIdCardSelfie = item.idCardSelfie
			listInfo.push(info)
		}

		for (let c of list) {
			c.hasAnswer = listAnswer.toJSON().some(answer => answer.personId == c.id)
		}

		return view.render("pages.customer", {
			caption: caption,
			fields: fieldList,
			data: list.toJSON(),
			setting: setting,
			infoFields: infoFieldList,
			dataInfo: listInfo,
			questionFields: questionFieldList,
			dataAnswer: listAnswer.toJSON()
		})
	}

	async query({ view, request }) {
		const setting = await BusinessService.GetSettings();
		const listAnswer = await CustomerAnswer.query().orderBy('id', 'desc').fetch()

		let today = new Date()
		let name = request.all().name
		let tradeId = request.all().login
		today.setDate(today.getDate() + 1)
		let end = request.all().endDate || moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 1810)
		let begin = request.all().beginDate || moment(today).format('YYYY-MM-DD')
		let terminalCode = request.all().terminalCode
		let financeInstitute = request.all().financeInstitute
		let kycForce = request.all().kycForce == 'on' ? 'on' : 'off'
		let kycProcessed = request.all().kycProcessed == 'on' ? 'on' : 'off'

		currentQuery.name = name
		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = tradeId
		currentQuery.terminalCode = terminalCode
		currentQuery.financeInstitute = financeInstitute
		currentQuery.kycForce = kycForce
		currentQuery.kycProcessed = kycProcessed

		let list = await Database.select('person.*')
			.from('person')
			.leftJoin('terminal', 'person.terminalCode', 'terminal.terminalCode')
			.where(function () {
				this.whereBetween('person.created_date', [currentQuery.begin, currentQuery.end])
				if (currentQuery.name) {
					this.andWhereRaw("CONCAT(person.surname, ' ', person.name) LIKE ?", [`%${currentQuery.name}%`])
				}
				if (currentQuery.tradeId) {
					this.andWhere('person.login', currentQuery.tradeId)
				}
				if (currentQuery.terminalCode) {
					this.andWhere('person.terminalCode', currentQuery.terminalCode)
				}
				if (currentQuery.financeInstitute && currentQuery.financeInstitute != 0) {
					this.andWhere('person.financeinstitute', currentQuery.financeInstitute)
				}
				if (currentQuery.kycForce && currentQuery.kycForce == 'on') {
					this.andWhere('person.kycforce', 1)
					this.andWhere('person.financeinstitute', 4)
				}
				if (currentQuery.kycProcessed && currentQuery.kycProcessed == 'on') {
					this.andWhere(function () {
						this.orWhere('person.kycIdentification', '!=', 0)
						this.orWhere('person.kycAddressValidation', '!=', 0)
						this.orWhere('person.kycUtilityBill', '!=', 0)
						this.orWhere('person.kycChatbot', '!=', 0)
					})
					this.andWhere('person.financeinstitute', 4)
				}
			}).orderBy('person.id', 'desc');

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		list.forEach(element => {
			element.created_date = moment(element.created_date).format()
			element.balance = element.balance != null ? element.balance : 0
			element.utilityBill = element.utilityBill ? `${element.id}/${element.utilityBill}` : null
		});

		const instituteList = await Institute.query().fetch()
		fieldList[7].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[2].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[2].v.splice(0, 0, { id: 0, title: "ALL" })

		let listInfo = []
		for (const item of list) {
			let info = item.kycIdentificationDescription != null ? JSON.parse(item.kycIdentificationDescription) : {}
			info.id = item.id
			info.kycIdentificationRate = item.kycIdentificationRate
			info.kycIdCardFront = `${item.id}/${item.idCardFront}`
			info.kycIdCardBack = `${item.id}/${item.idCardBack}`
			info.kycIdCardFoto = `${item.id}/${item.idCardFoto}`
			info.kycIdCardSelfie = `${item.id}/${item.idCardSelfie}`
			listInfo.push(info)
		}

		for (let c of list) {
			c.hasAnswer = listAnswer.toJSON().some(answer => answer.personId == c.id)
			c.kycUtilityBillDate = c.kycUtilityBillDate && moment(c.kycUtilityBillDate).format()
			c.kycAddressValidationDate = c.kycAddressValidationDate && moment(c.kycAddressValidationDate).format()
			c.kycIdentificationDate = c.kycIdentificationDate && moment(c.kycIdentificationDate).format()
			c.kycChatbotDate = c.kycChatbotDate && moment(c.kycChatbotDate).format()
			c.groupName = Common.Basic.customerGroupNames.filter(group => group.id == c.groupName).map(group => group.title)[0]
			c.pdfOk = await fs.access(`public/images/uploads/${c.id}/contract.pdf`)?.then(() => true).catch(() => false)
		}

		return view.render("pages.customer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list,
			setting: setting,
			infoFields: infoFieldList,
			dataInfo: listInfo,
			questionFields: questionFieldList,
			dataAnswer: listAnswer.toJSON()
		})
	}

	async getById({ view, request, params }) {
		let customer = await Customer.find(params.id)

		const setting = await BusinessService.GetSettings();
		const answer = await CustomerAnswer.findBy('personId', params.id)
		let listAnswer = []
		listAnswer.push(answer)

		let today = new Date()
		let name = request.all().name
		let tradeId = request.all().login
		today.setDate(today.getDate() + 1)
		let end = request.all().endDate || moment(today).format('YYYY-MM-DD')
		today.setDate(today.getDate() - 1810)
		let begin = request.all().beginDate || moment(today).format('YYYY-MM-DD')
		let terminalCode = request.all().terminalCode
		let financeInstitute = request.all().financeInstitute
		let kycForce = request.all().kycForce == 'on' ? 'on' : 'off'
		let kycProcessed = request.all().kycProcessed == 'on' ? 'on' : 'off'

		currentQuery.name = name
		currentQuery.begin = begin
		currentQuery.end = end
		currentQuery.tradeId = tradeId
		currentQuery.terminalCode = terminalCode
		currentQuery.financeInstitute = financeInstitute
		currentQuery.kycForce = kycForce
		currentQuery.kycProcessed = kycProcessed

		let list = []
		list.push(customer)

		//her kayıtta dönüp ön tarafa manipüle edilen veriyi basıyoruz.
		list.forEach(element => {
			element.created_date = moment(element.created_date).format()
			element.balance = element.balance != null ? element.balance : 0
			element.utilityBill = element.utilityBill ? `${element.id}/${element.utilityBill}` : null
		});

		const instituteList = await Institute.query().fetch()
		fieldList[7].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[2].v = instituteList.toJSON().map(institute => {
			let id = institute.id;
			let title = institute.title;
			return { id, title }
		})
		queryFieldList[2].v.splice(0, 0, { id: 0, title: "ALL" })

		let listInfo = []
		for (const item of list) {
			let info = item.kycIdentificationDescription != null ? JSON.parse(item.kycIdentificationDescription) : {}
			info.id = item.id
			info.kycIdentificationRate = item.kycIdentificationRate
			info.kycIdCardFront = `${item.id}/${item.idCardFront}`
			info.kycIdCardBack = `${item.id}/${item.idCardBack}`
			info.kycIdCardFoto = `${item.id}/${item.idCardFoto}`
			info.kycIdCardSelfie = `${item.id}/${item.idCardSelfie}`
			listInfo.push(info)
		}

		for (let c of list) {
			c.hasAnswer = listAnswer.some(answer => answer.personId == c.id)
			c.kycUtilityBillDate = c.kycUtilityBillDate && moment(c.kycUtilityBillDate).format()
			c.kycAddressValidationDate = c.kycAddressValidationDate && moment(c.kycAddressValidationDate).format()
			c.kycIdentificationDate = c.kycIdentificationDate && moment(c.kycIdentificationDate).format()
			c.kycChatbotDate = c.kycChatbotDate && moment(c.kycChatbotDate).format()
			c.groupName = Common.Basic.customerGroupNames.filter(group => group.id == c.groupName).map(group => group.title)[0]
		}

		return view.render("pages.customer", {
			caption: caption,
			fields: fieldList,
			queryFields: queryFieldList,
			currentQuery: currentQuery,
			data: list,
			setting: setting,
			infoFields: infoFieldList,
			dataInfo: listInfo,
			questionFields: questionFieldList,
			dataAnswer: listAnswer
		})
	}

	async update({ request, response, session, params }) {
		try {
			let record = await Customer.find(params.id)
			record.name = request.all().name
			record.surname = request.all().surname
			//record.gender = request.all().gender
			record.email = request.all().email
			record.phone = request.all().phone
			record.avenue = request.all().avenue
			record.financeinstitute = parseInt(request.all().financeinstitute)

			//kyc NO dan YES e çekilirse NULL Tarihler güncellenecek.
			if (record.kycAddressValidation == 0 && parseInt(request.all().kycAddressValidation) == 1) {
				record.kycAddressValidationDate = new Date()
			}
			if (record.kycUtilityBill == 0 && parseInt(request.all().kycRiskCheck) == 1) {
				record.kycUtilityBillDate = new Date()
			}
			if (record.kycIdentification == 0 && parseInt(request.all().kycIdentification) == 1) {
				record.kycIdentificationDate = new Date()
			}
			if (record.kycChatbot == 0 && parseInt(request.all().kycChatbot) == 1) {
				record.kycChatbotDate = new Date()
			}

			record.kycAddressValidation = parseInt(request.all().kycAddressValidation)
			record.kycUtilityBill = parseInt(request.all().kycRiskCheck)
			record.kycIdentification = parseInt(request.all().kycIdentification)
			record.kycChatbot = parseInt(request.all().kycChatbot)

			record.depositLimit = parseInt(request.all().depositLimit)
			record.withdrawalLimit = parseInt(request.all().withdrawalLimit)

			await record.save()

			session.flash({ info: caption.updatedMessage })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async delete({ response, session, params }) {
		try {
			const record = await Customer.find(params.id);
			await record.delete();
			session.flash({ info: caption.deletedMessage })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async sendSms({ request, response, session, params }) {
		try {
			const setting = await BusinessService.GetSettings();
			const record = await Customer.find(params.id);

			const message = `Sichern Sie sich jetzt sofortigen Zugang zu unseren Dienstleistungen! Nutzen Sie einfach den Verifizierungslink unten: ${appUrl}kyc/${record.login}`

			SMS.sendSMS(setting.toJSON(), record.phone, 'Alternate', message);

			session.flash({ info: caption.smsSentMessage })
			return response.redirect("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async sendCustomSms({ request, response, session, params }) {
		try {
			const setting = await BusinessService.GetSettings();
			const record = await Customer.find(params.id);

			let message = request.all().sendCustomSmsMessage

			message = message.replace("[AppUrl]", appUrl)
			message = message.replace("[TraderID]", record.login)
			message = message.replace("[Name]", record.name + " " + record.surname)

			SMS.sendSMS(setting.toJSON(), record.phone, 'Alternate', message);

			session.flash({ info: caption.smsSentMessage })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	//@Experimental
	async sendMultiSms({ request, response, session, params }) {
		try {
			if (process.env.MULTI_SMS != 'true') {
				session.flash({ error: "MultiSMS Deactivated !" });
				return response.route("back")
			}

			let message = request.all().sendMultiSmsMessage

			const setting = await BusinessService.GetSettings();

			const recordList = await Database.select('person.*')
				.from('person')
				.where('person.phone', '!=', '')
				//.where('person.kycforce', 1)
				//.andWhere('person.kycAddressValidation', 0)
				.orderBy('person.id', 'desc');

			let phoneArray = []

			for (const record of recordList) {
				if (record.phone == '') continue;
				phoneArray.push(record.phone)
			}
			if (phoneArray.length > 0) {
				SMS.sendMultiSMS(setting.toJSON(), phoneArray, 'Alternate', message);
			}

			session.flash({ info: `All(${phoneArray.length}) SMS sent !` })
			return response.route("back")
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async updateInfo({ request, response, session, params }) {
		try {
			const record = await Customer.find(params.id);
			const res = request.all()

			const info = {
				isValid: (res.isValid == 'true'),
				frontIsValid: (res.frontIsValid == 'true'),
				backIsValid: (res.backIsValid == 'true'),
				front: {
					fullName: (res.frontFullName == 'true'),
					birthDate: (res.frontBirthDate == 'true'),
					nationality: (res.frontNationality == 'true'),
					gender: (res.frontGender == 'true')
				},
				back: {
					fullName: (res.backFullName == 'true'),
					birthDate: (res.backBirthDate == 'true'),
					nationality: (res.backNationality == 'true'),
					gender: (res.backGender == 'true')
				}
			}

			record.kycIdentificationDescription = JSON.stringify(info)
			record.kycIdentification = info.isValid ? 1 : 0

			await record.save()

			return response.json({ success: true, info: caption.infoUpdatedMessage });
		} catch (error) {
			return response.json({ success: false, info: error.message });
		}
	}

	async utilityBillUpload({ view, params, request, response, session }) {
		const personId = request.all().id
		const checkingCustomer = await Customer.find(personId);

		const utilityBill = request.file('utilityBillFile')

		try {
			if (checkingCustomer && utilityBill) {

				// Dosyanın kaydedilmesi ${Date.now()}
				const uploadPath = `public/images/uploads/${personId}`

				const utilityBillFileName = `ub_${utilityBill.clientName}`;
				utilityBill.clientName = utilityBillFileName
				await utilityBill.move(uploadPath, { overwrite: true });

				//resim isimlerini Customer'a ye yaz
				checkingCustomer.utilityBill = utilityBillFileName
				await checkingCustomer.save()

				session.flash({ info: caption.updatedMessage })
				return response.route("back");
			} else {
				return response.status(404).send('Not found')
			}
		} catch (error) {
			return response.status(500).send(error)
		}
	}

	async sendKycRiskCheck({ response, session, params }) {
		try {
			const customer = await Customer.find(params.id);
			const setting = await BusinessService.GetSettings();

			const resultMessage = await this.checkRiskFromKycApi(setting, customer)

			session.flash({ info: resultMessage })
			return response.route("back");
		} catch (error) {
			session.flash({ error: error.message })
			return response.route("home")
		}
	}

	async checkRiskFromKycApi(setting, customer) {
		let message = "KYC Api Authentication Failed !";

		//KYC Spyder API RiskCheck
		if (customer.kycUtilityBill == 1) {
			message = "Pass Before from KycRiskControl: DB Yes";
		} else {
			const kc = await KYC.Kyc_GetKeyChallange(setting)
			if (kc && kc.Challenge && kc.Key) {
				const auth = await KYC.Kyc_Auth(setting, kc)
				if (auth && auth.data == "okay") {
					message = "Customer data not found !"
					const record = await Customer.find(customer.id)
					if (!record) {
						return message
					}

					const reference = `${customer.login}`;
					const customerExist = await KYC.Kyc_GetCustomerReference(kc, reference)
					if (!customerExist) {
						const crr = await KYC.Kyc_CreateCustomer(kc, customer);
						if (crr && crr.Datas && crr.Datas.length > 0) {
							for (const item of crr.Datas) {
								if (item.customerReference == reference) {
									message = `KYC GetCustomerReference: ${item.customerReference} - Created`;
								}
							}
						}
					} else {
						message = `KYC GetCustomerReference: ${reference} - AlreadyExist`;
					}
					console.info(message);

					const cr = await KYC.Kyc_CheckCustomer(kc, reference);
					if (cr && cr.Datas && cr.Datas.length > 0) {
						for (const item of cr.Datas) {
							if (item.riskState == "NO_RISKS_FOUND") {
								kycResult = true;
								record.kycUtilityBill = 1;
							}
							message = `KYC CheckCustomerRisk: ${item.customerReference} - ${item.riskState}`;
						}
					} else {
						message = `KYC CheckCustomerRisk: ${reference} - NO_DATA`;
					}
					console.info(message);
				}
			}
		}

		return message
	}

}

module.exports = CustomerController
