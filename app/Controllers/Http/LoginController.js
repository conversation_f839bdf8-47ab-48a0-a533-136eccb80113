'use strict'

const BusinessService = use("App/Helpers/BusinessService");
const { validate } = use('Validator')

const caption = {
    navLogin: "active",
    pageTitle: "User Informations",
    icon: 'fas fa-user fa-fw',
}

class LoginController {

    async index({ view, params }) {
        const setting = await BusinessService.GetSettings();

        if (setting.appType == 'panel') {
            return view.render("pages.login", {
                title: "Authentication",
                emailCaption: "Enter your Email",
                passwordCaption: "Enter your Password",
                buttonCaption: "Login",
                caption: caption,
                data: null,
                setting: setting,
            })
        }
        else if (setting.appType == 'report') {
            return view.render("reports.login", {
                title: "Authentication",
                emailCaption: "Enter your Email",
                passwordCaption: "Enter your Password",
                buttonCaption: "Login",
                caption: caption,
                data: null,
                setting: setting,
            })
        }


    }

    async check({ request, response, session, auth }) {
        const validation = await validate(request.all(), {
            email: "required",
            pass: "required|min:6"
        })

        if (validation.fails()) {
            session.withErrors(validation.messages()).flashAll()
            return response.redirect("back")
        }

        session.flash({ alert: "Your entered infos aren't match with database!" })

        await auth.attempt(request.input("email"), request.input("pass"))

        //await auth.login(user); sadece kullanıcıyı login etmek istersek

        session.flash({ info: "Welcome User " + auth.user.username })
        return response.route("home")
    }

    async destroy({ response, auth }) {
        await auth.logout()
        return response.route("login")
    }
}

module.exports = LoginController
