'use strict'

/* MetaTrader Web API 
Aşağıdaki URL'lerdeki apiye erişimde kullanılır.
https://grpc-mtrwl.match-trader.com
https://manager-api-mt.match-trade.com
*/

const axios = require("axios")

const moment = require("moment")
moment.defaultFormat = "DD.MM.YYYY HH:mm"

class Forex724Api {

  constructor(institute) {
    this.institute = institute
    this.ledgerTypes = [4, 6]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8
  }

  async getToken() {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/register/register`,
        {
          managerID: institute.restApiUser,
          password: institute.restApiPassword
        }
      )

      if (!response || !response.data || !response.data.token) {
        return undefined
      }

      return response.data.token

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async getAccounts(authToken) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/account/getAllForManager`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          }
        }
      )

      if (!response || !response.data || !response.data.accountInfo) {
        return undefined
      }

      return response.data.accountInfo
    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async getBalance(authToken, rangeStart, rangeEnd, clientIds) {
    try {
      const institute = this.institute
      const ledgerTypes = this.ledgerTypes
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/ledger/getEntries`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          rangeStart: rangeStart,
          rangeEnd: rangeEnd,
          clientIds: clientIds,
          ledgerTypes: ledgerTypes
        }
      )

      if (!response || !response.data || !response.data.ledgerEntry) {
        return undefined
      }

      return response.data.ledgerEntry

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async setDeposit(authToken, youLogin, amount, comment) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/depositMoney`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          comment: comment,
          amount: amount * 100,
          clientId: youLogin,
          additionalType: 0
        }
      )

      if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
        const transactionId = response.data.ledgerUid
        return transactionId
      }

      console.log(response)

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async setCreditIn(authToken, customerLogin, amount, comment) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/creditIn`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          comment: comment,
          amount: amount * 100,
          clientId: customerLogin,
          additionalType: 0
        }
      )

      if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
        const transactionId = response.data.ledgerUid
        return transactionId
      }

      console.log(response)
    } catch (error) {
      console.log(error)
    }

    return undefined
  }

}

class FxcentrumApi {

  constructor(institute) {
    this.institute = institute
    this.ledgerTypes = [4, 6]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8
  }

  async getToken() {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/register/register`,
        {
          managerID: institute.restApiUser,
          password: institute.restApiPassword
        }
      )

      if (!response || !response.data || !response.data.token) {
        return undefined
      }

      return response.data.token

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async getAccounts(authToken) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/account/getAllForManager`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          }
        }
      )

      if (!response || !response.data || !response.data.accountInfo) {
        return undefined
      }

      return response.data.accountInfo
    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async getBalance(authToken, rangeStart, rangeEnd, clientIds) {
    try {
      const institute = this.institute
      const ledgerTypes = this.ledgerTypes
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/ledger/getEntries`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          rangeStart: rangeStart,
          rangeEnd: rangeEnd,
          clientIds: clientIds,
          ledgerTypes: ledgerTypes
        }
      )

      if (!response || !response.data || !response.data.ledgerEntry) {
        return undefined
      }

      return response.data.ledgerEntry

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async setDeposit(authToken, youLogin, amount, comment) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/depositMoney`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          comment: comment,
          amount: amount * 100,
          clientId: youLogin,
          additionalType: 0
        }
      )

      if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
        const transactionId = response.data.ledgerUid
        return transactionId
      }

      console.log(response)

    } catch (error) {
      console.log(error)
    }

    return undefined
  }

  async setCreditIn(authToken, customerLogin, amount, comment) {
    try {
      const institute = this.institute
      const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/creditIn`,
        {
          auth: {
            managerID: institute.restApiUser,
            token: authToken
          },
          comment: comment,
          amount: amount * 100,
          clientId: customerLogin,
          additionalType: 0
        }
      )

      if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
        const transactionId = response.data.ledgerUid
        return transactionId
      }

      console.log(response)
    } catch (error) {
      console.log(error)
    }

    return undefined
  }

}

module.exports = { Forex724Api, FxcentrumApi }
