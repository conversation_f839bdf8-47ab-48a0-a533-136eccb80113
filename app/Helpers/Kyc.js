'use strict'

const BusinessService = use("App/Helpers/BusinessService");

const axios = require("axios")
const crypto = require('crypto');
const moment = require("moment");

class Kyc {

    example(setting, recipient, originator, message) {
        axios.post(setting.sms_url, {
            ForceGSM7bit: true
        })
            .then((response) => {
                console.log(response.data);
            })
            .catch((error) => {
                console.log(error);
            });
    }

    static async Kyc_GetKeyChallange(setting) {
        let result = undefined

        const URL = `${setting.kycKeyUrl}` //https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/challenge"
        await axios.get(URL)
            .then((response) => {
                result = { Key: response.data.key, Challenge: response.data.challenge }
            }).catch((error) => {
                console.log(error);
            });

        return result
    }

    static Kyc_HashSha1(plainText) {
        /*c# kodu
        var sha = new SHA1CryptoServiceProvider();
        return BitConverter.ToString(sha.ComputeHash(Encoding.UTF8.GetBytes(plainText))).Replace("-", "").ToLower();
        */

        const sha = crypto.createHash('sha1');
        sha.update(plainText, 'utf8');
        const shaData = sha.digest('hex');
        return shaData
    }

    static async Kyc_Auth(setting, kc) {
        let result = undefined

        const mandator = setting.kycMandator; //"telejetcomag";
        const password = setting.kycPass; //"zflu78_3?nmaH1";
        const user = setting.kycUser; //"api";
        const plainResponse = kc.Key + mandator + user + password + kc.Challenge; //PLAIN_RESPONSE = KEY + MANDATOR + USER + PASSWORD + CHALLENGE
        const shaData = this.Kyc_HashSha1(plainResponse);

        const DATA = {
            key: kc.Key,
            mandator: mandator,
            user: user,
            response: shaData
        }

        const URL = `${setting.kycAuthUrl}` //"https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/authenticate";
        await axios.post(URL, DATA)
            .then((response) => {
                result = response
            }).catch((error) => {
                console.log(error);
            });

        return result
    }

    static async Kyc_GetCustomerReference(kc, reference) {
        let result = false

        const URL = `https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/customers/${reference}`
        await axios.get(URL, {
            headers: {
                'Session-Key': kc.Key
            }
        }).then((response) => {
            result = true
        }).catch((error) => {
            console.log(error);
        });

        return result
    }

    static async Kyc_CreateCustomer(kc, record) {
        let result = undefined
        const birthDate = new Date(moment(record.dateofbirth).format("DD.MM.YYYY"))
        const DATA = {
            reference: record.login.toString(),
            type: "PERSON",
            names: [{ firstName: record.name, lastName: record.surname }],
            datesOfBirth: [{
                year: birthDate.getFullYear(),
                month: birthDate.getMonth() + 1, // Months are 0-indexed
                day: birthDate.getDate()
            }],
            citizenship: [BusinessService.FindShortCountry(record.nationality)], // Assuming a function to find short country code
            countriesOfResidence: ["CH"],
            emails: [record.email],
            telephones: [record.phone.replace(/\+/, "")],
            addresses: [], // Assuming addresses are not currently provided
            structuredAddresses: [{
                type: "BASIC",
                street: record.avenue,
                city: record.city,
                countryCode: record.country,
                zipCode: record.postcode
            }],
            gender: record.gender === "M" ? "MALE" : record.gender === "F" ? "FEMALE" : "UNKNOWN",
            title: "",
            prefferedLanguage: "de"
        }

        const URL = "https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/customers/simple"
        await axios.post(URL, DATA, {
            headers: {
                'Session-Key': kc.Key,
            }
        }).then((response) => {
            result = { State: true, Message: response, Datas: [response.data] };
        }).catch((error) => {
            console.log(error);
        });

        return result
    }

    static async Kyc_CheckCustomer(kc, reference) {
        let result = undefined

        const DATA = [reference]
        const URL = `https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/customers/check`
        await axios.post(URL, DATA, {
            headers: {
                'Session-Key': kc.Key
            }
        }).then((response) => {
            result = { State: true, Message: response, Datas: response.data };
        }).catch((error) => {
            console.log(error);
        });

        return result
    }

    static async Kyc_CheckCustomerAddress(kc, reference) {
        let result = undefined

        const DATA = [reference]
        const URL = `https://kyc.eurospider.com/kyc-v8-api/rest/3.0.0/customers/check-addresses`
        await axios.post(URL, DATA, {
            headers: {
                'Session-Key': kc.Key
            }
        }).then((response) => {
            result = { State: true, Message: response, Datas: response.data };
        }).catch((error) => {
            console.log(error);
        });

        return result
    }

}

module.exports = Kyc
