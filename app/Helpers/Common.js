'use strict'

/*
|--------------------------------------------------------------------------
| Basic Variables
|--------------------------------------------------------------------------
*/
const genders = [
    { id: 'U', title: 'U' },
    { id: 'M', title: 'M' },
    { id: 'F', title: 'F' },
]

const yesNo = [
    { id: 0, title: "No" },
    { id: 1, title: "Yes" },
]

//Live S -> /20*30
//Live M -> /13.4*20
//Live L -> /6.7*10
const customerGroupNames = [
    { id: 'demoEQFusd-B1', title: 'Demo - USD', commissionRateX: 0, commissionRateY: 0 },
    { id: 'demoEQFchf-B2', title: 'Demo - CHF', commissionRateX: 0, commissionRateY: 0 },
    { id: 'realEQFusd-B1', title: 'Live S - USD', commissionRateX: 0, commissionRateY: 0 },
    { id: 'realEQFusd-B2', title: 'Live M - USD', commissionRateX: 0, commissionRateY: 0 },
    { id: 'realEQFusd-B3', title: 'Live L - USD', commissionRateX: 0, commissionRateY: 0 },
    { id: 'realEQFchf-B4', title: 'Live S - CHF', commissionRateX: 200, commissionRateY: 300 },
    { id: 'realEQFchf-B5', title: 'Live M - CHF', commissionRateX: 134, commissionRateY: 200 },
    { id: 'realEQFchf-B6', title: 'Live L - CHF', commissionRateX: 67, commissionRateY: 100 },
    { id: 'demoWTGchf-B1', title: 'Demo - CHF', commissionRateX: 0, commissionRateY: 0 },
    { id: 'realWTGchf-A1', title: 'Live S - CHF', commissionRateX: 200, commissionRateY: 300 },
    { id: 'realWTGchf-A2', title: 'Live M - CHF', commissionRateX: 134, commissionRateY: 200 },
    { id: 'realWTGchf-A3', title: 'Live L - CHF', commissionRateX: 67, commissionRateY: 100 },
]

const customerSourceNames = [
    { id: 0, title: 'No Source' },
    { id: 1, title: 'Web' },
    { id: 2, title: 'Call Center' },
]

const userRoles = [
    { id: 1, title: 'User' },
    { id: 2, title: 'Manager' },
    { id: 3, title: 'Admin' },
    { id: 11, title: 'IB' },
    { id: 12, title: 'Call Center' },
    { id: 13, title: 'SAG' },
    { id: 21, title: 'CRM' },
]

const transactionTypes = [
    { id: 4, title: 'DEPOSIT' },
    { id: 5, title: 'WITHDRAWAL' },
]

const transactionGateways = [
    { id: "Bank", title: 'Bank' },
    { id: "MTC", title: 'MTC' },
    { id: "Crypto", title: 'Crypto' },
    { id: "UNKNOWN", title: 'UNKNOWN' },
]

const commentTypes = [
    { id: 1, title: "Normal" },
    { id: 2, title: "Contest" },
]

const Basic = {
    genders,
    yesNo,
    customerGroupNames,
    customerSourceNames,
    userRoles,
    transactionTypes,
    transactionGateways,
    commentTypes,
}

/*
|--------------------------------------------------------------------------
| KYC Variables
|--------------------------------------------------------------------------
*/
const kycAnswers1 = [
    { id: 'voucherkarten', title: 'Einkauf Voucher-Karten' },
    { id: 'telefonkarten', title: 'Einkauf Telefonkarten' },
    { id: 'zahlungsverkehr', title: 'Zahlungsverkehrsdienstleistungen' },
]

const kycAnswers2 = [
    { id: 'unter_2500', title: 'Unter 2‘500' },
    { id: 'uber_2500', title: 'Über 2‘500' },
    { id: 'uber_10000', title: 'Über 10‘000' },
]

const kycAnswers3 = [
    { id: 'weniger_10000', title: 'weniger als 10’000' },
    { id: '10000_30000', title: '10’001 bis 30’000' },
    { id: '30000_100000', title: '30’000 bis 100’000' },
    { id: '100000_300000', title: '100’001 bis 300’000' },
    { id: '300000_1000000', title: '300’001 bis 1’000’000' },
]

const kycAnswers4 = [
    { id: 'espartes', title: 'Erspartes' },
    { id: 'genossenschaft', title: 'Geschäftstätigkeit' },
    { id: 'erbschaft', title: 'Erbschaft' },
    { id: 'andere_quellen', title: 'andere Quellen' },
]

const kycAnswers5 = [
    { id: 'selbst', title: 'Ich handle für mich selbst' },
    { id: 'andere', title: 'Ich handle im Namen einer anderen Person' },
    { id: 'gesellschaft', title: 'Ich handle im Namen einer Gesellschaft' },
    { id: 'unternehmen', title: 'Ich handle im Namen meines Unternehmens' },
    { id: 'keine', title: 'Keine der obigen Möglichkeiten' },
]

const kycAnswers6 = [
    { id: 'mitarbeiter', title: 'Mitarbeiter' },
    { id: 'kader', title: 'Mittleres Kader' },
    { id: 'fuehrer', title: 'Führungskraft' },
    { id: 'selbststaetig', title: 'Selbstständiger' },
    { id: 'nicht_erwerbstaetig', title: 'Nicht erwerbstätig' },
    { id: 'andere', title: 'Andere' },
]

const kycAnswers10 = [
    { id: 'ja', title: 'Ja' },
    { id: 'nein', title: 'Nein' },
]

const kycAnswers11 = [
    { id: 'weniger_10000', title: 'weniger als 10’000' },
    { id: '10000_30000', title: '10’001 bis 30’000' },
    { id: '30000_100000', title: '30’000 bis 100’000' },
    { id: '100000_300000', title: '100’001 bis 300’000' },
    { id: 'uber_300000', title: 'über 300’000' },
]

const kycAnswers12 = [
    { id: 'weniger_10000', title: 'weniger als 10’000' },
    { id: '10000_30000', title: '10’001 bis 30’000' },
    { id: '30000_100000', title: '30’000 bis 100’000' },
    { id: '100000_300000', title: '100’001 bis 300’000' },
    { id: '300000_1000000', title: '300’001 bis 1’000’000' },
    { id: 'uber_1000000', title: 'über 1’000’000' },
]

const kycQuestions = {
    question1: 'Was ist der Zweck der mit Tele Jet Com AG einzugehenden Geschäftsbeziehung?',
    question2: 'Wie hoch ist der gesamte Betrag in CHF, den Sie jetzt investieren möchten?',
    question3: 'In Bezug auf die Entwicklung der Geschäftsbeziehung mit Tele Jet Com AG: Wie viel planen Sie in Zukunft zu investieren oder überweisen (in CHF)?',
    question4: 'Woher stammen diese Gelder?',
    question5: 'Handeln Sie in Ihrem eigenen Namen oder im Namen einer anderen Person oder einer Gesellschaft?',
    question6: 'Welche Rolle/Funktion nehmen Sie bei Ihrer beruflichen Tätigkeit wahr?',
    question7: 'Wie lautet der Name Ihres Arbeitgebers?',
    question8: 'Wie lautet die Adresse von Ihrem Arbeitgeber?',
    question9: 'In welcher Branche ist Ihr Arbeitgeber tätig?',
    question10: 'Sind Sie involviert im Handel mit Munition und Waffen, rohen Edelsteinen / Diamanten, Schmuck, in internationalem Handel mit exotischen Tieren, in Casino- und Lotteriegeschäften oder im Erotikgewerbe?',
    question11: 'Bitte bestätigen Sie die Höhe Ihres jährlichen Einkommens (in CHF).',
    question12: 'Bitte bestätigen Sie die Höhe Ihres Vermögens (in CHF).',
    question13: 'Bitte spezifizieren Sie die Höhe Ihrer Schulden (in CHF ohne Hypotheken)',
    question14: 'Können Sie über die eingebrachten Gelder uneingeschränkt verfügen?',
}

const KYC = {
    kycQuestions,
    kycAnswers1,
    kycAnswers2,
    kycAnswers3,
    kycAnswers4,
    kycAnswers5,
    kycAnswers6,
    kycAnswers10,
    kycAnswers11,
    kycAnswers12,
    answerTranslate: (answerIdList) => {
        return {
            answer1: kycAnswers1.find(item => item.id == answerIdList.answer1).title,
            answer2: kycAnswers2.find(item => item.id == answerIdList.answer2).title,
            answer3: kycAnswers3.find(item => item.id == answerIdList.answer3).title,
            answer4: kycAnswers4.find(item => item.id == answerIdList.answer4).title,
            answer5: kycAnswers5.find(item => item.id == answerIdList.answer5).title,
            answer6: kycAnswers6.find(item => item.id == answerIdList.answer6).title,
            answer7: answerIdList.answer7,
            answer8: answerIdList.answer8,
            answer9: answerIdList.answer9,
            answer10: kycAnswers10.find(item => item.id == answerIdList.answer10).title,
            answer11: kycAnswers11.find(item => item.id == answerIdList.answer11).title,
            answer12: kycAnswers12.find(item => item.id == answerIdList.answer12).title,
            answer13: kycAnswers12.find(item => item.id == answerIdList.answer13).title,
            answer14: kycAnswers10.find(item => item.id == answerIdList.answer14).title,
        }
    }
}


/*
|--------------------------------------------------------------------------
| Report Variables
|--------------------------------------------------------------------------
*/

const Report = {
    mainPeriods: [
        { id: 0, title: "None", en: "None" },
        { id: 1, title: "Alle", en: "All" },
        { id: 2, title: "Heute", en: "Today" },
        { id: 3, title: "Diese Woche", en: "This Week" },
        { id: 4, title: "Letzten Monat", en: "Last Month" },
        { id: 5, title: "Letzte 6 Monate", en: "Last 6 Months" },
        { id: 6, title: "Laufendes Jahr", en: "This Year" },
        { id: 7, title: "Vorjahr", en: "Last Year" },
    ],
    subPeriods: [
        { id: 8, title: "Januar", en: "January" },
        { id: 9, title: "Februar", en: "February" },
        { id: 10, title: "März", en: "March" },
        { id: 11, title: "April", en: "April" },
        { id: 12, title: "Mai", en: "May" },
        { id: 13, title: "Juni", en: "June" },
        { id: 14, title: "Juli", en: "July" },
        { id: 15, title: "August", en: "August" },
        { id: 16, title: "September", en: "September" },
        { id: 17, title: "Oktober", en: "October" },
        { id: 18, title: "November", en: "November" },
        { id: 19, title: "Dezember", en: "December" },
    ],
}

module.exports = { Basic, KYC, Report }
