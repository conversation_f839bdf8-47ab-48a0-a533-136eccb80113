'use strict'

const BankApi = use('App/Helpers/BankApi');

class TransactionService {

  constructor(institute) {
    this.institute = institute

    if (institute.companyName == 'Forex724') {
      this.transactionAdapter = new BankApi.Forex724Api(institute)
    } else if (institute.companyName == 'FXC') {
      this.transactionAdapter = new BankApi.FxcentrumApi(institute)
    }
  }

  async getAccounts() {
    const authToken = await this.transactionAdapter.getToken()
    return await this.transactionAdapter.getAccounts(authToken)
  }

  async getBalance(rangeStart, rangeEnd, clientIds) {
    const authToken = await this.transactionAdapter.getToken()
    return await this.transactionAdapter.getBalance(authToken, rangeStart, rangeEnd, clientIds)
  }

  async setDeposit(youLogin, amount, comment) {
    const authToken = await this.transactionAdapter.getToken()
    return await this.transactionAdapter.setDeposit(authToken, youLogin, amount, comment)
  }

  async setCreditIn(customerLogin, amount, comment) {
    const authToken = await this.transactionAdapter.getToken()
    return await this.transactionAdapter.setCreditIn(authToken, customerLogin, amount, comment)
  }
}

module.exports = TransactionService
