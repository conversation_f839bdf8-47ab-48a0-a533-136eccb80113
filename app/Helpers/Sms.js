'use strict'

const axios = require("axios")

function sendSMS(setting, recipient, originator, message) {
    const sender = originator === 'Default' ? setting.sms_originator : setting.sms_originator_alternate;

    axios.post(setting.sms_url, {
        UserName: setting.sms_user,
        Password: setting.sms_pass,
        Originator: sender,
        Recipients: [`${recipient}`],
        MessageText: message,
        ForceGSM7bit: true
    })
        .then(function (response) {
            console.log(response.data);
        })
        .catch(function (error) {
            console.log(error);
        });
}

function sendMultiSMS(setting, recipients, originator, message) {
    const sender = originator === 'Default' ? setting.sms_originator : setting.sms_originator_alternate;

    axios.post(setting.sms_url, {
        UserName: setting.sms_user,
        Password: setting.sms_pass,
        Originator: sender,
        Recipients: recipients,
        MessageText: message,
        ForceGSM7bit: true
    })
        .then(function (response) {
            console.log(response.data);
        })
        .catch(function (error) {
            console.log(error);
        });
}

module.exports = { sendSMS, sendMultiSMS }
