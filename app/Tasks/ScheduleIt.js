'use strict'

const Common = use('App/Helpers/Common');

const SMS = use('App/Helpers/Sms');
const KYC = use('App/Helpers/Kyc');
const BusinessService = use("App/Helpers/BusinessService");

const Task = use('Task')
const Database = use("Database")
const FinanceInstitute = use("App/Models/Institute")
const Customer = use("App/Models/Customer")
const WinnerPhone = use("App/Models/WinnerPhone")

const axios = require("axios")
const querystring = require("querystring");
const crypto = require("crypto");

const moment = require("moment")
moment.defaultFormat = "DD.MM.YYYY HH:mm"

const appUrl = process.env.APP_URL

class ScheduleIt extends Task {

	static get schedule() {
		//https://crontab.guru/every-minute
		return '*/10 * * * *' //MIN HOUR DAY-Of-MONTH MONTH DAY-OF-WEEK
		// return '* * * * *' //every minutes
	}

	static get useLock() {
		return true
	}

	async handle() {
		if (process.env.SCHEDULE_ACTIVE == 'true') {
			try {
				let today = new Date()
				const setting = await BusinessService.GetSettings();

				//TestModundaysa sürekli kycSmsCheck yapmasın
				if (!setting) {
					return
				}

				if (setting.test_mode == 1) {
					return
				}

				const rawInstitutes = await FinanceInstitute.query().where('active', 1).andWhere('moneyTransferActive', 1).orderBy('id', 'desc').fetch()
				const institutes = rawInstitutes.toJSON()

				await this.sync(today, setting, institutes)
				await this.invitationCheck(today, setting, institutes)
				await this.winnerCreditCheck(today, setting, institutes)
				await this.kycSmsCheck(today, setting, institutes)
				/* await this.kycRiskAndAddressCheck(today, setting, institutes)*/ //Kapatıldı
				await this.kycUpdateSameEmailAccount(today, setting, institutes)

			} catch (error) {
				this.error({ message: 'Setting Error:' + error + ' - ' + today });
			}
		}
	}

	//SCHEDULE FUNCTIONS

	async sync(today, setting, institutes) {
		try {
			for (const institute of institutes) {
				if (institute.restApiBaseUrl != "" && institute.restApiUser != "" && institute.restApiPassword != "") {
					let manipulatedCustomers = []

					//tradecenter MysqlDB ye SYNC
					const accounts = await this.subSyncTraderWebApi(institute, manipulatedCustomers)

					//equityfinance CRM API ye SYNC (veribis_webApi Kobikom)
					// await this.subSyncKobikomCrm(institute, manipulatedCustomers, accounts)

					//Suite CRM on crm.tradecenter24.com
					await this.subSyncTradecenterCrm(institute, manipulatedCustomers, accounts)

					this.info({ message: institute.title + ' Customer is synced - ' + today });
				}
			}

		} catch (error) {
			this.error({ message: 'Sync Error:' + error + ' - ' + today });
		}
	}

	async invitationCheck(today, setting, institutes) {
		try {
			const moneyFirst = setting.invitationMoney //20
			const moneyFive = setting.invitationMoneyFive //50
			const moneyFifty = setting.invitationMoneyFifty //500

			for (const institute of institutes) {
				if (institute.restApiBaseUrl != "" && institute.restApiUser != "" && institute.restApiPassword != "") {

					const authToken = await this.subGetTokenFromTraderWebApi(institute)

					//ilk hakediş davet edilenin payFirst alanına 1 yazılır
					const customers = await Database.select('person.*')
						.from('person')
						.where(function () {
							this.andWhere('person.invitorId', '>', 0)
							this.andWhere('person.payFirst', 0)
							this.andWhereNot('person.balance', 0)
						})

					for (const customer of customers) {
						const friend = await Customer.find(customer.id)
						const you = await Customer.find(customer.invitorId)

						let comment = 'Invitation for ID: ' + customer.login
						const resDepositFirst = await this.subSetDepositFromTraderApi(institute, authToken, you.login, customer.login, moneyFirst, comment)

						if (resDepositFirst === true) {
							friend.payFirst = 1
							await friend.save()
						} else {
							this.error({ message: 'Deposit API Error !' });
						}

						const friends = await Customer.query()
							.where('invitorId', you.id)
							.andWhereNot('balance', 0)
							.select('*')
							.fetch()
						const friendList = friends.toJSON()

						//ilk 5 kişi için hakediş davetçinin payFive alanına 1 yazılır
						if (friendList.length >= 5 && you.payFive == 0) {
							comment = '5. Invitation for ID: ' + customer.login
							const resDepositFive = await this.subSetDepositFromTraderApi(institute, authToken, you.login, customer.login, moneyFive, comment)

							if (resDepositFive === true) {
								you.payFive = 1
								await you.save()
							} else {
								this.error({ message: 'Deposit API Error !' });
							}
						}

						//ilk 50 kişi için hakediş davetçinin payFifty alanına 1 yazılır
						if (friendList.length >= 50 && you.payFifty == 0) {
							comment = '50. Invitation for ID: ' + customer.login
							const resDepositFifty = await this.subSetDepositFromTraderApi(institute, authToken, you.login, customer.login, moneyFifty, comment)

							if (resDepositFifty === true) {
								you.payFifty = 1
								await you.save()
							} else {
								this.error({ message: 'Deposit API Error !' });
							}
						}

					}

					this.info({ message: institute.title + ' Invitator Check Finished - ' + today });
				}
			}

		} catch (error) {
			this.error({ message: 'Invitation Check Error:' + error + ' - ' + today });
		}
	}

	async winnerCreditCheck(today, setting, institutes) {
		try {
			const activeInstitutesIdList = institutes.map(institute => institute.id)

			const winnerPhones = await Database.select('winner_phone.*')
				.from('winner_phone')
				.where(function () {
					this.andWhere('winner_phone.creditIn', 0)
				})

			for (const winnerPhone of winnerPhones) {
				const customers = await Customer.query()
					.whereIn('financeinstitute', activeInstitutesIdList)
					.andWhere('phone', winnerPhone.phone)
					.andWhere('created_date', '>=', winnerPhone.created_date)
					.orderBy('id', 'desc')
					.fetch()

				const firstCustomer = customers.toJSON()[0]

				if (firstCustomer != null) {
					const institute = institutes.find(x => x.id == firstCustomer.financeinstitute)

					if (institute.restApiBaseUrl != "" && institute.restApiUser != "" && institute.restApiPassword != "") {

						const authToken = await this.subGetTokenFromTraderWebApi(institute)

						//creditIn yapılacak
						let comment = 'Winner Credit for ID: ' + firstCustomer.login
						const credit = winnerPhone.credit

						//bu istek başarılı olup olmama durumuna bakılmalı mı emin değilim
						const winnerCreditIn = await this.subSetCreditInFromTraderApi(institute, authToken, firstCustomer.login, credit, comment)

						const record = await WinnerPhone.find(winnerPhone.id)
						record.creditIn = 1
						record.login = firstCustomer.login

						await record.save()
					}
				}
			}

			this.info({ message: 'Winner Credit Check Finished - ' + today });
		} catch (error) {
			this.error({ message: 'Winner Credit Check Error:' + error + ' - ' + today });
		}
	}

	async kycSmsCheck(today, setting, institutes) {
		try {
			const excludeLogins = [44308, ...institutes.map(x => Number(x.caseLoginId)), ...institutes.map(x => Number(x.withdrawLoginId))];
			const customers = await Database.select('person.*')
				.from('person')
				.where(function () {
					this.andWhere('person.phone', '!=', '')
					this.whereNotIn('person.login', excludeLogins)
					this.andWhere('person.kycForce', 1)
					this.andWhere('person.kycIdentification', 0)
				})

			await this.subSendKycSMS(setting, customers)

			await this.subCheckRiskAndAddressFromKyc(setting, customers)

			this.info({ message: 'KYC SMS Check Finished - ' + today });
		} catch (error) {
			this.error({ message: 'KYC SMS Check Error:' + error + ' - ' + today });
		}
	}

	//Deactivated
	async kycRiskAndAddressCheck(today, setting, institutes) {
		try {
			const excludeLogins = [44308, ...institutes.map(x => Number(x.caseLoginId)), ...institutes.map(x => Number(x.withdrawLoginId))];
			const customers = await Database.select('person.*')
				.from('person')
				.where(function () {
					this.andWhere('person.phone', '!=', '')
					this.whereNotIn('person.login', excludeLogins)
					this.whereNotNull('person.kycChatbotDate')
				})

			await this.subCheckRiskAndAddressFromKyc(setting, customers)

			this.info({ message: 'KYC SMS Check Finished - ' + today });
		} catch (error) {
			this.error({ message: 'KYC RiskAndAddress Check Error:' + error + ' - ' + today });
		}
	}

	//kontrol edilecek
	async kycUpdateSameEmailAccount(today, setting, institutes) {
		try {
			const customers = await Database.select('person.*')
				.from('person')
				.where(function () {
					this.andWhere('person.kycForce', 1)
					this.andWhere('person.email', '!=', '')
				})

			for (const customer of customers) {
				const rawRecords = await Customer.query().where(function () {
					this.where('email', customer.email)
					this.andWhere('id', '!=', customer.id)
				}).fetch()

				if (rawRecords.rows.length > 1) {
					for (const account of rawRecords.toJSON()) {
						const record = await Customer.find(account.id)

						record.kycIdentification = customer.kycIdentification
						record.kycAddressValidation = customer.kycAddressValidation
						record.kycUtilityBill = customer.kycUtilityBill
						record.kycChatbot = customer.kycChatbot

						//KYC Detaylarıda güncelle
						record.kycIdentificationDescription = customer.kycIdentificationDescription
						record.kycIdentificationRate = customer.kycIdentificationRate
						record.kycIdCardFront = customer.kycIdCardFront
						record.kycIdCardBack = customer.kycIdCardBack
						record.kycIdCardFoto = customer.kycIdCardFoto
						record.kycIdCardSelfie = customer.kycIdCardSelfie
						record.kycIdentificationDate = customer.kycIdentificationDate
						record.kycChatbotDate = customer.kycChatbotDate
						record.kycAddressValidationDate = customer.kycAddressValidationDate
						record.kycUtilityBillDate = customer.kycUtilityBillDate
						record.utilityBill = customer.utilityBill
						record.signature = customer.signature

						await record.save()
					}
				}
			}

			this.info({ message: 'Auto KYC Update Same Email Finished - ' + today });
		} catch (error) {
			this.error({ message: 'KYC Update Same Email Error:' + error + ' - ' + today });
		}
	}

	//KYC Spider API

	async subSendKycSMS(setting, customers) {
		for (const customer of customers) {
			let message = ""

			//KYC için SMS gonderme
			if (customer.kycSms == 0) {
				//bu mesajları db den almak lazım
				const smsMessage = `Lieber Kunde, bitte verifizieren Sie Ihr Konto - gesetzlich vorgeschrieben und zu Ihrem Schutz. Bereithalten: 1. ID oder Pass 2. Adressnachweis (z. B. Stromrechnung)
Starten Sie mit dem Link und verwenden Sie Ihre Handykamera. ${appUrl}kyc/${customer.login} Vielen Dank, Tele Jet AG`

				SMS.sendSMS(setting.toJSON(), customer.phone, 'Alternate', smsMessage);

				message = `KYC Send SMS: ${customer.login} - Sended - ${customer.phone}`;
				console.info(message);

				const record = await Customer.find(customer.id)
				if (record) {
					record.kycSms = 1
					await record.save()

					message = `KYC Send SMS: ${customer.login} - Saved - KycSms=1`;
					console.info(message);
				}
			}

			console.log(message);
		}
	}

	async subCheckRiskAndAddressFromKyc(setting, customers) {
		for (const customer of customers) {
			let message = ""

			//KYC Spyder API RiskCheck, AddressCheck
			if (customer.kycUtilityBill == 1) {
				message = "Pass Before from KycRiskControl: DB Yes";
			} else {
				let kycResult = false

				const kc = await KYC.Kyc_GetKeyChallange(setting)
				if (kc && kc.Challenge && kc.Key) {
					const auth = await KYC.Kyc_Auth(setting, kc)
					if (auth && auth.data == "okay") {
						const record = await Customer.find(customer.id)
						if (!record) {
							continue
						}

						const reference = `${customer.login}`;
						const customerExist = await KYC.Kyc_GetCustomerReference(kc, reference)
						if (!customerExist) {
							const crr = await KYC.Kyc_CreateCustomer(kc, customer);
							if (crr && crr.Datas && crr.Datas.length > 0) {
								for (const item of crr.Datas) {
									if (item.customerReference == reference) {
										message = `KYC GetCustomerReference: ${item.customerReference} - Created`;
									}
								}
							}
						} else {
							message = `KYC GetCustomerReference: ${reference} - AlreadyExist`;
						}
						console.info(message);

						const cr = await KYC.Kyc_CheckCustomer(kc, reference);
						if (cr && cr.Datas && cr.Datas.length > 0) {
							for (const item of cr.Datas) {
								if (item.riskState == "NO_RISKS_FOUND") {
									kycResult = true;
									record.kycUtilityBill = 1;
								}
								message = `KYC CheckCustomerRisk: ${item.customerReference} - ${item.riskState}`;
							}
						} else {
							message = `KYC CheckCustomerRisk: ${reference} - NO_DATA`;
						}
						console.info(message);

						if (kycResult) {
							//KYC Spider üzerinden Addresscheck işlemi artık yapılmayacak.
							// const car = await KYC.Kyc_CheckCustomerAddress(kc, reference);
							// if (car && car.Datas && car.Datas.length > 0) {
							// 	for (const item of car.Datas) {
							// 		if (item.state == "CHECKED") {
							// 			if (item.combinations[0].result == "MATCH") {
							// 				kycResult = true;
							// 				record.kycAddressValidation = 1;
							// 			}
							// 			message = `KYC CheckCustomerAddress: ${item.customerReference} - ${item.combinations[0].result}`;
							// 		}
							// 	}
							// } else {
							// 	message = `KYC CheckCustomerAddress: ${reference} - NO_DATA`;
							// }
							// console.info(message);

							await record.save()
						}
					}
				}
			}

			console.log(message);
		}
	}

	//Trader WEB API

	async subSyncTraderWebApi(institute, manipulatedCustomers) {
		const authToken = await this.subGetTokenFromTraderWebApi(institute)
		const accounts = await this.subGetAccountsFromTraderWebApi(institute, authToken)

		//filteredAccounts = accounts.filter(x => x.clientInfo.clientId == "215834")
		for (const account of accounts) {
			//let record = await Record.findBy('email', account.clientInfo.email);
			let record = await Customer.findBy('login', account.clientInfo.clientId);

			const fullName = account.clientInfo.name.replace(",", " ").replace("  ", " ").split(" ");

			if (account.clientInfo.name == null || account.clientInfo.name == '') {
				let nullName = fullName
			}

			account.name = fullName[0]
			account.surname = fullName[1]

			//getCustomerDeposits
			let customerDeposit = 0
			let customerKycForce = 0
			let customerInstitute = 0 //CHF olmayan diğer hesap türleri

			if (account.clientGroup == 'demoEQFchf-B2') {
				customerInstitute = 5 //Forex724 Demo
			}

			if (account.clientGroup == 'demoWTGchf-B1') {
				customerInstitute = 7 //FXCentrum Demo
			}

			if (account.clientGroup == 'realEQFchf-B4'
				|| account.clientGroup == 'realEQFchf-B5'
				|| account.clientGroup == 'realEQFchf-B6'
			) {
				customerInstitute = 4 //Forex724 Live
			}

			if (account.clientGroup == 'realWTGchf-A1'
				|| account.clientGroup == 'realWTGchf-A2'
				|| account.clientGroup == 'realWTGchf-A3'
			) {
				customerInstitute = 6 //FXCentrum Live
			}

			const ledgerTypes = [4]; //Types: Commission=1 Swap=2 ClosedPosition=3 Deposit=4 Withdrawal=5 CreditIn=6 CreditOut=7 AgentCommission=8
			const beginDate = new Date(2020, 0, 1, 1, 0, 0); // Yıl, ayIndex, gün, saat, dakika, saniye
			const rangeStart = Math.floor(beginDate.getTime()); // Unix zaman damgası (mili saniye cinsinden)
			const rangeEnd = Math.floor(Date.now());
			const clientId = Number(account.clientInfo.clientId)

			const deposits = await this.subGetBalanceFromTraderWebApi(institute, authToken, rangeStart, rangeEnd, ledgerTypes, [clientId])

			for (const item of deposits) {
				customerDeposit += item.amount / 100;
			}

			//tüm depositlerin toplamı 2500 den fazlaysa KYC ye girmeli
			if (customerDeposit >= 2500) {
				customerKycForce = 1
			}

			if (record == null) {
				await Customer.create({
					name: account.name,
					surname: account.surname,
					email: account.clientInfo.email,
					phone: account.clientInfo.phone,
					avenue: account.clientInfo.address,
					login: account.clientInfo.clientId,
					leverage: account.clientInfo.leverageRatioPercent,
					city: account.clientInfo.city,
					country: account.clientInfo.country,
					nickname: account.clientInfo.email,
					terminalCode: "API_Sync",
					gender: "U",
					//dateofBirth: Date(account.clientInfo.dateofBirth),
					financeinstitute: customerInstitute,
					accountType: 0,
					bonusShopPassword: 0,
					invoiceEmailStatus: 0,
					balance: Number(account.balance) / 100,
					deposit: customerDeposit,
					groupName: account.clientGroup,
					kycForce: customerKycForce,
					equity: Number(account.equity) / 100,
					withdrawalCapacity: (Number(account.equity) - Number(account.credit)) / 100,
					agentLogin: account.clientInfo.agentClientId || 0,
				})

				//yeni açılan hesaplara ilk seferinde SMS gönderilecek
				const message = `Sehr geehrte Kundin, sehr geehrter Kunde, wir möchten uns herzlich bei Ihnen dafür bedanken, dass Sie ein Handelskonto bei einem unserer Partner eröffnet haben. Um sicherzustellen, dass Sie optimal von unseren Schulungsangeboten profitieren können, wird sich unser Support-Team in Kürze telefonisch mit Ihnen in Verbindung setzen. Bitte nehmen Sie den Anruf entgegen und markieren Sie ihn nicht als Spam. Wir danken Ihnen für Ihr Verständnis und freuen uns darauf, Sie bei Ihrem Handelsweg zu unterstützen. Mit freundlichen Grüssen, TRADECENTER`

				if (setting.test_mode != 1) {
					SMS.sendSMS(setting.toJSON(), account.clientInfo.phone, 'Alternate', message);
				}

			} else {
				record.name = account.name
				record.surname = account.surname
				record.email = account.clientInfo.email
				record.phone = account.clientInfo.phone
				record.avenue = account.clientInfo.address
				//record.gender = "M"
				//record.dateofBirth = Date(account.clientInfo.dateofBirth)
				record.financeinstitute = customerInstitute
				record.login = account.clientInfo.clientId
				record.leverage = account.clientInfo.leverageRatioPercent
				record.city = account.clientInfo.city
				record.country = account.clientInfo.country
				record.terminalCode = "API_Sync"
				record.balance = Number(account.balance) / 100
				record.deposit = customerDeposit
				record.groupName = account.clientGroup
				record.kycForce = record.kycForce == 1 ? 1 : customerKycForce
				record.equity = Number(account.equity) / 100
				record.withdrawalCapacity = (Number(account.equity) - Number(account.credit)) / 100
				record.agentLogin = account.clientInfo.agentClientId || 0

				await record.save()
			}

			account.totalDeposit = customerDeposit
			account.kycForce = customerKycForce
			account.depositLimit = record.depositLimit || 0
			account.withdrawalLimit = record.withdrawalLimit || 0

			account.kycUtilityBill = record.kycUtilityBill || 0
			account.kycAddressValidation = record.kycAddressValidation || 0
			account.kycIdentification = record.kycIdentification || 0
			account.kycChatbot = record.kycChatbot || 0

			manipulatedCustomers.push(account)
		}

		return accounts
	}

	async subGetTokenFromTraderWebApi(institute) {
		try {
			const response = await axios.post(`${institute.restApiBaseUrl}/v1/register/register`,
				{
					managerID: institute.restApiUser,
					password: institute.restApiPassword
				}
			)

			if (!response || !response.data || !response.data.token) {
				return undefined
			}

			return response.data.token

		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	async subGetAccountsFromTraderWebApi(institute, authToken) {
		try {
			const response = await axios.post(`${institute.restApiBaseUrl}/v1/account/getAllForManager`,
				{
					auth: {
						managerID: institute.restApiUser,
						token: authToken
					}
				}
			)

			if (!response || !response.data || !response.data.accountInfo) {
				return undefined
			}

			return response.data.accountInfo
		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	async subGetBalanceFromTraderWebApi(institute, authToken, rangeStart, rangeEnd, ledgerTypes, clientIds) {
		try {
			const response = await axios.post(`${institute.restApiBaseUrl}/v1/ledger/getEntries`,
				{
					auth: {
						managerID: institute.restApiUser,
						token: authToken
					},
					rangeStart: rangeStart,
					rangeEnd: rangeEnd,
					clientIds: clientIds,
					ledgerTypes: ledgerTypes
				}
			)

			if (!response || !response.data || !response.data.ledgerEntry) {
				return undefined
			}

			return response.data.ledgerEntry

		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	async subSetDepositFromTraderApi(institute, authToken, youLogin, amount, comment) {
		try {
			const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/depositMoney`,
				{
					auth: {
						managerID: institute.restApiUser,
						token: authToken
					},
					comment: comment,
					amount: amount * 100,
					clientId: youLogin,
					additionalType: 0
				}
			)

			if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
				return true
			}

			console.log(response)

		} catch (error) {
			console.log(error)
		}

		return false
	}

	async subSetCreditInFromTraderApi(institute, authToken, customerLogin, amount, comment) {
		try {
			const response = await axios.post(`${institute.restApiBaseUrl}/v1/balance/creditIn`,
				{
					auth: {
						managerID: institute.restApiUser,
						token: authToken
					},
					comment: comment,
					amount: amount * 100,
					clientId: customerLogin,
					additionalType: 0
				}
			)

			if (response && response.data && response.data.status && response.data.status == 'OPERATION_SUCCESS') {
				return true
			}

			console.log(response)
		} catch (error) {
			console.log(error)
		}

		return false
	}

	//Kobikom CRM

	async subSyncKobikomCrm(institute, manipulatedCustomers, accounts) {
		const webApiToken = await this.subGetTokenFromKobikomCrm()

		for (const customer of manipulatedCustomers) {
			if (customer.clientInfo.agentClientId != undefined && customer.clientInfo.agentClientId != '') {

				const agent = accounts.find(x => x.clientId == customer.clientInfo.agentClientId)

				const customerData = {
					VerificationStatus: "Verified",
					Role: "USER",
					LeadStatus: "Active client",
					Name: customer.name,
					Surname: customer.surname,
					PhoneNumber: customer.clientInfo.phone,
					IBAccount: `${agent.clientInfo.email} (${agent.clientId})`,//"<EMAIL> (106606)",
					Email: customer.clientInfo.email,
					Branch: institute.crmBranchTitle, //"CHF-Forex 724" veya "CHF-FXCentrum"
					State: customer.clientInfo.province,
					PostCode: customer.clientInfo.zipCode,
					Country: customer.clientInfo.country,
					City: customer.clientInfo.city,
					Address: customer.clientInfo.address,
				}
				const updateCustomer = await this.subUpdateCustomerFromKobikomCrm(webApiToken, customerData)

				let clientGroupName = customer.clientGroup
				clientGroupName = Common.Basic.customerGroupNames.filter(group => group.id == customer.clientGroup).map(group => group.title)[0]

				const tradingAccountData = {
					Email: customer.clientInfo.email,
					LoginId: customer.clientInfo.clientId,
					AccountType: clientGroupName,
					Leverage: customer.clientInfo.leverageRatioPercent,
					Balance: Number(customer.balance) / 100,
				}
				const updateTradingData = await this.subUpdateTradingDataFromKobikomCrm(webApiToken, tradingAccountData)
			}
		}
	}

	async subGetTokenFromKobikomCrm() {
		try {
			const formData = {
				username: process.env.CRMAPI_USER,
				password: process.env.CRMAPI_PASSWORD,
				grant_type: "password"
			};

			const formBody = querystring.stringify(formData);
			const response = await axios.post('http://equityfinanscrm.kobikom.com.tr/token',
				formBody, {
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded'
				}
			})

			if (!response || !response.data || !response.data.access_token) {
				return undefined
			}

			return response.data.access_token

		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	async subUpdateCustomerFromKobikomCrm(webApiToken, customerData) {
		try {
			const response = await axios.post('http://equityfinanscrm.kobikom.com.tr/api/web/updatedata',
				{
					ApiKey: "243DBC975D6C7",
					Table: "Clients",
					Data: customerData
				},
				{
					headers: {
						'Authorization': `Bearer ${webApiToken}`
					}
				}
			)

			if (!response || !response.data || !response.data.Data) {
				return undefined
			}

			return response.data

		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	async subUpdateTradingDataFromKobikomCrm(webApiToken, tradingAccountData) {
		try {
			const response = await axios.post('http://equityfinanscrm.kobikom.com.tr/api/web/updatedata',
				{
					ApiKey: "243DBC975D6C7",
					Table: "ClientsTradingAccount",
					Data: tradingAccountData
				},
				{
					headers: {
						'Authorization': `Bearer ${webApiToken}`
					}
				}
			)

			if (!response || !response.data || !response.data.Data) {
				return undefined
			}

			return response.data

		} catch (error) {
			console.log(error)
		}

		return undefined
	}

	//Suite CRM on crm.tradecenter24.com

	async subSyncTradecenterCrm(institute, manipulatedCustomers, accounts) {
		const accessToken = await this.subGetTokenFromSuiteCrm()

		for (const customer of manipulatedCustomers) {
			if (customer.clientInfo.agentClientId != undefined && customer.clientInfo.agentClientId != '') {
				const agent = accounts.find(x => x.clientId == customer.clientInfo.agentClientId)

				const customerData = {
					email1: customer.clientInfo.email,
					name: customer.surname + ' ' + customer.name,
					phone_office: customer.clientInfo.phone,
					shipping_address_street: customer.clientInfo.address,
					shipping_address_city: customer.clientInfo.city,
					account_type: "Private",
					interessiertes_produkt_c: "CFD",
					status_c: "New",
					branch_c: institute.crmBranchTitle, //"CHF-Forex 724" veya "CHF-FXCentrum"
					kyc_risk_check_c: customer.kycUtilityBill == 1 ? "YES" : "NO",
					kyc_address_validation_c: customer.kycAddressValidation == 1 ? "YES" : "NO",
					kyc_identification_c: customer.kycIdentification == 1 ? "YES" : "NO",
					kyc_beneficial_owners_c: customer.kycChatbot == 1 ? "YES" : "NO",
				}

				let customerId = await this.subGetCustomerFromSuiteCrm(accessToken, customer.clientInfo.email)
				if (!customerId) {
					customerId = await this.subCreateCustomerToSuiteCrm(accessToken, customerData)
				} else {
					const updateCustomer = await this.subUpdateCustomerToSuiteCrm(accessToken, customerData, customerId)
					if (!updateCustomer) {
						console.log('Error updating customer:', error.message);
					}
				}

				let clientGroupName = customer.clientGroup
				clientGroupName = Common.Basic.customerGroupNames.filter(group => group.id == customer.clientGroup).map(group => group.title)[0]

				const tradingAccountData = {
					trader_id: customer.clientInfo.clientId,
					name: customer.surname + ' ' + customer.name,
					email: customer.clientInfo.email,
					nickname: customer.clientInfo.email,
					institute: institute.crmBranchTitle,
					leverage: customer.clientInfo.leverageRatioPercent,
					date_entered: customer.clientInfo.registrationDate,
					balance: Number(customer.balance) / 100,
					equity: Number(customer.equity) / 100,
					total_deposit: customer.totalDeposit,
					withdrawal_capacity: (Number(customer.equity) - Number(customer.credit)) / 100,
					individual_deposit_limit: customer.depositLimit,
					individual_withdrawal_limit: customer.withdrawalLimit,
					accounts_tam_trading_accounts_1accounts_ida: customerId,
					ibaccount_c: `${agent.clientInfo.email} (${agent.clientId})`,//"<EMAIL> (106606)",
					accounttype_c: clientGroupName,
				}

				let accountId = await this.subGetAccountFromSuiteCrm(accessToken, customer.clientInfo.clientId)
				if (!accountId) {
					accountId = await this.subCreateAccountToSuiteCrm(accessToken, tradingAccountData)

					const linkAccount = await this.subLinkAccountWithCustomerOnSuiteCrm(accessToken, customerId, accountId)
					if (!linkAccount) {
						console.log('Error linking account:', error.message);
					}
				} else {
					const updateTradingData = await this.subUpdateAccountToSuiteCrm(accessToken, tradingAccountData, accountId)
					if (!updateTradingData) {
						console.log('Error updating trading account:', error.message);
					}
				}

				console.log('Customer and trading account synced:', customer.clientInfo.clientId + ' - ' + customer.clientInfo.email);
			}
		}
	}

	async subGetTokenFromSuiteCrm() {
		try {
			const tokenUrl = 'https://crm.tradecenter24.com/legacy/Api/access_token'
			const credentials = {
				grant_type: 'client_credentials',
				client_id: '********-8048-ae0f-a700-6878f65499a7', //process.env.CRMAPI_USER, //'********-8048-ae0f-a700-6878f65499a7',
				client_secret: '%qARr(22z"03', //process.env.CRMAPI_PASSWORD, //'%qARr(22z"03'
			}
			const response = await axios.post(tokenUrl,
				new URLSearchParams(credentials).toString(),
				{
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded'
					}
				}
			);

			return response.data.access_token;
		} catch (error) {
			console.error('Error getting access token:', error.message);
			throw error;
		}
	}

	async subGetCustomerFromSuiteCrm(accessToken, email) {
		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.get(`${baseUrl}/Accounts?filter[email1][eq]=${email}`, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});

			if (!response.data || !response.data.data || response.data.data.length == 0) {
				return undefined
			}

			//interessiertes_produkt_c=CFD
			const onlyCfd = response.data.data.filter(x => x.attributes.interessiertes_produkt_c == "CFD")
			if (!onlyCfd || onlyCfd.length == 0) {
				return undefined
			}

			return onlyCfd[0].id;
		} catch (error) {
			console.error('Error getting customer:', error.message);
		}

		return undefined
	}

	async subCreateCustomerToSuiteCrm(accessToken, customerData) {
		const accountData = {
			data: {
				type: "Accounts",
				attributes: customerData
			}
		};

		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.post(baseUrl, accountData, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});
			return response.data.data.id;
		} catch (error) {
			console.error('Error creating account:', error.message);
		}

		return undefined
	}

	async subUpdateCustomerToSuiteCrm(accessToken, customerData, customerId) {
		const accountData = {
			data: {
				type: "Accounts",
				id: customerId,
				attributes: customerData
			}
		};

		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.patch(baseUrl, accountData, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});

			if (!response || !response.data || !response.data.data) {
				return undefined
			}

			return response.data.data.id;
		} catch (error) {
			console.error('Error creating account:', error.message);
		}

		return undefined
	}

	async subGetAccountFromSuiteCrm(accessToken, traderId) {
		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.get(`${baseUrl}/TAM_Trading_Accounts?filter[trader_id][eq]=${traderId}`, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});

			if (!response || !response.data || !response.data.data || response.data.data.length == 0) {
				return undefined
			}

			return response.data.data[0].id;
		} catch (error) {
			console.error('Error getting account:', error.message);
		}

		return undefined
	}

	async subCreateAccountToSuiteCrm(accessToken, accountData) {
		const tradingData = {
			data: {
				type: "TAM_Trading_Accounts",
				attributes: accountData
			}
		};

		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.post(baseUrl, tradingData, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});
			return response.data.data.id;
		} catch (error) {
			console.error('Error creating trading account:', error.message);
		}

		return undefined
	}

	async subUpdateAccountToSuiteCrm(accessToken, accountData, accountId) {
		const tradingData = {
			data: {
				type: "TAM_Trading_Accounts",
				id: accountId,
				attributes: accountData
			}
		};

		try {
			const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
			const response = await axios.patch(baseUrl, tradingData, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});

			if (!response || !response.data || !response.data.data) {
				return undefined
			}

			return response.data.data.id;
		} catch (error) {
			console.error('Error creating trading account:', error.message);
		}

		return undefined
	}

	async subLinkAccountWithCustomerOnSuiteCrm(accessToken, customerId, accountId) {
		const baseUrl = 'https://crm.tradecenter24.com/legacy/Api/V8/module'
		const relationKey = 'accounts_tam_trading_accounts_1'
		const requestUrl = `${baseUrl}/Accounts/${customerId}/relationships/${relationKey}`;
		const relationData = {
			data: {
				type: "TAM_Trading_Accounts",
				id: accountId
			}
		};

		try {
			const response = await axios.post(requestUrl, relationData, {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/vnd.api+json',
					'Accept': 'application/vnd.api+json'
				}
			});

			if (!response || !response.data || !response.data.meta) {
				return undefined
			}

			return response.data.meta.message;
		} catch (error) {
			console.error('Error linking accounts:', error.message);
		}

		return undefined
	}

}

module.exports = ScheduleIt