'use strict'

//const { HttpException } = require('@adonisjs/http-exceptions');

class OnlyUrl {
  async handle({ request, response }, next) {
    // sadece belirli urllerde gelen istekleri kabul et
    const allowedOrigin = 'https://quiz.tradecenter24.com';

    const requestOrigin = request.headers.get('Origin');

    if (requestOrigin !== allowedOrigin) {
      return response.status(403).send('Invalid request origin')
      //throw new HttpException('Invalid request origin', 403);
    }

    await next();
  }
}

module.exports = OnlyUrl
