'use strict'

class TerminalCreate {

	get rules() {
		return {
			terminalCode: 'required|unique:terminal',
			devicePort: 'required',
			lang: 'required|min:2',
		}
	}

	get messages() {
		return {
			'terminalCode.required': 'This field is required',
			'terminalCode.unique': 'This is already exists',
			'devicePort.required': 'This field is required',
			'lang.required': 'This field is required',
			'lang.min': 'This field must be at least 2 characters',
			//'required': 'Woah now, {{ field }} is required.', sadece böylede kullanılabilir. field kısmı rulesda yazanlardan gelir.
			//'unique': 'Wait a second, the {{ field }} already exists'
		}
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ newModalShow: '1' })
		return this.ctx.response.redirect('back');
	}

}

module.exports = TerminalCreate