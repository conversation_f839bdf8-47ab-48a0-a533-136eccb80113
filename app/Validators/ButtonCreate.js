'use strict'

class ButtonCreate {

	get rules() {
		return {
			caption: 'required',
			menuOrder: 'required|number',
			url: 'required|string',
			image: 'required',
		}
	}

	get messages() {
		return {
			//'caption.required': 'This field is required',
			'required': 'This field is required. {{ field }}',
			'number': 'This field is number. {{ field }}',
			'string': 'This field is string. {{ field }}',
		}
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ newModalShow: '1' })
		return this.ctx.response.redirect('back');
	}

}

module.exports = ButtonCreate