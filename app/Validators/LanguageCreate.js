'use strict'

class LanguageCreate {

    get rules() {
        return {
            key: 'required|unique:language',
        }
    }

    get messages() {
        return {
            'required': 'This field is required',
        }
    }

    async fails(error) {
        this.ctx.session.withErrors(error).flashAll();
        this.ctx.session.flash({ newModalShow: '1' })
        return this.ctx.response.redirect('back');
    }

}

module.exports = LanguageCreate