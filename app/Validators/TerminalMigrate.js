'use strict'

class TerminalMigrate {

	get rules() {
		return {
			targetTerminal: 'required',
		}
	}

	get messages() {
		return {
			'targetTerminal.required': 'This field is required',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ migrateModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = TerminalMigrate