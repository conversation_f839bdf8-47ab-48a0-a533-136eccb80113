'use strict'

class Button {

	get rules() {
		return {
			caption: 'required',
			menuOrder: 'required|number',
			url: 'required|string',
			image: 'required',
		}
	}

	get messages() {
		return {
			//'caption.required': 'This field is required',
			'required': 'This field is required. {{ field }}',
			'number': 'This field is number. {{ field }}',
			'string': 'This field is string. {{ field }}',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = Button