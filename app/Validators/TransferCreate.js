'use strict'

class TransferCreate {

	get rules() {
		return {
			login: 'required',
			amount: 'required',
			financeInstitute: 'required',
			terminalCode: 'required',
			bId: 'required',
		}
	}

	get messages() {
		return {
			'required': 'This field is required',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ newModalShow: '1' })
		return this.ctx.response.redirect('back');
	}

}

module.exports = TransferCreate