'use strict'

class Invoice {

	get rules() {
		return {
			date: 'required',
			requestedCredit: 'required',
			previousCredit: 'required',
		}
	}

	get messages() {
		return {
			'date.required': 'This field is required',
			'requestedCredit.required': 'This field is required',
			'previousCredit.required': 'This field is required',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = Invoice
