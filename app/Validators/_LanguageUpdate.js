'use strict'

const Validator = use('Validator')
const Database = use('Database')

const escapeFn = async (data, field, message, args, get) => {
    const value = get(data, field)
    if (!value) {
        return
    }

    if (value.indexOf("'") > 0) {
        throw message
    }
}

Validator.extend('escape', escapeFn)

class Language {

    get rules() {
        return {
            key: 'required',
            en: 'escape',
        }
    }

    get messages() {
        return {
            'required': 'This field is required',
            'escape': '<PERSON><PERSON><PERSON> write rightly'
        }
    }

    async fails(error) {
        this.ctx.session.withErrors(error).flashAll();
        this.ctx.session.flash({ newModalShow: '1' })
        return this.ctx.response.redirect('back');
    }

}

module.exports = Language