'use strict'

class ForexMtc {

	get rules() {
		return {
			login: 'required',
			amount: 'required',
			date: 'required',
		}
	}

	get messages() {
		return {
			'login.required': 'This field is required',
			'amount.required': 'This field is required',
			'date.required': 'This field is required',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = ForexMtc
