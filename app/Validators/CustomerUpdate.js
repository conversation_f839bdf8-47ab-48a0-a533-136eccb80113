'use strict'

class Customer {

	get rules() {
		return {
			name: 'required',
			surname: 'required',
			email: 'required|email',
			phone: 'required',
		}
	}

	get messages() {
		return {
			'required': 'This field is required',
			'email': 'This field must be email format',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = Customer