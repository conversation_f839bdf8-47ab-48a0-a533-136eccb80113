'use strict'

class TerminalUpdate {

	get rules() {
		return {
			terminalCode: 'required',
			devicePort: 'required',
			lang: 'required|min:2',
		}
	}

	get messages() {
		return {
			'terminalCode.required': 'This field is required',
			'devicePort.required': 'This field is required',
			'lang.required': 'This field is required',
			'lang.min': 'This field must be at least 2 characters',
			//'required': 'Woah now, {{ field }} is required.', sadece böylede kullanılabilir. field kısmı rulesda yazanlardan gelir.
			//'unique': 'Wait a second, the {{ field }} already exists'
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = TerminalUpdate