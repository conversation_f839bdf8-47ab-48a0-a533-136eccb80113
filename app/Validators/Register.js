'use strict'

class Register {

	get rules() {
		return {
			email: 'required|email|unique:users',
			pass: 'required|min:6|confirmed'
		}
	}

	get messages() {
		return {
			'email.required': 'This field is required',
			'email.email': 'Enter a valid email address',
			'email.unique': 'This email already exists',
			'pass.required': 'This field is required',
			'pass.min': 'This field must be at least 6 characters',
			'pass.confirmed': 'The password fields do not match', //catched by name attribute from input element 'pass_confirmation'
		}
	}

}

module.exports = Register