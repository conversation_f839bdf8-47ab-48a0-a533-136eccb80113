'use strict'

class ForexMtcCreate {

	get rules() {
		return {
			login: 'required',
			amount: 'required',
			date: 'required',
		}
	}

	get messages() {
		return {
			'login.required': 'This field is required',
			'amount.required': 'This field is required',
			'date.required': 'This field is required',
		}
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ newModalShow: '1' })
		return this.ctx.response.redirect('back');
	}

}

module.exports = ForexMtcCreate
