'use strict'

class Setting {

	get rules() {
		return {
			sms_url: 'required',
			sms_user: 'required',
			sms_pass: 'required',
			sms_originator: 'required',
			faq_url: 'required',
			money_wait_time: 'required',
			general_wait_time: 'required',
			ftp_host: 'required',
			ftp_port: 'required',
			ftp_user: 'required',
			ftp_pass: 'required',
			ftp_working_dir: 'required',
			test_mode: 'required',
			version: 'required',
			updateVersion: 'required',
		}
	}

	get messages() {
		return {
			'required': 'This field is required',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ editModalShow: '1', id: this.data.id })
		return this.ctx.response.redirect('back');
	}

}

module.exports = Setting