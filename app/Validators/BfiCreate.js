'use strict'

class BfiCreate {

	get rules() {
		return {
			businessName: 'required',
			companyName: 'required',
			contactName: 'required',
			contactEmail: 'email',
		}
	}

	get messages() {
		return {
			'required': 'This field is required',
			'email': 'This field must be email format',
		}
	}

	get data() {
		const requestBody = this.ctx.request.all()
		const id = this.ctx.params.id
		return Object.assign({}, requestBody, { id })
	}

	async fails(error) {
		this.ctx.session.withErrors(error).flashAll();
		this.ctx.session.flash({ newModalShow: '1' })
		return this.ctx.response.redirect('back');
	}

}

module.exports = BfiCreate