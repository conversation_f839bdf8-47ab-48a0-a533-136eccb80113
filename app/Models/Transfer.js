'use strict'

/** @type {typeof import('@adonisjs/lucid/src/Lucid/Model')} */
const Model = use('Model')

class Transfer extends Model {
    static get table() {
        return 'transfer'
    }

    static get createdAtColumn() {
        return null;
    }

    static get updatedAtColumn() {
        return null;
    }

    static get dates() {
        return super.dates.concat(["created_date"]);
    }

    static castDates(field, value) {
        if (field === 'created_date') {
            return value ? value.format("DD.MM.YYYY HH:mm:ss") : value;
        } else {
            return value ? super.formatDates(field, value) : value
        }
    }

    financeInstitutes() {
        return this.hasOne('App/Models/Institute')
    }
}

module.exports = Transfer
