'use strict'

/** @type {typeof import('@adonisjs/lucid/src/Lucid/Model')} */
const Model = use('Model')

class Customer extends Model {
    static get table() {
        return 'person'
    }

    static get createdAtColumn() {
        return null
    }

    static get updatedAtColumn() {
        return null
    }

    /*
    //hangi alanları tarih olarak değerlendirecekse buraya yazıyoruz sonra aşağıdaki tarihlerle alakalı fonksiyonlar buna göre çalışıyor.
    static get dates () {
        return super.dates.concat(['created_date'])
    }

    //database ye kaydederken bunu referans alıyor
    static formatDates(field, value) {
        if (field === 'created_date') {
            return value.format('YYYY-MM-DD HH:mm:ss')
        }
        return super.formatDates(field, value)
    }

    //database den okurken bunu referans alıyor
    //bu kısımdaki value değeri moment.js den miras alındığından dolayı ona ait fonksiyonlar kullanılabilir.(https://momentjs.com/)
    static castDates(field, value) {
        if (field === 'created_date') {
            return `${value.format("MM.DD.YYYY HH:mm")}` //bu kısma yazılacak formatlama moment.js in sitesindeki doc kısmından bakılabilir. 
            //return `${value.toString().substring(4, 24)}`
        }
        return super.formatDates(field, value)
    }
    */

    /*

    static get fields() {
        return {
            kanton: { label: 'Santon', type: 'switch', listable: false },
            body: { type: 'html', },
        }
    }
    */

}

module.exports = Customer
