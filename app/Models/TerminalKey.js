'use strict'

/** @type {typeof import('@adonisjs/lucid/src/Lucid/Model')} */
const Model = use('Model')

class TerminalKey extends Model {
    static get table() {
        return 'terminalkey'
    }

    static get createdAtColumn() {
        return null;
    }

    static get updatedAtColumn() {
        return null;
    }

    static castDates(field, value) {
        if (field === 'created_at') {
            //return `${value.fromNow(true)} old`
            return value ? value.format("DD.MM.YYYY HH:mm") : value;
        } else {
            return value ? super.formatDates(field, value) : value
        }
    }

    static formatDates(field, value) {
        if (field === 'created_at') {
            return value.format('YYYY-MM-DD HH:mm:ss')
        }
        return super.formatDates(field, value)
    }
}

module.exports = TerminalKey
