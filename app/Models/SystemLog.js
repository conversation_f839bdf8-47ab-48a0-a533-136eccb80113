'use strict'

/** @type {typeof import('@adonisjs/lucid/src/Lucid/Model')} */
const Model = use('Model')

class SystemLog extends Model {
    static get table() {
        return 'systemlog'
    }

    static get createdAtColumn() {
        return null
    }

    static get updatedAtColumn() {
        return null
    }

    //hangi alanları tarih olarak değerlendirecekse buraya yazıyoruz sonra aşağıdaki tarihlerle alakalı fonksiyonlar buna göre çalışıyor.
    static get dates() {
        return super.dates.concat(['created_date'])
    }

    //database den okurken bunu referans alıyor
    //bu kısımdaki value değeri moment.js den miras alındığından dolayı ona ait fonksiyonlar kullanılabilir.(https://momentjs.com/)
    /*
    static castDates(field, value) {
        if (field === 'created_date') {
            return `${value.format("DD.MM.YYYY HH:mm")}` //bu kısma yazılacak formatlama moment.js in sitesindeki doc kısmından bakılabilir. 
            //return `${value.toString().substring(4, 24)}`
        }
        return super.formatDates(field, value)
    }
    */

    static castDates(field, value) {
        if (field === 'created_date') {
            //return `${value.fromNow(true)} old`
            return value ? value.format("DD.MM.YYYY HH:mm") : value;
        } else {
            return value ? super.formatDates(field, value) : value
        }
    }

    static formatDates(field, value) {
        if (field === 'created_date') {
            return value.format('YYYY-MM-DD HH:mm:ss')
        }
        return super.formatDates(field, value)
    }

}

module.exports = SystemLog
